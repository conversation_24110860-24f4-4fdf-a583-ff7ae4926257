const { ethers } = require("hardhat");
const fs = require("fs");
const path = require("path");

async function main() {
  console.log("🚀 开始部署 Web3 生态系统合约...\n");

  const [deployer] = await ethers.getSigners();
  console.log("部署账户:", deployer.address);
  console.log("账户余额:", ethers.formatEther(await deployer.provider.getBalance(deployer.address)), "ETH\n");

  const deployedContracts = {};

  try {
    // 1. 部署 SimpleToken (ERC-20)
    console.log("📄 部署 SimpleToken...");
    const SimpleToken = await ethers.getContractFactory("SimpleToken");
    const simpleToken = await SimpleToken.deploy(
      "Web3 Ecosystem Token",
      "W3T",
      ethers.parseEther("1000000"), // 100万代币初始供应
      deployer.address
    );
    await simpleToken.waitForDeployment();
    deployedContracts.SimpleToken = await simpleToken.getAddress();
    console.log("✅ SimpleToken 部署成功:", deployedContracts.SimpleToken);

    // 2. 部署 Web3NFT (ERC-721)
    console.log("\n🎨 部署 Web3NFT...");
    const Web3NFT = await ethers.getContractFactory("Web3NFT");
    const web3NFT = await Web3NFT.deploy(
      "Web3 Ecosystem NFT",
      "W3NFT",
      "https://api.web3ecosystem.com/metadata/", // 基础 URI
      deployer.address,
      deployer.address // 版税接收者
    );
    await web3NFT.waitForDeployment();
    deployedContracts.Web3NFT = await web3NFT.getAddress();
    console.log("✅ Web3NFT 部署成功:", deployedContracts.Web3NFT);

    // 3. 部署 MultiSigWallet
    console.log("\n🔐 部署 MultiSigWallet...");
    const MultiSigWallet = await ethers.getContractFactory("MultiSigWallet");
    const multiSigWallet = await MultiSigWallet.deploy(
      [deployer.address], // 初始签名者
      1 // 需要1个签名
    );
    await multiSigWallet.waitForDeployment();
    deployedContracts.MultiSigWallet = await multiSigWallet.getAddress();
    console.log("✅ MultiSigWallet 部署成功:", deployedContracts.MultiSigWallet);

    // 4. 部署 SimpleDAO
    console.log("\n🏛️ 部署 SimpleDAO...");
    const SimpleDAO = await ethers.getContractFactory("SimpleDAO");
    const simpleDAO = await SimpleDAO.deploy(
      deployedContracts.SimpleToken, // 治理代币
      deployer.address // 所有者
    );
    await simpleDAO.waitForDeployment();
    deployedContracts.SimpleDAO = await simpleDAO.getAddress();
    console.log("✅ SimpleDAO 部署成功:", deployedContracts.SimpleDAO);

    // 5. 部署 SimpleAMM
    console.log("\n💱 部署 SimpleAMM...");
    
    // 首先部署一个测试代币作为配对
    console.log("   部署测试代币 USDT...");
    const TestUSDT = await ethers.getContractFactory("SimpleToken");
    const testUSDT = await TestUSDT.deploy(
      "Test USDT",
      "USDT",
      ethers.parseEther("1000000"),
      deployer.address
    );
    await testUSDT.waitForDeployment();
    deployedContracts.TestUSDT = await testUSDT.getAddress();
    console.log("   ✅ TestUSDT 部署成功:", deployedContracts.TestUSDT);

    const SimpleAMM = await ethers.getContractFactory("SimpleAMM");
    const simpleAMM = await SimpleAMM.deploy(
      deployedContracts.SimpleToken,
      deployedContracts.TestUSDT,
      "W3T-USDT LP Token",
      "W3T-USDT-LP",
      deployer.address
    );
    await simpleAMM.waitForDeployment();
    deployedContracts.SimpleAMM = await simpleAMM.getAddress();
    console.log("✅ SimpleAMM 部署成功:", deployedContracts.SimpleAMM);

    // 6. 部署 CrossChainBridge
    console.log("\n🌉 部署 CrossChainBridge...");
    const CrossChainBridge = await ethers.getContractFactory("CrossChainBridge");
    const crossChainBridge = await CrossChainBridge.deploy(
      [deployer.address], // 验证者
      1, // 需要1个签名
      deployer.address
    );
    await crossChainBridge.waitForDeployment();
    deployedContracts.CrossChainBridge = await crossChainBridge.getAddress();
    console.log("✅ CrossChainBridge 部署成功:", deployedContracts.CrossChainBridge);

    // 7. 配置合约
    console.log("\n⚙️ 配置合约...");

    // 配置 NFT 铸造阶段为公开
    console.log("   设置 NFT 为公开铸造...");
    await web3NFT.setMintPhase(2); // PUBLIC = 2

    // 配置跨链桥支持的代币和链
    console.log("   配置跨链桥...");
    await crossChainBridge.addSupportedToken(deployedContracts.SimpleToken);
    await crossChainBridge.addSupportedToken(deployedContracts.TestUSDT);
    
    // 添加一些测试链 ID
    await crossChainBridge.addSupportedChain(137); // Polygon
    await crossChainBridge.addSupportedChain(56);  // BSC

    // 设置桥接手续费
    await crossChainBridge.setBridgeFee(137, ethers.parseEther("0.01")); // 0.01 ETH
    await crossChainBridge.setBridgeFee(56, ethers.parseEther("0.005"));  // 0.005 ETH

    console.log("✅ 合约配置完成");

    // 9. 保存部署信息
    console.log("\n💾 保存部署信息...");
    const deploymentInfo = {
      network: await ethers.provider.getNetwork(),
      deployer: deployer.address,
      timestamp: new Date().toISOString(),
      contracts: deployedContracts,
      gasUsed: {
        // 这里可以记录每个合约的 gas 使用量
      }
    };

    const deploymentsDir = path.join(__dirname, "../deployments");
    if (!fs.existsSync(deploymentsDir)) {
      fs.mkdirSync(deploymentsDir, { recursive: true });
    }

    const networkName = (await ethers.provider.getNetwork()).name || "unknown";
    const filename = `${networkName}-${Date.now()}.json`;
    const filepath = path.join(deploymentsDir, filename);

    fs.writeFileSync(filepath, JSON.stringify(deploymentInfo, null, 2));
    console.log("✅ 部署信息已保存到:", filepath);

    // 10. 生成前端配置文件
    console.log("\n🔧 生成前端配置文件...");
    const frontendConfig = {
      contracts: deployedContracts,
      network: {
        chainId: (await ethers.provider.getNetwork()).chainId,
        name: (await ethers.provider.getNetwork()).name
      }
    };

    const frontendConfigPath = path.join(__dirname, "../frontend/config/contracts.json");
    const frontendConfigDir = path.dirname(frontendConfigPath);
    
    if (!fs.existsSync(frontendConfigDir)) {
      fs.mkdirSync(frontendConfigDir, { recursive: true });
    }

    fs.writeFileSync(frontendConfigPath, JSON.stringify(frontendConfig, null, 2));
    console.log("✅ 前端配置文件已生成:", frontendConfigPath);

    // 11. 显示部署摘要
    console.log("\n🎉 部署完成！合约地址摘要:");
    console.log("=====================================");
    Object.entries(deployedContracts).forEach(([name, address]) => {
      console.log(`${name.padEnd(20)}: ${address}`);
    });
    console.log("=====================================");

    console.log("\n📋 下一步操作:");
    console.log("1. 验证合约 (如果在测试网/主网):");
    console.log("   npx hardhat verify --network <network> <contract-address> <constructor-args>");
    console.log("\n2. 启动前端应用:");
    console.log("   npm run dev");
    console.log("\n3. 配置环境变量:");
    console.log("   将合约地址添加到 .env 文件中");

    return deployedContracts;

  } catch (error) {
    console.error("\n❌ 部署失败:", error);
    throw error;
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error(error);
      process.exit(1);
    });
}

module.exports = main;
