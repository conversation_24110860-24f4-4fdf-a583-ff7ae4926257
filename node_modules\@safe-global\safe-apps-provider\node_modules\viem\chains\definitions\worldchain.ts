import { chainConfig } from '../../op-stack/chainConfig.js'
import { define<PERSON>hain } from '../../utils/chain/defineChain.js'

const sourceId = 1 // mainnet

export const worldchain = /*#__PURE__*/ defineChain({
  ...chainConfig,
  id: 480,
  name: 'World Chain',
  network: 'worldchain',
  nativeCurrency: { name: 'Ether', symbol: 'ETH', decimals: 18 },
  rpcUrls: {
    default: { http: ['https://worldchain-mainnet.g.alchemy.com/public'] },
  },
  blockExplorers: {
    default: {
      name: 'Worldscan',
      url: 'https://worldscan.org',
      apiUrl: 'https://api.worldscan.org/api',
    },
    blockscout: {
      name: 'Blockscout',
      url: 'https://worldchain-mainnet.explorer.alchemy.com',
      apiUrl: 'https://worldchain-mainnet.explorer.alchemy.com/api',
    },
  },
  contracts: {
    ...chainConfig.contracts,
    multicall3: {
      address: '******************************************',
      blockCreated: 0,
    },
    disputeGameFactory: {
      [sourceId]: {
        address: '******************************************',
      },
    },
    l2OutputOracle: {
      [sourceId]: {
        address: '******************************************',
      },
    },
    portal: {
      [sourceId]: {
        address: '******************************************',
      },
    },
    l1StandardBridge: {
      [sourceId]: {
        address: '0x470458C91978D2d929704489Ad730DC3E3001113',
      },
    },
  },
  testnet: false,
  sourceId,
})
