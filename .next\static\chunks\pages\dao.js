/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["pages/dao"],{

/***/ "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Cxiaot%5CDesktop%5Ccs%5Cpages%5Cdao.js&page=%2Fdao!":
/*!*********************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Cxiaot%5CDesktop%5Ccs%5Cpages%5Cdao.js&page=%2Fdao! ***!
  \*********************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/dao\",\n      function () {\n        return __webpack_require__(/*! ./pages/dao.js */ \"./pages/dao.js\");\n      }\n    ]);\n    if(true) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/dao\"])\n      });\n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWNsaWVudC1wYWdlcy1sb2FkZXIuanM/YWJzb2x1dGVQYWdlUGF0aD1DJTNBJTVDVXNlcnMlNUN4aWFvdCU1Q0Rlc2t0b3AlNUNjcyU1Q3BhZ2VzJTVDZGFvLmpzJnBhZ2U9JTJGZGFvISIsIm1hcHBpbmdzIjoiO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxtQkFBTyxDQUFDLHNDQUFnQjtBQUN2QztBQUNBO0FBQ0EsT0FBTyxJQUFVO0FBQ2pCLE1BQU0sVUFBVTtBQUNoQjtBQUNBLE9BQU87QUFDUDtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8/YjAwOCJdLCJzb3VyY2VzQ29udGVudCI6WyJcbiAgICAod2luZG93Ll9fTkVYVF9QID0gd2luZG93Ll9fTkVYVF9QIHx8IFtdKS5wdXNoKFtcbiAgICAgIFwiL2Rhb1wiLFxuICAgICAgZnVuY3Rpb24gKCkge1xuICAgICAgICByZXR1cm4gcmVxdWlyZShcIi4vcGFnZXMvZGFvLmpzXCIpO1xuICAgICAgfVxuICAgIF0pO1xuICAgIGlmKG1vZHVsZS5ob3QpIHtcbiAgICAgIG1vZHVsZS5ob3QuZGlzcG9zZShmdW5jdGlvbiAoKSB7XG4gICAgICAgIHdpbmRvdy5fX05FWFRfUC5wdXNoKFtcIi9kYW9cIl0pXG4gICAgICB9KTtcbiAgICB9XG4gICJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Cxiaot%5CDesktop%5Ccs%5Cpages%5Cdao.js&page=%2Fdao!\n"));

/***/ }),

/***/ "./pages/dao.js":
/*!**********************!*\
  !*** ./pages/dao.js ***!
  \**********************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DAO; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _app__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./_app */ \"./pages/_app.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ethers */ \"./node_modules/ethers/lib.esm/index.js\");\n/* harmony import */ var _frontend_config_contracts_json__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../frontend/config/contracts.json */ \"./frontend/config/contracts.json\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nconst DAO_ABI = [\n    \"function governanceToken() view returns (address)\",\n    \"function proposalCount() view returns (uint256)\",\n    \"function treasuryBalance() view returns (uint256)\",\n    \"function votingPeriod() view returns (uint256)\",\n    \"function proposalThreshold() view returns (uint256)\",\n    \"function quorumPercentage() view returns (uint256)\",\n    \"function createProposal(string description, uint8 proposalType, uint256 requestedAmount, address beneficiary) returns (uint256)\",\n    \"function vote(uint256 proposalId, bool support)\",\n    \"function executeProposal(uint256 proposalId)\",\n    \"function updateProposalState(uint256 proposalId)\",\n    \"function getProposal(uint256 proposalId) view returns (uint256, address, string, uint8, uint256, address, uint256, uint256, uint256, uint256, uint8, bool)\",\n    \"function getUserVote(uint256 proposalId, address user) view returns (bool, bool)\",\n    \"function depositFunds() payable\",\n    \"function memberContributions(address) view returns (uint256)\"\n];\nconst TOKEN_ABI = [\n    \"function balanceOf(address) view returns (uint256)\",\n    \"function totalSupply() view returns (uint256)\",\n    \"function symbol() view returns (string)\"\n];\nconst ProposalType = {\n    GENERAL: 0,\n    FUNDING: 1,\n    PARAMETER_CHANGE: 2\n};\nconst ProposalState = {\n    PENDING: 0,\n    ACTIVE: 1,\n    SUCCEEDED: 2,\n    DEFEATED: 3,\n    EXECUTED: 4\n};\nfunction DAO() {\n    _s();\n    const { account, signer, isConnected } = (0,_app__WEBPACK_IMPORTED_MODULE_3__.useWeb3)();\n    const [daoContract, setDaoContract] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [tokenContract, setTokenContract] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [daoInfo, setDaoInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [proposals, setProposals] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"overview\");\n    // 表单状态\n    const [newProposal, setNewProposal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        description: \"\",\n        type: ProposalType.GENERAL,\n        requestedAmount: \"\",\n        beneficiary: \"\"\n    });\n    const [depositAmount, setDepositAmount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"0.1\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isConnected && signer) {\n            initializeContracts();\n        }\n    }, [\n        isConnected,\n        signer\n    ]);\n    const initializeContracts = async ()=>{\n        try {\n            const daoAddress = _frontend_config_contracts_json__WEBPACK_IMPORTED_MODULE_4__.contracts.SimpleDAO;\n            const dao = new ethers__WEBPACK_IMPORTED_MODULE_5__.ethers.Contract(daoAddress, DAO_ABI, signer);\n            setDaoContract(dao);\n            const tokenAddress = await dao.governanceToken();\n            const token = new ethers__WEBPACK_IMPORTED_MODULE_5__.ethers.Contract(tokenAddress, TOKEN_ABI, signer);\n            setTokenContract(token);\n            loadDAOData(dao, token);\n        } catch (error) {\n            console.error(\"初始化合约失败:\", error);\n        }\n    };\n    const loadDAOData = async (dao, token)=>{\n        setLoading(true);\n        try {\n            // 加载 DAO 基本信息\n            const [proposalCount, treasuryBalance, votingPeriod, proposalThreshold, quorumPercentage, userBalance, totalSupply, symbol, userContribution] = await Promise.all([\n                dao.proposalCount(),\n                dao.treasuryBalance(),\n                dao.votingPeriod(),\n                dao.proposalThreshold(),\n                dao.quorumPercentage(),\n                token.balanceOf(account),\n                token.totalSupply(),\n                token.symbol(),\n                dao.memberContributions(account)\n            ]);\n            setDaoInfo({\n                proposalCount: proposalCount.toString(),\n                treasuryBalance: ethers__WEBPACK_IMPORTED_MODULE_5__.ethers.formatEther(treasuryBalance),\n                votingPeriod: Number(votingPeriod) / 86400,\n                proposalThreshold: ethers__WEBPACK_IMPORTED_MODULE_5__.ethers.formatEther(proposalThreshold),\n                quorumPercentage: quorumPercentage.toString(),\n                userBalance: ethers__WEBPACK_IMPORTED_MODULE_5__.ethers.formatEther(userBalance),\n                totalSupply: ethers__WEBPACK_IMPORTED_MODULE_5__.ethers.formatEther(totalSupply),\n                symbol: symbol,\n                userContribution: ethers__WEBPACK_IMPORTED_MODULE_5__.ethers.formatEther(userContribution)\n            });\n            // 加载提案\n            await loadProposals(dao, Number(proposalCount));\n        } catch (error) {\n            console.error(\"加载 DAO 数据失败:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const loadProposals = async (dao, count)=>{\n        const proposalsData = [];\n        for(let i = 0; i < count; i++){\n            try {\n                const proposal = await dao.getProposal(i);\n                const [voted, choice] = await dao.getUserVote(i, account);\n                proposalsData.push({\n                    id: i,\n                    proposer: proposal[1],\n                    description: proposal[2],\n                    type: Number(proposal[3]),\n                    requestedAmount: ethers__WEBPACK_IMPORTED_MODULE_5__.ethers.formatEther(proposal[4]),\n                    beneficiary: proposal[5],\n                    forVotes: ethers__WEBPACK_IMPORTED_MODULE_5__.ethers.formatEther(proposal[6]),\n                    againstVotes: ethers__WEBPACK_IMPORTED_MODULE_5__.ethers.formatEther(proposal[7]),\n                    startTime: new Date(Number(proposal[8]) * 1000),\n                    endTime: new Date(Number(proposal[9]) * 1000),\n                    state: Number(proposal[10]),\n                    executed: proposal[11],\n                    userVoted: voted,\n                    userChoice: choice\n                });\n            } catch (error) {\n                console.error(\"加载提案 \".concat(i, \" 失败:\"), error);\n            }\n        }\n        setProposals(proposalsData.reverse()); // 最新的在前面\n    };\n    const handleCreateProposal = async ()=>{\n        if (!daoContract || !newProposal.description) return;\n        try {\n            setLoading(true);\n            const requestedAmount = newProposal.type === ProposalType.FUNDING ? ethers__WEBPACK_IMPORTED_MODULE_5__.ethers.parseEther(newProposal.requestedAmount || \"0\") : 0;\n            const beneficiary = newProposal.type === ProposalType.FUNDING ? newProposal.beneficiary : ethers__WEBPACK_IMPORTED_MODULE_5__.ethers.ZeroAddress;\n            const tx = await daoContract.createProposal(newProposal.description, newProposal.type, requestedAmount, beneficiary);\n            await tx.wait();\n            alert(\"提案创建成功!\");\n            setNewProposal({\n                description: \"\",\n                type: ProposalType.GENERAL,\n                requestedAmount: \"\",\n                beneficiary: \"\"\n            });\n            loadDAOData(daoContract, tokenContract);\n        } catch (error) {\n            console.error(\"创建提案失败:\", error);\n            alert(\"创建提案失败: \" + error.message);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleVote = async (proposalId, support)=>{\n        if (!daoContract) return;\n        try {\n            setLoading(true);\n            const tx = await daoContract.vote(proposalId, support);\n            await tx.wait();\n            alert(\"投票成功!\");\n            loadDAOData(daoContract, tokenContract);\n        } catch (error) {\n            console.error(\"投票失败:\", error);\n            alert(\"投票失败: \" + error.message);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleExecuteProposal = async (proposalId)=>{\n        if (!daoContract) return;\n        try {\n            setLoading(true);\n            const tx = await daoContract.executeProposal(proposalId);\n            await tx.wait();\n            alert(\"提案执行成功!\");\n            loadDAOData(daoContract, tokenContract);\n        } catch (error) {\n            console.error(\"执行提案失败:\", error);\n            alert(\"执行提案失败: \" + error.message);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleDeposit = async ()=>{\n        if (!daoContract || !depositAmount) return;\n        try {\n            setLoading(true);\n            const tx = await daoContract.depositFunds({\n                value: ethers__WEBPACK_IMPORTED_MODULE_5__.ethers.parseEther(depositAmount)\n            });\n            await tx.wait();\n            alert(\"存款成功!\");\n            loadDAOData(daoContract, tokenContract);\n        } catch (error) {\n            console.error(\"存款失败:\", error);\n            alert(\"存款失败: \" + error.message);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const getProposalTypeText = (type)=>{\n        switch(type){\n            case ProposalType.GENERAL:\n                return \"一般提案\";\n            case ProposalType.FUNDING:\n                return \"资金申请\";\n            case ProposalType.PARAMETER_CHANGE:\n                return \"参数修改\";\n            default:\n                return \"未知类型\";\n        }\n    };\n    const getProposalStateText = (state)=>{\n        switch(state){\n            case ProposalState.PENDING:\n                return \"待处理\";\n            case ProposalState.ACTIVE:\n                return \"投票中\";\n            case ProposalState.SUCCEEDED:\n                return \"通过\";\n            case ProposalState.DEFEATED:\n                return \"未通过\";\n            case ProposalState.EXECUTED:\n                return \"已执行\";\n            default:\n                return \"未知状态\";\n        }\n    };\n    const getProposalStateColor = (state)=>{\n        switch(state){\n            case ProposalState.PENDING:\n                return \"text-yellow-600\";\n            case ProposalState.ACTIVE:\n                return \"text-blue-600\";\n            case ProposalState.SUCCEEDED:\n                return \"text-green-600\";\n            case ProposalState.DEFEATED:\n                return \"text-red-600\";\n            case ProposalState.EXECUTED:\n                return \"text-purple-600\";\n            default:\n                return \"text-gray-600\";\n        }\n    };\n    if (!isConnected) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-8 text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-3xl font-bold mb-4\",\n                    children: \"DAO 治理\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                    lineNumber: 292,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-600\",\n                    children: \"请先连接钱包以使用 DAO 治理功能\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                    lineNumber: 293,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n            lineNumber: 291,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                    children: \"DAO 治理 - Web3 生态系统\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                    lineNumber: 301,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                lineNumber: 300,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"bg-white rounded-lg shadow-lg p-4 mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center space-x-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/\",\n                                    className: \"text-gray-600 font-medium hover:text-primary-600\",\n                                    children: \"首页\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                    lineNumber: 308,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/nft-marketplace\",\n                                    className: \"text-gray-600 font-medium hover:text-primary-600\",\n                                    children: \"NFT 市场\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                    lineNumber: 311,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/defi\",\n                                    className: \"text-gray-600 font-medium hover:text-primary-600\",\n                                    children: \"DeFi 协议\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                    lineNumber: 314,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/dao\",\n                                    className: \"text-primary-600 font-medium hover:text-primary-800\",\n                                    children: \"DAO 治理\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                    lineNumber: 317,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                            lineNumber: 307,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                        lineNumber: 306,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-4xl font-bold text-center mb-8 gradient-text\",\n                        children: \"\\uD83C\\uDFDB️ DAO 治理系统\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                        lineNumber: 323,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg p-1 shadow-lg\",\n                            children: [\n                                \"overview\",\n                                \"proposals\",\n                                \"create\",\n                                \"treasury\"\n                            ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setActiveTab(tab),\n                                    className: \"px-6 py-2 rounded-md font-medium transition-colors \".concat(activeTab === tab ? \"bg-primary-600 text-white\" : \"text-gray-600 hover:text-primary-600\"),\n                                    children: [\n                                        tab === \"overview\" && \"概览\",\n                                        tab === \"proposals\" && \"提案\",\n                                        tab === \"create\" && \"创建提案\",\n                                        tab === \"treasury\" && \"资金池\"\n                                    ]\n                                }, tab, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                    lineNumber: 331,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                            lineNumber: 329,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                        lineNumber: 328,\n                        columnNumber: 9\n                    }, this),\n                    activeTab === \"overview\" && daoInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid-responsive\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"card\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold mb-4\",\n                                        children: \"DAO 统计\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                        lineNumber: 353,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-600\",\n                                                        children: \"总提案数:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                        lineNumber: 356,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-semibold\",\n                                                        children: daoInfo.proposalCount\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                        lineNumber: 357,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                lineNumber: 355,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-600\",\n                                                        children: \"资金池:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                        lineNumber: 360,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-semibold\",\n                                                        children: [\n                                                            parseFloat(daoInfo.treasuryBalance).toFixed(4),\n                                                            \" ETH\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                        lineNumber: 361,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                lineNumber: 359,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-600\",\n                                                        children: \"投票期限:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                        lineNumber: 364,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-semibold\",\n                                                        children: [\n                                                            daoInfo.votingPeriod,\n                                                            \" 天\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                        lineNumber: 365,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                lineNumber: 363,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-600\",\n                                                        children: \"法定人数:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                        lineNumber: 368,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-semibold\",\n                                                        children: [\n                                                            daoInfo.quorumPercentage,\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                        lineNumber: 369,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                lineNumber: 367,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                        lineNumber: 354,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                lineNumber: 352,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"card\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold mb-4\",\n                                        children: \"我的治理权\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                        lineNumber: 375,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-600\",\n                                                        children: \"持有代币:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                        lineNumber: 378,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-semibold\",\n                                                        children: [\n                                                            parseFloat(daoInfo.userBalance).toFixed(2),\n                                                            \" \",\n                                                            daoInfo.symbol\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                        lineNumber: 379,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                lineNumber: 377,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-600\",\n                                                        children: \"投票权重:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                        lineNumber: 382,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-semibold\",\n                                                        children: [\n                                                            (parseFloat(daoInfo.userBalance) / parseFloat(daoInfo.totalSupply) * 100).toFixed(2),\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                        lineNumber: 383,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                lineNumber: 381,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-600\",\n                                                        children: \"贡献资金:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                        lineNumber: 388,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-semibold\",\n                                                        children: [\n                                                            parseFloat(daoInfo.userContribution).toFixed(4),\n                                                            \" ETH\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                        lineNumber: 389,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                lineNumber: 387,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-600\",\n                                                        children: \"提案阈值:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                        lineNumber: 392,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-semibold\",\n                                                        children: [\n                                                            daoInfo.proposalThreshold,\n                                                            \" \",\n                                                            daoInfo.symbol\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                        lineNumber: 393,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                lineNumber: 391,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                        lineNumber: 376,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                lineNumber: 374,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"card\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold mb-4\",\n                                        children: \"快速操作\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                        lineNumber: 399,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setActiveTab(\"create\"),\n                                                disabled: parseFloat(daoInfo.userBalance) < parseFloat(daoInfo.proposalThreshold),\n                                                className: \"btn-primary w-full disabled:opacity-50\",\n                                                children: \"创建提案\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                lineNumber: 401,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setActiveTab(\"treasury\"),\n                                                className: \"btn-secondary w-full\",\n                                                children: \"向资金池存款\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                lineNumber: 408,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                        lineNumber: 400,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                lineNumber: 398,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                        lineNumber: 351,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === \"proposals\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            proposals.map((proposal)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-start mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-xl font-bold mb-2\",\n                                                            children: [\n                                                                \"提案 #\",\n                                                                proposal.id\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                            lineNumber: 426,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"inline-block px-2 py-1 rounded text-sm font-medium \".concat(getProposalStateColor(proposal.state)),\n                                                            children: getProposalStateText(proposal.state)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                            lineNumber: 427,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"ml-2 inline-block px-2 py-1 rounded text-sm bg-gray-100\",\n                                                            children: getProposalTypeText(proposal.type)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                            lineNumber: 430,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                    lineNumber: 425,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-right text-sm text-gray-600\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: [\n                                                                \"提案者: \",\n                                                                proposal.proposer.slice(0, 6),\n                                                                \"...\",\n                                                                proposal.proposer.slice(-4)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                            lineNumber: 435,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: [\n                                                                \"结束时间: \",\n                                                                proposal.endTime.toLocaleDateString()\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                            lineNumber: 436,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                    lineNumber: 434,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                            lineNumber: 424,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-700 mb-4\",\n                                            children: proposal.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                            lineNumber: 440,\n                                            columnNumber: 17\n                                        }, this),\n                                        proposal.type === ProposalType.FUNDING && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-yellow-50 p-3 rounded mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"申请金额:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                            lineNumber: 445,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \" \",\n                                                        proposal.requestedAmount,\n                                                        \" ETH\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                    lineNumber: 444,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"受益人:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                            lineNumber: 448,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \" \",\n                                                        proposal.beneficiary\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                    lineNumber: 447,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                            lineNumber: 443,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-4 mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"支持票\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                            lineNumber: 455,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-lg font-bold text-green-600\",\n                                                            children: parseFloat(proposal.forVotes).toFixed(2)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                            lineNumber: 456,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                    lineNumber: 454,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"反对票\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                            lineNumber: 459,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-lg font-bold text-red-600\",\n                                                            children: parseFloat(proposal.againstVotes).toFixed(2)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                            lineNumber: 460,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                    lineNumber: 458,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                            lineNumber: 453,\n                                            columnNumber: 17\n                                        }, this),\n                                        proposal.state === ProposalState.ACTIVE && !proposal.userVoted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleVote(proposal.id, true),\n                                                    disabled: loading,\n                                                    className: \"btn-primary flex-1\",\n                                                    children: \"支持\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                    lineNumber: 466,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleVote(proposal.id, false),\n                                                    disabled: loading,\n                                                    className: \"bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg flex-1\",\n                                                    children: \"反对\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                    lineNumber: 473,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                            lineNumber: 465,\n                                            columnNumber: 19\n                                        }, this),\n                                        proposal.userVoted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-center text-sm text-gray-600\",\n                                            children: [\n                                                \"您已投票: \",\n                                                proposal.userChoice ? \"支持\" : \"反对\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                            lineNumber: 484,\n                                            columnNumber: 19\n                                        }, this),\n                                        proposal.state === ProposalState.SUCCEEDED && !proposal.executed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>handleExecuteProposal(proposal.id),\n                                            disabled: loading,\n                                            className: \"btn-primary w-full\",\n                                            children: \"执行提案\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                            lineNumber: 490,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, proposal.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                    lineNumber: 423,\n                                    columnNumber: 15\n                                }, this)),\n                            proposals.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: \"暂无提案\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                    lineNumber: 503,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                lineNumber: 502,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                        lineNumber: 421,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === \"create\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-2xl mx-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-bold mb-6\",\n                                    children: \"创建新提案\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                    lineNumber: 513,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                    children: \"提案类型\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                    lineNumber: 517,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    value: newProposal.type,\n                                                    onChange: (e)=>setNewProposal({\n                                                            ...newProposal,\n                                                            type: parseInt(e.target.value)\n                                                        }),\n                                                    className: \"input-field\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: ProposalType.GENERAL,\n                                                            children: \"一般提案\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                            lineNumber: 525,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: ProposalType.FUNDING,\n                                                            children: \"资金申请\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                            lineNumber: 526,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: ProposalType.PARAMETER_CHANGE,\n                                                            children: \"参数修改\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                            lineNumber: 527,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                    lineNumber: 520,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                            lineNumber: 516,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                    children: \"提案描述\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                    lineNumber: 532,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                    value: newProposal.description,\n                                                    onChange: (e)=>setNewProposal({\n                                                            ...newProposal,\n                                                            description: e.target.value\n                                                        }),\n                                                    placeholder: \"详细描述您的提案...\",\n                                                    rows: 4,\n                                                    className: \"input-field\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                    lineNumber: 535,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                            lineNumber: 531,\n                                            columnNumber: 17\n                                        }, this),\n                                        newProposal.type === ProposalType.FUNDING && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                            children: \"申请金额 (ETH)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                            lineNumber: 547,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"number\",\n                                                            step: \"0.01\",\n                                                            value: newProposal.requestedAmount,\n                                                            onChange: (e)=>setNewProposal({\n                                                                    ...newProposal,\n                                                                    requestedAmount: e.target.value\n                                                                }),\n                                                            placeholder: \"0.0\",\n                                                            className: \"input-field\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                            lineNumber: 550,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                    lineNumber: 546,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                            children: \"受益人地址\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                            lineNumber: 560,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: newProposal.beneficiary,\n                                                            onChange: (e)=>setNewProposal({\n                                                                    ...newProposal,\n                                                                    beneficiary: e.target.value\n                                                                }),\n                                                            placeholder: \"0x...\",\n                                                            className: \"input-field\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                            lineNumber: 563,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                    lineNumber: 559,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleCreateProposal,\n                                            disabled: loading || !newProposal.description || newProposal.type === ProposalType.FUNDING && (!newProposal.requestedAmount || !newProposal.beneficiary),\n                                            className: \"btn-primary w-full disabled:opacity-50\",\n                                            children: loading ? \"创建中...\" : \"创建提案\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                            lineNumber: 574,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                    lineNumber: 515,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                            lineNumber: 512,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                        lineNumber: 511,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === \"treasury\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-md mx-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-bold mb-4\",\n                                    children: \"向 DAO 资金池存款\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                    lineNumber: 591,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                    children: \"存款金额 (ETH)\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                    lineNumber: 594,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    step: \"0.01\",\n                                                    value: depositAmount,\n                                                    onChange: (e)=>setDepositAmount(e.target.value),\n                                                    className: \"input-field\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                    lineNumber: 597,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                            lineNumber: 593,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleDeposit,\n                                            disabled: loading || !depositAmount || parseFloat(depositAmount) <= 0,\n                                            className: \"btn-primary w-full disabled:opacity-50\",\n                                            children: loading ? \"存款中...\" : \"存入 \".concat(depositAmount, \" ETH\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                            lineNumber: 605,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 text-center\",\n                                            children: \"存款将增加您在 DAO 中的贡献记录\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                            lineNumber: 612,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                    lineNumber: 592,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                            lineNumber: 590,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                        lineNumber: 589,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                lineNumber: 304,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(DAO, \"UWgRlH5BryRBXMxtTDzpYi3gE8M=\", false, function() {\n    return [\n        _app__WEBPACK_IMPORTED_MODULE_3__.useWeb3\n    ];\n});\n_c = DAO;\nvar _c;\n$RefreshReg$(_c, \"DAO\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/dao.js\n"));

/***/ }),

/***/ "./node_modules/next/head.js":
/*!***********************************!*\
  !*** ./node_modules/next/head.js ***!
  \***********************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("module.exports = __webpack_require__(/*! ./dist/shared/lib/head */ \"./node_modules/next/dist/shared/lib/head.js\")\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9oZWFkLmpzIiwibWFwcGluZ3MiOiJBQUFBLGlIQUFrRCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbmV4dC9oZWFkLmpzPzg4NDkiXSwic291cmNlc0NvbnRlbnQiOlsibW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Rpc3Qvc2hhcmVkL2xpYi9oZWFkJylcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./node_modules/next/head.js\n"));

/***/ }),

/***/ "./frontend/config/contracts.json":
/*!****************************************!*\
  !*** ./frontend/config/contracts.json ***!
  \****************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

"use strict";
module.exports = /*#__PURE__*/JSON.parse('{"contracts":{"SimpleToken":"0xa513E6E4b8f2a923D98304ec87F64353C4D5C853","Web3NFT":"0x2279B7A0a67DB372996a5FaB50D91eAA73d2eBe6","NFTMarketplace":"0x8A791620dd6260079BF849Dc5567aDC3F2FdC318","SimpleDAO":"0x610178dA211FEF7D417bC0e6FeD39F05609AD788"},"network":{"chainId":31337,"name":"localhost"}}');

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["pages/_app","main"], function() { return __webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Cxiaot%5CDesktop%5Ccs%5Cpages%5Cdao.js&page=%2Fdao!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);