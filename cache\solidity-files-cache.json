{"_format": "hh-sol-cache-2", "files": {"C:\\Users\\<USER>\\Desktop\\cs\\contracts\\bridge\\CrossChainBridge.sol": {"lastModificationDate": 1754406227301, "contentHash": "04f619a888548657d28f58e142648be5", "sourceName": "contracts/bridge/CrossChainBridge.sol", "solcConfig": {"version": "0.8.24", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["@openzeppelin/contracts/token/ERC20/IERC20.sol", "@openzeppelin/contracts/access/Ownable.sol", "@openzeppelin/contracts/utils/ReentrancyGuard.sol", "@openzeppelin/contracts/utils/cryptography/ECDSA.sol", "@openzeppelin/contracts/utils/cryptography/MessageHashUtils.sol"], "versionPragmas": ["^0.8.24"], "artifacts": ["CrossChainBridge"]}, "C:\\Users\\<USER>\\Desktop\\cs\\node_modules\\@openzeppelin\\contracts\\utils\\ReentrancyGuard.sol": {"lastModificationDate": 1754405674371, "contentHash": "190613e556d509d9e9a0ea43dc5d891d", "sourceName": "@openzeppelin/contracts/utils/ReentrancyGuard.sol", "solcConfig": {"version": "0.8.24", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["Reentrancy<PERSON><PERSON>"]}, "C:\\Users\\<USER>\\Desktop\\cs\\node_modules\\@openzeppelin\\contracts\\access\\Ownable.sol": {"lastModificationDate": 1754405674305, "contentHash": "d3c790edc9ccf808a17c5a6cd13614fd", "sourceName": "@openzeppelin/contracts/access/Ownable.sol", "solcConfig": {"version": "0.8.24", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../utils/Context.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["Ownable"]}, "C:\\Users\\<USER>\\Desktop\\cs\\node_modules\\@openzeppelin\\contracts\\token\\ERC20\\IERC20.sol": {"lastModificationDate": 1754405673327, "contentHash": "9261adf6457863de3e9892f51317ec89", "sourceName": "@openzeppelin/contracts/token/ERC20/IERC20.sol", "solcConfig": {"version": "0.8.24", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": [">=0.4.16"], "artifacts": ["IERC20"]}, "C:\\Users\\<USER>\\Desktop\\cs\\node_modules\\@openzeppelin\\contracts\\utils\\cryptography\\MessageHashUtils.sol": {"lastModificationDate": 1754405674199, "contentHash": "260f3968eefa3bbd30520cff5384cd93", "sourceName": "@openzeppelin/contracts/utils/cryptography/MessageHashUtils.sol", "solcConfig": {"version": "0.8.24", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../Strings.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["MessageHashUtils"]}, "C:\\Users\\<USER>\\Desktop\\cs\\node_modules\\@openzeppelin\\contracts\\utils\\cryptography\\ECDSA.sol": {"lastModificationDate": 1754405671892, "contentHash": "81de029d56aa803972be03c5d277cb6c", "sourceName": "@openzeppelin/contracts/utils/cryptography/ECDSA.sol", "solcConfig": {"version": "0.8.24", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["ECDSA"]}, "C:\\Users\\<USER>\\Desktop\\cs\\node_modules\\@openzeppelin\\contracts\\utils\\Context.sol": {"lastModificationDate": 1754405671274, "contentHash": "67bfbc07588eb8683b3fd8f6f909563e", "sourceName": "@openzeppelin/contracts/utils/Context.sol", "solcConfig": {"version": "0.8.24", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["Context"]}, "C:\\Users\\<USER>\\Desktop\\cs\\node_modules\\@openzeppelin\\contracts\\utils\\Strings.sol": {"lastModificationDate": 1754405674497, "contentHash": "d8f70caf0e0c77dc908176ed44812fb7", "sourceName": "@openzeppelin/contracts/utils/Strings.sol", "solcConfig": {"version": "0.8.24", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["./math/Math.sol", "./math/SafeCast.sol", "./math/SignedMath.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["Strings"]}, "C:\\Users\\<USER>\\Desktop\\cs\\node_modules\\@openzeppelin\\contracts\\utils\\math\\Math.sol": {"lastModificationDate": 1754405674174, "contentHash": "5ec781e33d3a9ac91ffdc83d94420412", "sourceName": "@openzeppelin/contracts/utils/math/Math.sol", "solcConfig": {"version": "0.8.24", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../Panic.sol", "./SafeCast.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["Math"]}, "C:\\Users\\<USER>\\Desktop\\cs\\node_modules\\@openzeppelin\\contracts\\utils\\math\\SignedMath.sol": {"lastModificationDate": 1754405674434, "contentHash": "ae3528afb8bdb0a7dcfba5b115ee8074", "sourceName": "@openzeppelin/contracts/utils/math/SignedMath.sol", "solcConfig": {"version": "0.8.24", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["./SafeCast.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["SignedMath"]}, "C:\\Users\\<USER>\\Desktop\\cs\\node_modules\\@openzeppelin\\contracts\\utils\\math\\SafeCast.sol": {"lastModificationDate": 1754405674401, "contentHash": "2adca1150f58fc6f3d1f0a0f22ee7cca", "sourceName": "@openzeppelin/contracts/utils/math/SafeCast.sol", "solcConfig": {"version": "0.8.24", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["SafeCast"]}, "C:\\Users\\<USER>\\Desktop\\cs\\node_modules\\@openzeppelin\\contracts\\utils\\Panic.sol": {"lastModificationDate": 1754405674342, "contentHash": "2133dc13536b4a6a98131e431fac59e1", "sourceName": "@openzeppelin/contracts/utils/Panic.sol", "solcConfig": {"version": "0.8.24", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["Panic"]}, "C:\\Users\\<USER>\\Desktop\\cs\\node_modules\\@openzeppelin\\contracts\\governance\\utils\\Votes.sol": {"lastModificationDate": 1754405674576, "contentHash": "95aceafdc639babdd22576e5e3774d64", "sourceName": "@openzeppelin/contracts/governance/utils/Votes.sol", "solcConfig": {"version": "0.8.24", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../../interfaces/IERC5805.sol", "../../utils/Context.sol", "../../utils/Nonces.sol", "../../utils/cryptography/EIP712.sol", "../../utils/structs/Checkpoints.sol", "../../utils/math/SafeCast.sol", "../../utils/cryptography/ECDSA.sol", "../../utils/types/Time.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["Votes"]}, "C:\\Users\\<USER>\\Desktop\\cs\\node_modules\\@openzeppelin\\contracts\\utils\\Nonces.sol": {"lastModificationDate": 1754405674292, "contentHash": "c32d108058718efb9061b88e83a83f79", "sourceName": "@openzeppelin/contracts/utils/Nonces.sol", "solcConfig": {"version": "0.8.24", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["Nonces"]}, "C:\\Users\\<USER>\\Desktop\\cs\\node_modules\\@openzeppelin\\contracts\\utils\\structs\\Checkpoints.sol": {"lastModificationDate": 1754405671173, "contentHash": "7e3b3e7a04ea9d446b39835a2f6a58cd", "sourceName": "@openzeppelin/contracts/utils/structs/Checkpoints.sol", "solcConfig": {"version": "0.8.24", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../math/Math.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["Checkpoints"]}, "C:\\Users\\<USER>\\Desktop\\cs\\node_modules\\@openzeppelin\\contracts\\utils\\cryptography\\EIP712.sol": {"lastModificationDate": 1754405671913, "contentHash": "d67dfba302ed2489324d7584625c3bde", "sourceName": "@openzeppelin/contracts/utils/cryptography/EIP712.sol", "solcConfig": {"version": "0.8.24", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["./MessageHashUtils.sol", "../ShortStrings.sol", "../../interfaces/IERC5267.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["EIP712"]}, "C:\\Users\\<USER>\\Desktop\\cs\\node_modules\\@openzeppelin\\contracts\\interfaces\\IERC5805.sol": {"lastModificationDate": 1754405673465, "contentHash": "4ca519ef7ae252e5aaff422172d4e8a9", "sourceName": "@openzeppelin/contracts/interfaces/IERC5805.sol", "solcConfig": {"version": "0.8.24", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../governance/utils/IVotes.sol", "./IERC6372.sol"], "versionPragmas": [">=0.8.4"], "artifacts": ["IERC5805"]}, "C:\\Users\\<USER>\\Desktop\\cs\\node_modules\\@openzeppelin\\contracts\\utils\\types\\Time.sol": {"lastModificationDate": 1754405674505, "contentHash": "d83e7814a059fc1287fd765f424ce004", "sourceName": "@openzeppelin/contracts/utils/types/Time.sol", "solcConfig": {"version": "0.8.24", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../math/Math.sol", "../math/SafeCast.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["Time"]}, "C:\\Users\\<USER>\\Desktop\\cs\\node_modules\\@openzeppelin\\contracts\\utils\\ShortStrings.sol": {"lastModificationDate": 1754405674417, "contentHash": "c1be9487e5a64acf23b5d8028482e748", "sourceName": "@openzeppelin/contracts/utils/ShortStrings.sol", "solcConfig": {"version": "0.8.24", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["./StorageSlot.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["ShortStrings"]}, "C:\\Users\\<USER>\\Desktop\\cs\\node_modules\\@openzeppelin\\contracts\\interfaces\\IERC5267.sol": {"lastModificationDate": 1754405673453, "contentHash": "6e00d2fe4bc0d5f1add753824498bb35", "sourceName": "@openzeppelin/contracts/interfaces/IERC5267.sol", "solcConfig": {"version": "0.8.24", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": [">=0.4.16"], "artifacts": ["IERC5267"]}, "C:\\Users\\<USER>\\Desktop\\cs\\node_modules\\@openzeppelin\\contracts\\utils\\StorageSlot.sol": {"lastModificationDate": 1754405674487, "contentHash": "e656d64c4ce918f3d13030b91c935134", "sourceName": "@openzeppelin/contracts/utils/StorageSlot.sol", "solcConfig": {"version": "0.8.24", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["StorageSlot"]}, "C:\\Users\\<USER>\\Desktop\\cs\\node_modules\\@openzeppelin\\contracts\\governance\\utils\\IVotes.sol": {"lastModificationDate": 1754405673798, "contentHash": "61c6e9b90081d85a755a3107dcaacc26", "sourceName": "@openzeppelin/contracts/governance/utils/IVotes.sol", "solcConfig": {"version": "0.8.24", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": [">=0.8.4"], "artifacts": ["IVotes"]}, "C:\\Users\\<USER>\\Desktop\\cs\\node_modules\\@openzeppelin\\contracts\\interfaces\\IERC6372.sol": {"lastModificationDate": 1754405673476, "contentHash": "af8cb4a1b941a8230c36de0b4aacf653", "sourceName": "@openzeppelin/contracts/interfaces/IERC6372.sol", "solcConfig": {"version": "0.8.24", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": [">=0.4.16"], "artifacts": ["IERC6372"]}, "C:\\Users\\<USER>\\Desktop\\cs\\node_modules\\@openzeppelin\\contracts\\token\\ERC20\\extensions\\ERC20Votes.sol": {"lastModificationDate": 1754405672413, "contentHash": "51c2083b160453420aaa0a046c16d5ca", "sourceName": "@openzeppelin/contracts/token/ERC20/extensions/ERC20Votes.sol", "solcConfig": {"version": "0.8.24", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../ERC20.sol", "../../../governance/utils/Votes.sol", "../../../utils/structs/Checkpoints.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["ERC20Votes"]}, "C:\\Users\\<USER>\\Desktop\\cs\\node_modules\\@openzeppelin\\contracts\\token\\ERC20\\ERC20.sol": {"lastModificationDate": 1754405672287, "contentHash": "59dfce11284f2636db261df9b6a18f81", "sourceName": "@openzeppelin/contracts/token/ERC20/ERC20.sol", "solcConfig": {"version": "0.8.24", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["./IERC20.sol", "./extensions/IERC20Metadata.sol", "../../utils/Context.sol", "../../interfaces/draft-IERC6093.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["ERC20"]}, "C:\\Users\\<USER>\\Desktop\\cs\\node_modules\\@openzeppelin\\contracts\\interfaces\\draft-IERC6093.sol": {"lastModificationDate": 1754405671812, "contentHash": "5041977bbe908de2e6ed0270447f79ad", "sourceName": "@openzeppelin/contracts/interfaces/draft-IERC6093.sol", "solcConfig": {"version": "0.8.24", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": [">=0.8.4"], "artifacts": ["IERC1155Errors", "IERC20Errors", "IERC721Errors"]}, "C:\\Users\\<USER>\\Desktop\\cs\\node_modules\\@openzeppelin\\contracts\\token\\ERC20\\extensions\\IERC20Metadata.sol": {"lastModificationDate": 1754405673351, "contentHash": "513778b30d2750f5d2b9b19bbcf748a5", "sourceName": "@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol", "solcConfig": {"version": "0.8.24", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../IERC20.sol"], "versionPragmas": [">=0.6.2"], "artifacts": ["IERC20Metadata"]}, "C:\\Users\\<USER>\\Desktop\\cs\\node_modules\\@openzeppelin\\contracts\\token\\ERC20\\extensions\\ERC20Permit.sol": {"lastModificationDate": 1754405672401, "contentHash": "2343c0c3b51267f4f2639ec3f99cd63e", "sourceName": "@openzeppelin/contracts/token/ERC20/extensions/ERC20Permit.sol", "solcConfig": {"version": "0.8.24", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["./IERC20Permit.sol", "../ERC20.sol", "../../../utils/cryptography/ECDSA.sol", "../../../utils/cryptography/EIP712.sol", "../../../utils/Nonces.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["ERC20Permit"]}, "C:\\Users\\<USER>\\Desktop\\cs\\node_modules\\@openzeppelin\\contracts\\token\\ERC20\\extensions\\IERC20Permit.sol": {"lastModificationDate": 1754405673358, "contentHash": "b01905745d47df90c7bd5dd70d498cb7", "sourceName": "@openzeppelin/contracts/token/ERC20/extensions/IERC20Permit.sol", "solcConfig": {"version": "0.8.24", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": [">=0.4.16"], "artifacts": ["IERC20Permit"]}, "C:\\Users\\<USER>\\Desktop\\cs\\contracts\\tokens\\Web3Token.sol": {"lastModificationDate": 1754406170375, "contentHash": "45ca7ee12a3b081256153b7951f57d29", "sourceName": "contracts/tokens/Web3Token.sol", "solcConfig": {"version": "0.8.24", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["@openzeppelin/contracts/token/ERC20/ERC20.sol", "@openzeppelin/contracts/token/ERC20/extensions/ERC20Burnable.sol", "@openzeppelin/contracts/token/ERC20/extensions/ERC20Pausable.sol", "@openzeppelin/contracts/access/Ownable.sol", "@openzeppelin/contracts/token/ERC20/extensions/ERC20Permit.sol", "@openzeppelin/contracts/token/ERC20/extensions/ERC20Votes.sol", "@openzeppelin/contracts/utils/ReentrancyGuard.sol"], "versionPragmas": ["^0.8.24"], "artifacts": ["Web3Token"]}, "C:\\Users\\<USER>\\Desktop\\cs\\node_modules\\@openzeppelin\\contracts\\token\\ERC20\\extensions\\ERC20Pausable.sol": {"lastModificationDate": 1754405672386, "contentHash": "a3ce035689a60e32921347c16a3fb786", "sourceName": "@openzeppelin/contracts/token/ERC20/extensions/ERC20Pausable.sol", "solcConfig": {"version": "0.8.24", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../ERC20.sol", "../../../utils/Pausable.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["ERC20Pausable"]}, "C:\\Users\\<USER>\\Desktop\\cs\\node_modules\\@openzeppelin\\contracts\\token\\ERC20\\extensions\\ERC20Burnable.sol": {"lastModificationDate": 1754405672323, "contentHash": "273d8d24b06f67207dd5f35c3a0c1086", "sourceName": "@openzeppelin/contracts/token/ERC20/extensions/ERC20Burnable.sol", "solcConfig": {"version": "0.8.24", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../ERC20.sol", "../../../utils/Context.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["ERC20Burnable"]}, "C:\\Users\\<USER>\\Desktop\\cs\\node_modules\\@openzeppelin\\contracts\\utils\\Pausable.sol": {"lastModificationDate": 1754405674348, "contentHash": "0d47b53e10b1985efbb396f937626279", "sourceName": "@openzeppelin/contracts/utils/Pausable.sol", "solcConfig": {"version": "0.8.24", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../utils/Context.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["Pausable"]}, "C:\\Users\\<USER>\\Desktop\\cs\\node_modules\\@openzeppelin\\contracts\\token\\ERC721\\extensions\\ERC721Pausable.sol": {"lastModificationDate": 1754405672655, "contentHash": "bf7cceab5154fe7b4289fe4452e83f38", "sourceName": "@openzeppelin/contracts/token/ERC721/extensions/ERC721Pausable.sol", "solcConfig": {"version": "0.8.24", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../ERC721.sol", "../../../utils/Pausable.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["ERC721Pausable"]}, "C:\\Users\\<USER>\\Desktop\\cs\\node_modules\\@openzeppelin\\contracts\\token\\ERC721\\ERC721.sol": {"lastModificationDate": 1754405672539, "contentHash": "522ec023bce510c30dfbf61584f5a190", "sourceName": "@openzeppelin/contracts/token/ERC721/ERC721.sol", "solcConfig": {"version": "0.8.24", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["./IERC721.sol", "./extensions/IERC721Metadata.sol", "./utils/ERC721Utils.sol", "../../utils/Context.sol", "../../utils/Strings.sol", "../../utils/introspection/ERC165.sol", "../../interfaces/draft-IERC6093.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["ERC721"]}, "C:\\Users\\<USER>\\Desktop\\cs\\node_modules\\@openzeppelin\\contracts\\token\\ERC721\\IERC721.sol": {"lastModificationDate": 1754405673683, "contentHash": "f62e11dbd302e17a5621a1438f4e054c", "sourceName": "@openzeppelin/contracts/token/ERC721/IERC721.sol", "solcConfig": {"version": "0.8.24", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../../utils/introspection/IERC165.sol"], "versionPragmas": [">=0.6.2"], "artifacts": ["IERC721"]}, "C:\\Users\\<USER>\\Desktop\\cs\\node_modules\\@openzeppelin\\contracts\\utils\\introspection\\ERC165.sol": {"lastModificationDate": 1754405672192, "contentHash": "0906d06dca25210d4696dcef6dad2909", "sourceName": "@openzeppelin/contracts/utils/introspection/ERC165.sol", "solcConfig": {"version": "0.8.24", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["./IERC165.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["ERC165"]}, "C:\\Users\\<USER>\\Desktop\\cs\\node_modules\\@openzeppelin\\contracts\\token\\ERC721\\utils\\ERC721Utils.sol": {"lastModificationDate": 1754405672691, "contentHash": "4cefad279a37c895b709bce2b901816e", "sourceName": "@openzeppelin/contracts/token/ERC721/utils/ERC721Utils.sol", "solcConfig": {"version": "0.8.24", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../IERC721Receiver.sol", "../../../interfaces/draft-IERC6093.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["ERC721Utils"]}, "C:\\Users\\<USER>\\Desktop\\cs\\node_modules\\@openzeppelin\\contracts\\token\\ERC721\\extensions\\IERC721Metadata.sol": {"lastModificationDate": 1754405673715, "contentHash": "136c64eca25bcb8a68f43ac6605559f9", "sourceName": "@openzeppelin/contracts/token/ERC721/extensions/IERC721Metadata.sol", "solcConfig": {"version": "0.8.24", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../IERC721.sol"], "versionPragmas": [">=0.6.2"], "artifacts": ["IERC721Metadata"]}, "C:\\Users\\<USER>\\Desktop\\cs\\node_modules\\@openzeppelin\\contracts\\utils\\introspection\\IERC165.sol": {"lastModificationDate": 1754405673195, "contentHash": "7074c93b1ea0a122063f26ddd1db1032", "sourceName": "@openzeppelin/contracts/utils/introspection/IERC165.sol", "solcConfig": {"version": "0.8.24", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": [">=0.4.16"], "artifacts": ["IERC165"]}, "C:\\Users\\<USER>\\Desktop\\cs\\node_modules\\@openzeppelin\\contracts\\token\\ERC721\\IERC721Receiver.sol": {"lastModificationDate": 1754405673731, "contentHash": "d61660a41ce200e99816e4734f7fd202", "sourceName": "@openzeppelin/contracts/token/ERC721/IERC721Receiver.sol", "solcConfig": {"version": "0.8.24", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": [">=0.5.0"], "artifacts": ["IERC721Receiver"]}, "C:\\Users\\<USER>\\Desktop\\cs\\node_modules\\@openzeppelin\\contracts\\token\\ERC721\\extensions\\ERC721Burnable.sol": {"lastModificationDate": 1754405672565, "contentHash": "594379619f21d2767c325a6c46b53399", "sourceName": "@openzeppelin/contracts/token/ERC721/extensions/ERC721Burnable.sol", "solcConfig": {"version": "0.8.24", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../ERC721.sol", "../../../utils/Context.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["ERC721Burnable"]}, "C:\\Users\\<USER>\\Desktop\\cs\\contracts\\nft\\Web3NFT.sol": {"lastModificationDate": 1754406182054, "contentHash": "d82432e6ae550d87d854b351206d581d", "sourceName": "contracts/nft/Web3NFT.sol", "solcConfig": {"version": "0.8.24", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["@openzeppelin/contracts/token/ERC721/ERC721.sol", "@openzeppelin/contracts/token/ERC721/extensions/ERC721Enumerable.sol", "@openzeppelin/contracts/token/ERC721/extensions/ERC721URIStorage.sol", "@openzeppelin/contracts/token/ERC721/extensions/ERC721Pausable.sol", "@openzeppelin/contracts/access/Ownable.sol", "@openzeppelin/contracts/token/ERC721/extensions/ERC721Burnable.sol", "@openzeppelin/contracts/utils/ReentrancyGuard.sol"], "versionPragmas": ["^0.8.24"], "artifacts": ["Web3NFT"]}, "C:\\Users\\<USER>\\Desktop\\cs\\node_modules\\@openzeppelin\\contracts\\token\\ERC721\\extensions\\ERC721URIStorage.sol": {"lastModificationDate": 1754405672678, "contentHash": "0b00dd0d9f2738855f17aa283bfa3ff5", "sourceName": "@openzeppelin/contracts/token/ERC721/extensions/ERC721URIStorage.sol", "solcConfig": {"version": "0.8.24", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../ERC721.sol", "./IERC721Metadata.sol", "../../../utils/Strings.sol", "../../../interfaces/IERC4906.sol", "../../../interfaces/IERC165.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["ERC721URIStorage"]}, "C:\\Users\\<USER>\\Desktop\\cs\\node_modules\\@openzeppelin\\contracts\\token\\ERC721\\extensions\\ERC721Enumerable.sol": {"lastModificationDate": 1754405672615, "contentHash": "1e4b432e9069bd6f5935e071b74d180c", "sourceName": "@openzeppelin/contracts/token/ERC721/extensions/ERC721Enumerable.sol", "solcConfig": {"version": "0.8.24", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../ERC721.sol", "./IERC721Enumerable.sol", "../../../utils/introspection/ERC165.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["ERC721Enumerable"]}, "C:\\Users\\<USER>\\Desktop\\cs\\node_modules\\@openzeppelin\\contracts\\interfaces\\IERC165.sol": {"lastModificationDate": 1754405673186, "contentHash": "947853028399b7de34bcc3704ee06e99", "sourceName": "@openzeppelin/contracts/interfaces/IERC165.sol", "solcConfig": {"version": "0.8.24", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../utils/introspection/IERC165.sol"], "versionPragmas": [">=0.4.16"], "artifacts": []}, "C:\\Users\\<USER>\\Desktop\\cs\\node_modules\\@openzeppelin\\contracts\\interfaces\\IERC4906.sol": {"lastModificationDate": 1754405673445, "contentHash": "4386d2e9b1578157802062e156bd9ae1", "sourceName": "@openzeppelin/contracts/interfaces/IERC4906.sol", "solcConfig": {"version": "0.8.24", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["./IERC165.sol", "./IERC721.sol"], "versionPragmas": [">=0.6.2"], "artifacts": ["IERC4906"]}, "C:\\Users\\<USER>\\Desktop\\cs\\node_modules\\@openzeppelin\\contracts\\interfaces\\IERC721.sol": {"lastModificationDate": 1754405673482, "contentHash": "6b9716905fb42874ed1769ae7f7458b1", "sourceName": "@openzeppelin/contracts/interfaces/IERC721.sol", "solcConfig": {"version": "0.8.24", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../token/ERC721/IERC721.sol"], "versionPragmas": [">=0.6.2"], "artifacts": []}, "C:\\Users\\<USER>\\Desktop\\cs\\node_modules\\@openzeppelin\\contracts\\token\\ERC721\\extensions\\IERC721Enumerable.sol": {"lastModificationDate": 1754405673701, "contentHash": "09d80d577d5abfc679455e7b67c5b537", "sourceName": "@openzeppelin/contracts/token/ERC721/extensions/IERC721Enumerable.sol", "solcConfig": {"version": "0.8.24", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../IERC721.sol"], "versionPragmas": [">=0.6.2"], "artifacts": ["IERC721Enumerable"]}, "C:\\Users\\<USER>\\Desktop\\cs\\contracts\\governance\\MultiSigWallet.sol": {"lastModificationDate": 1754406304310, "contentHash": "c3fb350e23da9f3c98ccbb90c773d229", "sourceName": "contracts/governance/MultiSigWallet.sol", "solcConfig": {"version": "0.8.24", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["@openzeppelin/contracts/utils/ReentrancyGuard.sol", "@openzeppelin/contracts/token/ERC20/IERC20.sol", "@openzeppelin/contracts/token/ERC721/IERC721.sol"], "versionPragmas": ["^0.8.24"], "artifacts": ["MultiSigWallet"]}, "C:\\Users\\<USER>\\Desktop\\cs\\contracts\\defi\\SimpleAMM.sol": {"lastModificationDate": 1754406215903, "contentHash": "c9d872ae32a7f1caced3ff7993c90a3d", "sourceName": "contracts/defi/SimpleAMM.sol", "solcConfig": {"version": "0.8.24", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["@openzeppelin/contracts/token/ERC20/IERC20.sol", "@openzeppelin/contracts/token/ERC20/ERC20.sol", "@openzeppelin/contracts/utils/ReentrancyGuard.sol", "@openzeppelin/contracts/access/Ownable.sol", "@openzeppelin/contracts/utils/math/Math.sol"], "versionPragmas": ["^0.8.24"], "artifacts": ["SimpleAMM"]}, "C:\\Users\\<USER>\\Desktop\\cs\\contracts\\dao\\SimpleDAO.sol": {"lastModificationDate": 1754441300097, "contentHash": "365e3472173c881c8b1048867512d63e", "sourceName": "contracts/dao/SimpleDAO.sol", "solcConfig": {"version": "0.8.24", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["@openzeppelin/contracts/token/ERC20/IERC20.sol", "@openzeppelin/contracts/access/Ownable.sol", "@openzeppelin/contracts/utils/ReentrancyGuard.sol"], "versionPragmas": ["^0.8.24"], "artifacts": ["SimpleDAO"]}, "C:\\Users\\<USER>\\Desktop\\cs\\contracts\\tokens\\SimpleToken.sol": {"lastModificationDate": 1754406706207, "contentHash": "c3372d8a962b0d072a74726f52f7e8ba", "sourceName": "contracts/tokens/SimpleToken.sol", "solcConfig": {"version": "0.8.24", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["@openzeppelin/contracts/token/ERC20/ERC20.sol", "@openzeppelin/contracts/access/Ownable.sol"], "versionPragmas": ["^0.8.24"], "artifacts": ["SimpleToken"]}, "C:\\Users\\<USER>\\Desktop\\cs\\contracts\\nft\\NFTMarketplace.sol": {"lastModificationDate": 1754440527332, "contentHash": "8e78ef6245214df988ddbccb06635f65", "sourceName": "contracts/nft/NFTMarketplace.sol", "solcConfig": {"version": "0.8.24", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["@openzeppelin/contracts/token/ERC721/IERC721.sol", "@openzeppelin/contracts/token/ERC721/IERC721Receiver.sol", "@openzeppelin/contracts/access/Ownable.sol", "@openzeppelin/contracts/utils/ReentrancyGuard.sol"], "versionPragmas": ["^0.8.24"], "artifacts": ["NFTMarketplace"]}}}