/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/_app";
exports.ids = ["pages/_app"];
exports.modules = {

/***/ "./pages/_app.js":
/*!***********************!*\
  !*** ./pages/_app.js ***!
  \***********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App),\n/* harmony export */   useWeb3: () => (/* binding */ useWeb3)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _frontend_styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../frontend/styles/globals.css */ \"./frontend/styles/globals.css\");\n/* harmony import */ var _frontend_styles_globals_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_frontend_styles_globals_css__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ethers */ \"ethers\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([ethers__WEBPACK_IMPORTED_MODULE_3__]);\nethers__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n// Web3 上下文\n\nconst Web3Context = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_2__.createContext)();\nconst useWeb3 = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_2__.useContext)(Web3Context);\n    if (!context) {\n        throw new Error(\"useWeb3 must be used within a Web3Provider\");\n    }\n    return context;\n};\nfunction Web3Provider({ children }) {\n    const [account, setAccount] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [provider, setProvider] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [signer, setSigner] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [chainId, setChainId] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [isConnecting, setIsConnecting] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // 连接钱包\n    const connectWallet = async ()=>{\n        if (typeof window.ethereum === \"undefined\") {\n            alert(\"请安装 MetaMask!\");\n            return;\n        }\n        setIsConnecting(true);\n        try {\n            // 请求账户访问\n            await window.ethereum.request({\n                method: \"eth_requestAccounts\"\n            });\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_3__.ethers.BrowserProvider(window.ethereum);\n            const signer = await provider.getSigner();\n            const address = await signer.getAddress();\n            const network = await provider.getNetwork();\n            setProvider(provider);\n            setSigner(signer);\n            setAccount(address);\n            setChainId(Number(network.chainId));\n            console.log(\"钱包连接成功:\", address);\n        } catch (error) {\n            console.error(\"连接钱包失败:\", error);\n            alert(\"连接钱包失败: \" + error.message);\n        } finally{\n            setIsConnecting(false);\n        }\n    };\n    // 断开钱包连接\n    const disconnectWallet = ()=>{\n        setAccount(null);\n        setProvider(null);\n        setSigner(null);\n        setChainId(null);\n    };\n    // 监听账户变化\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (typeof window.ethereum !== \"undefined\") {\n            window.ethereum.on(\"accountsChanged\", (accounts)=>{\n                if (accounts.length === 0) {\n                    disconnectWallet();\n                } else {\n                    connectWallet();\n                }\n            });\n            window.ethereum.on(\"chainChanged\", (chainId)=>{\n                setChainId(parseInt(chainId, 16));\n            });\n            // 检查是否已经连接\n            window.ethereum.request({\n                method: \"eth_accounts\"\n            }).then((accounts)=>{\n                if (accounts.length > 0) {\n                    connectWallet();\n                }\n            });\n        }\n        return ()=>{\n            if (typeof window.ethereum !== \"undefined\") {\n                window.ethereum.removeAllListeners(\"accountsChanged\");\n                window.ethereum.removeAllListeners(\"chainChanged\");\n            }\n        };\n    }, []);\n    const value = {\n        account,\n        provider,\n        signer,\n        chainId,\n        isConnecting,\n        connectWallet,\n        disconnectWallet,\n        isConnected: !!account\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Web3Context.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\_app.js\",\n        lineNumber: 108,\n        columnNumber: 5\n    }, this);\n}\nfunction App({ Component, pageProps }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Web3Provider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                ...pageProps\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\_app.js\",\n                lineNumber: 118,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\_app.js\",\n            lineNumber: 117,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\_app.js\",\n        lineNumber: 116,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/_app.js\n");

/***/ }),

/***/ "./frontend/styles/globals.css":
/*!*************************************!*\
  !*** ./frontend/styles/globals.css ***!
  \*************************************/
/***/ (() => {



/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "ethers":
/*!*************************!*\
  !*** external "ethers" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = import("ethers");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = (__webpack_exec__("./pages/_app.js"));
module.exports = __webpack_exports__;

})();