{"_format": "hh-sol-artifact-1", "contractName": "SimpleToken", "sourceName": "contracts/tokens/SimpleToken.sol", "abi": [{"inputs": [{"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "symbol", "type": "string"}, {"internalType": "uint256", "name": "initialSupply", "type": "uint256"}, {"internalType": "address", "name": "initialOwner", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "allowance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "name": "ERC20InsufficientAllowance", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "uint256", "name": "balance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "name": "ERC20InsufficientBalance", "type": "error"}, {"inputs": [{"internalType": "address", "name": "approver", "type": "address"}], "name": "ERC20InvalidApprover", "type": "error"}, {"inputs": [{"internalType": "address", "name": "receiver", "type": "address"}], "name": "ERC20InvalidReceiver", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}], "name": "ERC20InvalidSender", "type": "error"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}], "name": "ERC20InvalidSpender", "type": "error"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "OwnableInvalidOwner", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "OwnableUnauthorizedAccount", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "spender", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Approval", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Transfer", "type": "event"}, {"inputs": [], "name": "MAX_SUPPLY", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}], "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "burn", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "mint", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}