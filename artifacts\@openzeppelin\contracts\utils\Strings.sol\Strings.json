{"_format": "hh-sol-artifact-1", "contractName": "Strings", "sourceName": "@openzeppelin/contracts/utils/Strings.sol", "abi": [{"inputs": [{"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "uint256", "name": "length", "type": "uint256"}], "name": "StringsInsufficientHexLength", "type": "error"}, {"inputs": [], "name": "StringsInvalidAddressFormat", "type": "error"}, {"inputs": [], "name": "StringsInvalidChar", "type": "error"}], "bytecode": "0x60808060405234601757603a9081601d823930815050f35b600080fdfe600080fdfea2646970667358221220a26ceb9e22e9d3421f2f76b318ebc5f2324cc113daaa2f7b54162df0401c12e964736f6c63430008180033", "deployedBytecode": "0x600080fdfea2646970667358221220a26ceb9e22e9d3421f2f76b318ebc5f2324cc113daaa2f7b54162df0401c12e964736f6c63430008180033", "linkReferences": {}, "deployedLinkReferences": {}}