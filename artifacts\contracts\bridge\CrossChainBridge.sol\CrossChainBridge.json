{"_format": "hh-sol-artifact-1", "contractName": "CrossChainBridge", "sourceName": "contracts/bridge/CrossChainBridge.sol", "abi": [{"inputs": [{"internalType": "address[]", "name": "_validators", "type": "address[]"}, {"internalType": "uint256", "name": "_requiredSignatures", "type": "uint256"}, {"internalType": "address", "name": "_owner", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "name": "ECDSAInvalidSignature", "type": "error"}, {"inputs": [{"internalType": "uint256", "name": "length", "type": "uint256"}], "name": "ECDSAInvalidSignatureLength", "type": "error"}, {"inputs": [{"internalType": "bytes32", "name": "s", "type": "bytes32"}], "name": "ECDSAInvalidSignatureS", "type": "error"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "OwnableInvalidOwner", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "OwnableUnauthorizedAccount", "type": "error"}, {"inputs": [], "name": "ReentrancyGuardReentrantCall", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "chainId", "type": "uint256"}], "name": "ChainSupported", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "chainId", "type": "uint256"}], "name": "ChainUnsupported", "type": "event"}, {"anonymous": false, "inputs": [], "name": "ContractPaused", "type": "event"}, {"anonymous": false, "inputs": [], "name": "ContractUnpaused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "chainId", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "bridgeFee", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "token", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "tokenFee", "type": "uint256"}], "name": "FeesUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "newRequirement", "type": "uint256"}], "name": "RequiredSignaturesUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "token", "type": "address"}], "name": "TokenSupported", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "token", "type": "address"}], "name": "TokenUnsupported", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "transferId", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "token", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "TransferCompleted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "transferId", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "token", "type": "address"}, {"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": false, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "targetChainId", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "nonce", "type": "uint256"}], "name": "TransferInitiated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "validator", "type": "address"}], "name": "ValidatorAdded", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "validator", "type": "address"}], "name": "ValidatorRemoved", "type": "event"}, {"inputs": [], "name": "FEE_DENOMINATOR", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MAX_FEE_PERCENTAGE", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "chainId", "type": "uint256"}], "name": "addSupportedChain", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "name": "addSupportedToken", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "validator", "type": "address"}], "name": "addValidator", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "bridgeFees", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "uint256", "name": "targetChainId", "type": "uint256"}], "name": "calculateFees", "outputs": [{"internalType": "uint256", "name": "bridgeFee", "type": "uint256"}, {"internalType": "uint256", "name": "tokenFee", "type": "uint256"}, {"internalType": "uint256", "name": "totalAmount", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "transferId", "type": "bytes32"}, {"internalType": "address", "name": "token", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "uint256", "name": "sourceChainId", "type": "uint256"}, {"internalType": "bytes[]", "name": "signatures", "type": "bytes[]"}], "name": "completeTransfer", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "emergencyWithdraw", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "transferId", "type": "bytes32"}], "name": "getTransfer", "outputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "uint256", "name": "targetChainId", "type": "uint256"}, {"internalType": "uint256", "name": "nonce", "type": "uint256"}, {"internalType": "bool", "name": "completed", "type": "bool"}, {"internalType": "uint256", "name": "timestamp", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getValidators", "outputs": [{"internalType": "address[]", "name": "", "type": "address[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "targetChainId", "type": "uint256"}], "name": "initiateTransfer", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "pause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "paused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "name": "processedTransfers", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "chainId", "type": "uint256"}], "name": "removeSupported<PERSON><PERSON><PERSON>", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "name": "removeSupportedToken", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "validator", "type": "address"}], "name": "removeValidator", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "requiredSignatures", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "chainId", "type": "uint256"}, {"internalType": "uint256", "name": "fee", "type": "uint256"}], "name": "setBridgeFee", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "uint256", "name": "feePercentage", "type": "uint256"}], "name": "setTokenFee", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "<PERSON><PERSON><PERSON><PERSON>", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "supportedTokens", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "tokenFees", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "name": "transfers", "outputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "uint256", "name": "targetChainId", "type": "uint256"}, {"internalType": "uint256", "name": "nonce", "type": "uint256"}, {"internalType": "bool", "name": "completed", "type": "bool"}, {"internalType": "uint256", "name": "timestamp", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "unpause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "newRequirement", "type": "uint256"}], "name": "updateRequiredSignatures", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "userNonces", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "validatorList", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "validators", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "withdrawFees", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}