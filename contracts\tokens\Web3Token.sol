// SPDX-License-Identifier: MIT
pragma solidity ^0.8.24;

import "@openzeppelin/contracts/token/ERC20/ERC20.sol";
import "@openzeppelin/contracts/token/ERC20/extensions/ERC20Burnable.sol";
import "@openzeppelin/contracts/token/ERC20/extensions/ERC20Pausable.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/token/ERC20/extensions/ERC20Permit.sol";
import "@openzeppelin/contracts/token/ERC20/extensions/ERC20Votes.sol";
import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";

/**
 * @title Web3Token
 * @dev 完整的 ERC-20 代币实现，支持治理、质押、燃烧等功能
 * 
 * 功能特性：
 * - 标准 ERC-20 功能
 * - 可暂停/恢复转账
 * - 代币燃烧机制
 * - 治理投票功能
 * - 质押奖励系统
 * - 反重入保护
 */
contract Web3Token is 
    ERC20, 
    ERC20Burnable, 
    ERC20Pausable, 
    Ownable, 
    ERC20Permit, 
    ERC20Votes,
    ReentrancyGuard 
{
    // 最大供应量 (1亿代币)
    uint256 public constant MAX_SUPPLY = 100_000_000 * 10**18;
    
    // 质押相关状态
    struct StakeInfo {
        uint256 amount;          // 质押数量
        uint256 timestamp;       // 质押时间
        uint256 rewardDebt;      // 已领取奖励
    }
    
    mapping(address => StakeInfo) public stakes;
    uint256 public totalStaked;
    uint256 public rewardRate = 100; // 每秒每代币的奖励 (基点)
    uint256 public constant REWARD_PRECISION = 10000;
    
    // 事件
    event Staked(address indexed user, uint256 amount);
    event Unstaked(address indexed user, uint256 amount);
    event RewardClaimed(address indexed user, uint256 reward);
    event RewardRateUpdated(uint256 newRate);
    
    constructor(
        string memory name,
        string memory symbol,
        uint256 initialSupply,
        address initialOwner
    ) 
        ERC20(name, symbol) 
        Ownable(initialOwner)
        ERC20Permit(name)
    {
        require(initialSupply <= MAX_SUPPLY, "Initial supply exceeds max supply");
        _mint(initialOwner, initialSupply);
    }
    
    /**
     * @dev 铸造新代币 (仅所有者)
     */
    function mint(address to, uint256 amount) public onlyOwner {
        require(totalSupply() + amount <= MAX_SUPPLY, "Exceeds max supply");
        _mint(to, amount);
    }
    
    /**
     * @dev 暂停代币转账 (仅所有者)
     */
    function pause() public onlyOwner {
        _pause();
    }
    
    /**
     * @dev 恢复代币转账 (仅所有者)
     */
    function unpause() public onlyOwner {
        _unpause();
    }
    
    /**
     * @dev 质押代币获取奖励
     */
    function stake(uint256 amount) external nonReentrant whenNotPaused {
        require(amount > 0, "Cannot stake 0 tokens");
        require(balanceOf(msg.sender) >= amount, "Insufficient balance");
        
        // 先领取之前的奖励
        if (stakes[msg.sender].amount > 0) {
            _claimReward();
        }
        
        // 转移代币到合约
        _transfer(msg.sender, address(this), amount);
        
        // 更新质押信息
        stakes[msg.sender].amount += amount;
        stakes[msg.sender].timestamp = block.timestamp;
        totalStaked += amount;
        
        emit Staked(msg.sender, amount);
    }
    
    /**
     * @dev 取消质押
     */
    function unstake(uint256 amount) external nonReentrant {
        require(amount > 0, "Cannot unstake 0 tokens");
        require(stakes[msg.sender].amount >= amount, "Insufficient staked amount");
        
        // 先领取奖励
        _claimReward();
        
        // 更新质押信息
        stakes[msg.sender].amount -= amount;
        totalStaked -= amount;
        
        // 返还代币
        _transfer(address(this), msg.sender, amount);
        
        emit Unstaked(msg.sender, amount);
    }
    
    /**
     * @dev 领取质押奖励
     */
    function claimReward() external nonReentrant {
        _claimReward();
    }
    
    /**
     * @dev 内部函数：计算并发放奖励
     */
    function _claimReward() internal {
        uint256 reward = calculateReward(msg.sender);
        if (reward > 0) {
            stakes[msg.sender].rewardDebt += reward;
            stakes[msg.sender].timestamp = block.timestamp;
            
            // 铸造奖励代币 (如果不超过最大供应量)
            if (totalSupply() + reward <= MAX_SUPPLY) {
                _mint(msg.sender, reward);
                emit RewardClaimed(msg.sender, reward);
            }
        }
    }
    
    /**
     * @dev 计算用户的待领取奖励
     */
    function calculateReward(address user) public view returns (uint256) {
        StakeInfo memory userStake = stakes[user];
        if (userStake.amount == 0) {
            return 0;
        }
        
        uint256 stakingDuration = block.timestamp - userStake.timestamp;
        uint256 reward = (userStake.amount * rewardRate * stakingDuration) / REWARD_PRECISION;
        
        return reward;
    }
    
    /**
     * @dev 设置奖励率 (仅所有者)
     */
    function setRewardRate(uint256 newRate) external onlyOwner {
        require(newRate <= 1000, "Reward rate too high"); // 最大10%
        rewardRate = newRate;
        emit RewardRateUpdated(newRate);
    }
    
    /**
     * @dev 获取用户质押信息
     */
    function getStakeInfo(address user) external view returns (
        uint256 stakedAmount,
        uint256 stakeTimestamp,
        uint256 pendingReward
    ) {
        StakeInfo memory userStake = stakes[user];
        return (
            userStake.amount,
            userStake.timestamp,
            calculateReward(user)
        );
    }
    
    // 重写必要的函数以支持多重继承
    function _update(address from, address to, uint256 value)
        internal
        override(ERC20, ERC20Pausable, ERC20Votes)
    {
        super._update(from, to, value);
    }
    
    function nonces(address owner)
        public
        view
        override(ERC20Permit, Nonces)
        returns (uint256)
    {
        return super.nonces(owner);
    }
}
