import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useWeb3 } from './_app';
import { ethers } from 'ethers';
import contractsConfig from '../frontend/config/contracts.json';

const AMM_ABI = [
  "function tokenA() view returns (address)",
  "function tokenB() view returns (address)",
  "function reserveA() view returns (uint256)",
  "function reserveB() view returns (uint256)",
  "function totalSupply() view returns (uint256)",
  "function balanceOf(address) view returns (uint256)",
  "function getPrice() view returns (uint256, uint256)",
  "function getAmountOut(uint256, uint256, uint256) view returns (uint256)",
  "function getPoolInfo() view returns (uint256, uint256, uint256, uint256, uint256)",
  "function addLiquidity(uint256, uint256, uint256, uint256, address) returns (uint256, uint256, uint256)",
  "function removeLiquidity(uint256, uint256, uint256, address) returns (uint256, uint256)",
  "function swapAForB(uint256, uint256, address) returns (uint256)",
  "function swapBForA(uint256, uint256, address) returns (uint256)"
];

const TOKEN_ABI = [
  "function name() view returns (string)",
  "function symbol() view returns (string)",
  "function decimals() view returns (uint8)",
  "function balanceOf(address) view returns (uint256)",
  "function allowance(address, address) view returns (uint256)",
  "function approve(address, uint256) returns (bool)",
  "function transfer(address, uint256) returns (bool)"
];

export default function DeFi() {
  const { account, signer, isConnected } = useWeb3();
  const [ammContract, setAmmContract] = useState(null);
  const [tokenAContract, setTokenAContract] = useState(null);
  const [tokenBContract, setTokenBContract] = useState(null);
  const [poolInfo, setPoolInfo] = useState(null);
  const [userInfo, setUserInfo] = useState(null);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('swap');

  // 表单状态
  const [swapForm, setSwapForm] = useState({
    fromToken: 'A',
    fromAmount: '',
    toAmount: '',
    slippage: '0.5'
  });

  const [liquidityForm, setLiquidityForm] = useState({
    tokenAAmount: '',
    tokenBAmount: '',
    lpTokens: ''
  });

  useEffect(() => {
    if (isConnected && signer) {
      initializeContracts();
    }
  }, [isConnected, signer]);

  const initializeContracts = async () => {
    try {
      // 注意：这里需要实际部署 AMM 合约后更新地址
      const ammAddress = "******************************************"; // 需要部署后更新
      
      if (ammAddress === "******************************************") {
        console.log("AMM 合约尚未部署");
        return;
      }

      const amm = new ethers.Contract(ammAddress, AMM_ABI, signer);
      setAmmContract(amm);

      const tokenAAddress = await amm.tokenA();
      const tokenBAddress = await amm.tokenB();

      const tokenA = new ethers.Contract(tokenAAddress, TOKEN_ABI, signer);
      const tokenB = new ethers.Contract(tokenBAddress, TOKEN_ABI, signer);

      setTokenAContract(tokenA);
      setTokenBContract(tokenB);

      loadData(amm, tokenA, tokenB);
    } catch (error) {
      console.error('初始化合约失败:', error);
    }
  };

  const loadData = async (amm, tokenA, tokenB) => {
    setLoading(true);
    try {
      // 加载池子信息
      const [reserveA, reserveB, totalSupply, totalFeeA, totalFeeB] = await amm.getPoolInfo();
      const [priceAB, priceBA] = await amm.getPrice();

      // 加载代币信息
      const [nameA, symbolA, nameB, symbolB] = await Promise.all([
        tokenA.name(),
        tokenA.symbol(),
        tokenB.name(),
        tokenB.symbol()
      ]);

      setPoolInfo({
        reserveA: ethers.formatEther(reserveA),
        reserveB: ethers.formatEther(reserveB),
        totalSupply: ethers.formatEther(totalSupply),
        totalFeeA: ethers.formatEther(totalFeeA),
        totalFeeB: ethers.formatEther(totalFeeB),
        priceAB: ethers.formatEther(priceAB),
        priceBA: ethers.formatEther(priceBA),
        tokenA: { name: nameA, symbol: symbolA },
        tokenB: { name: nameB, symbol: symbolB }
      });

      // 加载用户信息
      const [balanceA, balanceB, lpBalance] = await Promise.all([
        tokenA.balanceOf(account),
        tokenB.balanceOf(account),
        amm.balanceOf(account)
      ]);

      setUserInfo({
        balanceA: ethers.formatEther(balanceA),
        balanceB: ethers.formatEther(balanceB),
        lpBalance: ethers.formatEther(lpBalance)
      });

    } catch (error) {
      console.error('加载数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const calculateSwapOutput = async (fromAmount, fromToken) => {
    if (!ammContract || !poolInfo || !fromAmount) return;

    try {
      const amount = ethers.parseEther(fromAmount);
      const reserveIn = fromToken === 'A' ? 
        ethers.parseEther(poolInfo.reserveA) : 
        ethers.parseEther(poolInfo.reserveB);
      const reserveOut = fromToken === 'A' ? 
        ethers.parseEther(poolInfo.reserveB) : 
        ethers.parseEther(poolInfo.reserveA);

      const output = await ammContract.getAmountOut(amount, reserveIn, reserveOut);
      return ethers.formatEther(output);
    } catch (error) {
      console.error('计算交换输出失败:', error);
      return '0';
    }
  };

  const handleSwapInputChange = async (value) => {
    setSwapForm({ ...swapForm, fromAmount: value });
    
    if (value && parseFloat(value) > 0) {
      const output = await calculateSwapOutput(value, swapForm.fromToken);
      setSwapForm(prev => ({ ...prev, fromAmount: value, toAmount: output }));
    } else {
      setSwapForm(prev => ({ ...prev, fromAmount: value, toAmount: '' }));
    }
  };

  const handleSwap = async () => {
    if (!ammContract || !tokenAContract || !tokenBContract || !swapForm.fromAmount) return;

    try {
      setLoading(true);
      
      const fromAmount = ethers.parseEther(swapForm.fromAmount);
      const minOutput = ethers.parseEther(swapForm.toAmount);
      const slippageAmount = minOutput * BigInt(Math.floor((100 - parseFloat(swapForm.slippage)) * 100)) / BigInt(10000);

      // 检查并批准代币
      const tokenContract = swapForm.fromToken === 'A' ? tokenAContract : tokenBContract;
      const allowance = await tokenContract.allowance(account, await ammContract.getAddress());
      
      if (allowance < fromAmount) {
        const approveTx = await tokenContract.approve(await ammContract.getAddress(), fromAmount);
        await approveTx.wait();
      }

      // 执行交换
      let tx;
      if (swapForm.fromToken === 'A') {
        tx = await ammContract.swapAForB(fromAmount, slippageAmount, account);
      } else {
        tx = await ammContract.swapBForA(fromAmount, slippageAmount, account);
      }
      
      await tx.wait();
      alert('交换成功!');
      
      setSwapForm({ ...swapForm, fromAmount: '', toAmount: '' });
      loadData(ammContract, tokenAContract, tokenBContract);
    } catch (error) {
      console.error('交换失败:', error);
      alert('交换失败: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleAddLiquidity = async () => {
    if (!ammContract || !tokenAContract || !tokenBContract || 
        !liquidityForm.tokenAAmount || !liquidityForm.tokenBAmount) return;

    try {
      setLoading(true);
      
      const amountA = ethers.parseEther(liquidityForm.tokenAAmount);
      const amountB = ethers.parseEther(liquidityForm.tokenBAmount);
      
      // 批准代币
      const allowanceA = await tokenAContract.allowance(account, await ammContract.getAddress());
      const allowanceB = await tokenBContract.allowance(account, await ammContract.getAddress());
      
      if (allowanceA < amountA) {
        const approveTx = await tokenAContract.approve(await ammContract.getAddress(), amountA);
        await approveTx.wait();
      }
      
      if (allowanceB < amountB) {
        const approveTx = await tokenBContract.approve(await ammContract.getAddress(), amountB);
        await approveTx.wait();
      }

      // 添加流动性
      const tx = await ammContract.addLiquidity(
        amountA,
        amountB,
        amountA * BigInt(95) / BigInt(100), // 5% 滑点
        amountB * BigInt(95) / BigInt(100),
        account
      );
      
      await tx.wait();
      alert('添加流动性成功!');
      
      setLiquidityForm({ tokenAAmount: '', tokenBAmount: '', lpTokens: '' });
      loadData(ammContract, tokenAContract, tokenBContract);
    } catch (error) {
      console.error('添加流动性失败:', error);
      alert('添加流动性失败: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleRemoveLiquidity = async () => {
    if (!ammContract || !liquidityForm.lpTokens) return;

    try {
      setLoading(true);
      
      const lpAmount = ethers.parseEther(liquidityForm.lpTokens);
      
      const tx = await ammContract.removeLiquidity(
        lpAmount,
        0, // 最小 tokenA 数量
        0, // 最小 tokenB 数量
        account
      );
      
      await tx.wait();
      alert('移除流动性成功!');
      
      setLiquidityForm({ tokenAAmount: '', tokenBAmount: '', lpTokens: '' });
      loadData(ammContract, tokenAContract, tokenBContract);
    } catch (error) {
      console.error('移除流动性失败:', error);
      alert('移除流动性失败: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  if (!isConnected) {
    return (
      <div className="container mx-auto px-4 py-8 text-center">
        <h1 className="text-3xl font-bold mb-4">DeFi 协议</h1>
        <p className="text-gray-600">请先连接钱包以使用 DeFi 功能</p>
      </div>
    );
  }

  // 如果 AMM 合约未部署，显示提示
  if (!ammContract) {
    return (
      <div className="container mx-auto px-4 py-8 text-center">
        <h1 className="text-3xl font-bold mb-4">DeFi 协议</h1>
        <div className="card max-w-md mx-auto">
          <h2 className="text-xl font-bold mb-4">🚧 开发中</h2>
          <p className="text-gray-600 mb-4">
            DeFi 协议功能正在开发中，包括：
          </p>
          <ul className="text-left space-y-2 mb-6">
            <li>• 自动做市商 (AMM)</li>
            <li>• 流动性挖矿</li>
            <li>• 代币交换</li>
            <li>• 流动性提供</li>
          </ul>
          <p className="text-sm text-gray-500">
            请等待合约部署完成后再使用此功能
          </p>
        </div>
      </div>
    );
  }

  return (
    <>
      <Head>
        <title>DeFi 协议 - Web3 生态系统</title>
      </Head>

      <div className="container mx-auto px-4 py-8">
        {/* 导航栏 */}
        <nav className="bg-white rounded-lg shadow-lg p-4 mb-8">
          <div className="flex justify-center space-x-6">
            <a href="/" className="text-gray-600 font-medium hover:text-primary-600">
              首页
            </a>
            <a href="/nft-marketplace" className="text-gray-600 font-medium hover:text-primary-600">
              NFT 市场
            </a>
            <a href="/defi" className="text-primary-600 font-medium hover:text-primary-800">
              DeFi 协议
            </a>
            <a href="/dao" className="text-gray-600 font-medium hover:text-primary-600">
              DAO 治理
            </a>
          </div>
        </nav>

        <h1 className="text-4xl font-bold text-center mb-8 gradient-text">
          💱 DeFi 协议
        </h1>

        {/* 池子信息 */}
        {poolInfo && (
          <div className="grid-responsive mb-8">
            <div className="card">
              <h2 className="text-2xl font-bold mb-4">流动性池</h2>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600">{poolInfo.tokenA.symbol} 储备:</span>
                  <span className="font-semibold">{parseFloat(poolInfo.reserveA).toLocaleString()}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">{poolInfo.tokenB.symbol} 储备:</span>
                  <span className="font-semibold">{parseFloat(poolInfo.reserveB).toLocaleString()}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">LP 代币总量:</span>
                  <span className="font-semibold">{parseFloat(poolInfo.totalSupply).toLocaleString()}</span>
                </div>
              </div>
            </div>

            <div className="card">
              <h2 className="text-2xl font-bold mb-4">价格信息</h2>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600">1 {poolInfo.tokenA.symbol} =</span>
                  <span className="font-semibold">{parseFloat(poolInfo.priceAB).toFixed(6)} {poolInfo.tokenB.symbol}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">1 {poolInfo.tokenB.symbol} =</span>
                  <span className="font-semibold">{parseFloat(poolInfo.priceBA).toFixed(6)} {poolInfo.tokenA.symbol}</span>
                </div>
              </div>
            </div>

            {userInfo && (
              <div className="card">
                <h2 className="text-2xl font-bold mb-4">我的资产</h2>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-600">{poolInfo.tokenA.symbol}:</span>
                    <span className="font-semibold">{parseFloat(userInfo.balanceA).toFixed(4)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">{poolInfo.tokenB.symbol}:</span>
                    <span className="font-semibold">{parseFloat(userInfo.balanceB).toFixed(4)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">LP 代币:</span>
                    <span className="font-semibold">{parseFloat(userInfo.lpBalance).toFixed(4)}</span>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        {/* 标签页导航 */}
        <div className="flex justify-center mb-8">
          <div className="bg-white rounded-lg p-1 shadow-lg">
            {['swap', 'liquidity'].map((tab) => (
              <button
                key={tab}
                onClick={() => setActiveTab(tab)}
                className={`px-6 py-2 rounded-md font-medium transition-colors ${
                  activeTab === tab
                    ? 'bg-primary-600 text-white'
                    : 'text-gray-600 hover:text-primary-600'
                }`}
              >
                {tab === 'swap' && '代币交换'}
                {tab === 'liquidity' && '流动性'}
              </button>
            ))}
          </div>
        </div>

        {/* 代币交换 */}
        {activeTab === 'swap' && poolInfo && (
          <div className="max-w-md mx-auto">
            <div className="card">
              <h2 className="text-2xl font-bold mb-6">代币交换</h2>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    从
                  </label>
                  <div className="flex space-x-2">
                    <select
                      value={swapForm.fromToken}
                      onChange={(e) => setSwapForm({...swapForm, fromToken: e.target.value})}
                      className="input-field w-24"
                    >
                      <option value="A">{poolInfo.tokenA.symbol}</option>
                      <option value="B">{poolInfo.tokenB.symbol}</option>
                    </select>
                    <input
                      type="number"
                      step="0.01"
                      value={swapForm.fromAmount}
                      onChange={(e) => handleSwapInputChange(e.target.value)}
                      placeholder="0.0"
                      className="input-field flex-1"
                    />
                  </div>
                </div>

                <div className="text-center">
                  <button
                    onClick={() => setSwapForm({
                      ...swapForm, 
                      fromToken: swapForm.fromToken === 'A' ? 'B' : 'A'
                    })}
                    className="bg-gray-100 hover:bg-gray-200 p-2 rounded-full"
                  >
                    ⇅
                  </button>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    到
                  </label>
                  <div className="flex space-x-2">
                    <div className="input-field w-24 bg-gray-50">
                      {swapForm.fromToken === 'A' ? poolInfo.tokenB.symbol : poolInfo.tokenA.symbol}
                    </div>
                    <input
                      type="text"
                      value={swapForm.toAmount}
                      readOnly
                      placeholder="0.0"
                      className="input-field flex-1 bg-gray-50"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    滑点容忍度 (%)
                  </label>
                  <input
                    type="number"
                    step="0.1"
                    value={swapForm.slippage}
                    onChange={(e) => setSwapForm({...swapForm, slippage: e.target.value})}
                    className="input-field"
                  />
                </div>

                <button
                  onClick={handleSwap}
                  disabled={loading || !swapForm.fromAmount || parseFloat(swapForm.fromAmount) <= 0}
                  className="btn-primary w-full disabled:opacity-50"
                >
                  {loading ? '交换中...' : '交换'}
                </button>
              </div>
            </div>
          </div>
        )}

        {/* 流动性 */}
        {activeTab === 'liquidity' && poolInfo && (
          <div className="max-w-2xl mx-auto">
            <div className="grid md:grid-cols-2 gap-6">
              {/* 添加流动性 */}
              <div className="card">
                <h2 className="text-xl font-bold mb-4">添加流动性</h2>
                
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      {poolInfo.tokenA.symbol} 数量
                    </label>
                    <input
                      type="number"
                      step="0.01"
                      value={liquidityForm.tokenAAmount}
                      onChange={(e) => setLiquidityForm({...liquidityForm, tokenAAmount: e.target.value})}
                      placeholder="0.0"
                      className="input-field"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      {poolInfo.tokenB.symbol} 数量
                    </label>
                    <input
                      type="number"
                      step="0.01"
                      value={liquidityForm.tokenBAmount}
                      onChange={(e) => setLiquidityForm({...liquidityForm, tokenBAmount: e.target.value})}
                      placeholder="0.0"
                      className="input-field"
                    />
                  </div>

                  <button
                    onClick={handleAddLiquidity}
                    disabled={loading || !liquidityForm.tokenAAmount || !liquidityForm.tokenBAmount}
                    className="btn-primary w-full disabled:opacity-50"
                  >
                    {loading ? '添加中...' : '添加流动性'}
                  </button>
                </div>
              </div>

              {/* 移除流动性 */}
              <div className="card">
                <h2 className="text-xl font-bold mb-4">移除流动性</h2>
                
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      LP 代币数量
                    </label>
                    <input
                      type="number"
                      step="0.01"
                      value={liquidityForm.lpTokens}
                      onChange={(e) => setLiquidityForm({...liquidityForm, lpTokens: e.target.value})}
                      placeholder="0.0"
                      className="input-field"
                    />
                    {userInfo && (
                      <p className="text-sm text-gray-600 mt-1">
                        可用: {parseFloat(userInfo.lpBalance).toFixed(4)} LP
                      </p>
                    )}
                  </div>

                  <button
                    onClick={handleRemoveLiquidity}
                    disabled={loading || !liquidityForm.lpTokens}
                    className="bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg w-full disabled:opacity-50"
                  >
                    {loading ? '移除中...' : '移除流动性'}
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </>
  );
}
