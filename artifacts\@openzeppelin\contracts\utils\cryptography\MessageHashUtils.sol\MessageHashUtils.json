{"_format": "hh-sol-artifact-1", "contractName": "MessageHashUtils", "sourceName": "@openzeppelin/contracts/utils/cryptography/MessageHashUtils.sol", "abi": [], "bytecode": "0x60808060405234601757603a9081601d823930815050f35b600080fdfe600080fdfea2646970667358221220fdb2563bc6a2525e390068a1e1d3a6ac567219145cd404589172e36ae1589a7964736f6c63430008180033", "deployedBytecode": "0x600080fdfea2646970667358221220fdb2563bc6a2525e390068a1e1d3a6ac567219145cd404589172e36ae1589a7964736f6c63430008180033", "linkReferences": {}, "deployedLinkReferences": {}}