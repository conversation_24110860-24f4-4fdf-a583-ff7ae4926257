/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/dao";
exports.ids = ["pages/dao"];
exports.modules = {

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fdao&preferredRegion=&absolutePagePath=.%2Fpages%5Cdao.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fdao&preferredRegion=&absolutePagePath=.%2Fpages%5Cdao.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./pages/_app.js\");\n/* harmony import */ var _pages_dao_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./pages\\dao.js */ \"./pages/dao.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__, _pages_dao_js__WEBPACK_IMPORTED_MODULE_5__]);\n([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__, _pages_dao_js__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_dao_js__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_dao_js__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_dao_js__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_dao_js__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_dao_js__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_dao_js__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_dao_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_dao_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_dao_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_dao_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_dao_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/dao\",\n        pathname: \"/dao\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default())\n    },\n    userland: _pages_dao_js__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fdao&preferredRegion=&absolutePagePath=.%2Fpages%5Cdao.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./pages/_app.js":
/*!***********************!*\
  !*** ./pages/_app.js ***!
  \***********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App),\n/* harmony export */   useWeb3: () => (/* binding */ useWeb3)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _frontend_styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../frontend/styles/globals.css */ \"./frontend/styles/globals.css\");\n/* harmony import */ var _frontend_styles_globals_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_frontend_styles_globals_css__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ethers */ \"ethers\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([ethers__WEBPACK_IMPORTED_MODULE_3__]);\nethers__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n// Web3 上下文\n\nconst Web3Context = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_2__.createContext)();\nconst useWeb3 = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_2__.useContext)(Web3Context);\n    if (!context) {\n        throw new Error(\"useWeb3 must be used within a Web3Provider\");\n    }\n    return context;\n};\nfunction Web3Provider({ children }) {\n    const [account, setAccount] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [provider, setProvider] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [signer, setSigner] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [chainId, setChainId] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [isConnecting, setIsConnecting] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // 连接钱包\n    const connectWallet = async ()=>{\n        if (typeof window.ethereum === \"undefined\") {\n            alert(\"请安装 MetaMask!\");\n            return;\n        }\n        setIsConnecting(true);\n        try {\n            // 请求账户访问\n            await window.ethereum.request({\n                method: \"eth_requestAccounts\"\n            });\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_3__.ethers.BrowserProvider(window.ethereum);\n            const signer = await provider.getSigner();\n            const address = await signer.getAddress();\n            const network = await provider.getNetwork();\n            setProvider(provider);\n            setSigner(signer);\n            setAccount(address);\n            setChainId(Number(network.chainId));\n            console.log(\"钱包连接成功:\", address);\n        } catch (error) {\n            console.error(\"连接钱包失败:\", error);\n            alert(\"连接钱包失败: \" + error.message);\n        } finally{\n            setIsConnecting(false);\n        }\n    };\n    // 断开钱包连接\n    const disconnectWallet = ()=>{\n        setAccount(null);\n        setProvider(null);\n        setSigner(null);\n        setChainId(null);\n    };\n    // 监听账户变化\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (typeof window.ethereum !== \"undefined\") {\n            window.ethereum.on(\"accountsChanged\", (accounts)=>{\n                if (accounts.length === 0) {\n                    disconnectWallet();\n                } else {\n                    connectWallet();\n                }\n            });\n            window.ethereum.on(\"chainChanged\", (chainId)=>{\n                setChainId(parseInt(chainId, 16));\n            });\n            // 检查是否已经连接\n            window.ethereum.request({\n                method: \"eth_accounts\"\n            }).then((accounts)=>{\n                if (accounts.length > 0) {\n                    connectWallet();\n                }\n            });\n        }\n        return ()=>{\n            if (typeof window.ethereum !== \"undefined\") {\n                window.ethereum.removeAllListeners(\"accountsChanged\");\n                window.ethereum.removeAllListeners(\"chainChanged\");\n            }\n        };\n    }, []);\n    const value = {\n        account,\n        provider,\n        signer,\n        chainId,\n        isConnecting,\n        connectWallet,\n        disconnectWallet,\n        isConnected: !!account\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Web3Context.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\_app.js\",\n        lineNumber: 108,\n        columnNumber: 5\n    }, this);\n}\nfunction App({ Component, pageProps }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Web3Provider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                ...pageProps\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\_app.js\",\n                lineNumber: 118,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\_app.js\",\n            lineNumber: 117,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\_app.js\",\n        lineNumber: 116,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/_app.js\n");

/***/ }),

/***/ "./pages/dao.js":
/*!**********************!*\
  !*** ./pages/dao.js ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DAO)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"next/head\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _app__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./_app */ \"./pages/_app.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ethers */ \"ethers\");\n/* harmony import */ var _frontend_config_contracts_json__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../frontend/config/contracts.json */ \"./frontend/config/contracts.json\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_app__WEBPACK_IMPORTED_MODULE_3__, ethers__WEBPACK_IMPORTED_MODULE_4__]);\n([_app__WEBPACK_IMPORTED_MODULE_3__, ethers__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\nconst DAO_ABI = [\n    \"function governanceToken() view returns (address)\",\n    \"function proposalCount() view returns (uint256)\",\n    \"function treasuryBalance() view returns (uint256)\",\n    \"function votingPeriod() view returns (uint256)\",\n    \"function proposalThreshold() view returns (uint256)\",\n    \"function quorumPercentage() view returns (uint256)\",\n    \"function createProposal(string description, uint8 proposalType, uint256 requestedAmount, address beneficiary) returns (uint256)\",\n    \"function vote(uint256 proposalId, bool support)\",\n    \"function executeProposal(uint256 proposalId)\",\n    \"function updateProposalState(uint256 proposalId)\",\n    \"function getProposal(uint256 proposalId) view returns (uint256, address, string, uint8, uint256, address, uint256, uint256, uint256, uint256, uint8, bool)\",\n    \"function getUserVote(uint256 proposalId, address user) view returns (bool, bool)\",\n    \"function depositFunds() payable\",\n    \"function memberContributions(address) view returns (uint256)\"\n];\nconst TOKEN_ABI = [\n    \"function balanceOf(address) view returns (uint256)\",\n    \"function totalSupply() view returns (uint256)\",\n    \"function symbol() view returns (string)\"\n];\nconst ProposalType = {\n    GENERAL: 0,\n    FUNDING: 1,\n    PARAMETER_CHANGE: 2\n};\nconst ProposalState = {\n    PENDING: 0,\n    ACTIVE: 1,\n    SUCCEEDED: 2,\n    DEFEATED: 3,\n    EXECUTED: 4\n};\nfunction DAO() {\n    const { account, signer, isConnected } = (0,_app__WEBPACK_IMPORTED_MODULE_3__.useWeb3)();\n    const [daoContract, setDaoContract] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [tokenContract, setTokenContract] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [daoInfo, setDaoInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [proposals, setProposals] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"overview\");\n    // 表单状态\n    const [newProposal, setNewProposal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        description: \"\",\n        type: ProposalType.GENERAL,\n        requestedAmount: \"\",\n        beneficiary: \"\"\n    });\n    const [depositAmount, setDepositAmount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"0.1\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isConnected && signer) {\n            initializeContracts();\n        }\n    }, [\n        isConnected,\n        signer\n    ]);\n    const initializeContracts = async ()=>{\n        try {\n            const daoAddress = _frontend_config_contracts_json__WEBPACK_IMPORTED_MODULE_5__.contracts.SimpleDAO;\n            const dao = new ethers__WEBPACK_IMPORTED_MODULE_4__.ethers.Contract(daoAddress, DAO_ABI, signer);\n            setDaoContract(dao);\n            const tokenAddress = await dao.governanceToken();\n            const token = new ethers__WEBPACK_IMPORTED_MODULE_4__.ethers.Contract(tokenAddress, TOKEN_ABI, signer);\n            setTokenContract(token);\n            loadDAOData(dao, token);\n        } catch (error) {\n            console.error(\"初始化合约失败:\", error);\n        }\n    };\n    const loadDAOData = async (dao, token)=>{\n        setLoading(true);\n        try {\n            // 加载 DAO 基本信息\n            const [proposalCount, treasuryBalance, votingPeriod, proposalThreshold, quorumPercentage, userBalance, totalSupply, symbol, userContribution] = await Promise.all([\n                dao.proposalCount(),\n                dao.treasuryBalance(),\n                dao.votingPeriod(),\n                dao.proposalThreshold(),\n                dao.quorumPercentage(),\n                token.balanceOf(account),\n                token.totalSupply(),\n                token.symbol(),\n                dao.memberContributions(account)\n            ]);\n            setDaoInfo({\n                proposalCount: proposalCount.toString(),\n                treasuryBalance: ethers__WEBPACK_IMPORTED_MODULE_4__.ethers.formatEther(treasuryBalance),\n                votingPeriod: Number(votingPeriod) / 86400,\n                proposalThreshold: ethers__WEBPACK_IMPORTED_MODULE_4__.ethers.formatEther(proposalThreshold),\n                quorumPercentage: quorumPercentage.toString(),\n                userBalance: ethers__WEBPACK_IMPORTED_MODULE_4__.ethers.formatEther(userBalance),\n                totalSupply: ethers__WEBPACK_IMPORTED_MODULE_4__.ethers.formatEther(totalSupply),\n                symbol: symbol,\n                userContribution: ethers__WEBPACK_IMPORTED_MODULE_4__.ethers.formatEther(userContribution)\n            });\n            // 加载提案\n            await loadProposals(dao, Number(proposalCount));\n        } catch (error) {\n            console.error(\"加载 DAO 数据失败:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const loadProposals = async (dao, count)=>{\n        const proposalsData = [];\n        for(let i = 0; i < count; i++){\n            try {\n                const proposal = await dao.getProposal(i);\n                const [voted, choice] = await dao.getUserVote(i, account);\n                proposalsData.push({\n                    id: i,\n                    proposer: proposal[1],\n                    description: proposal[2],\n                    type: Number(proposal[3]),\n                    requestedAmount: ethers__WEBPACK_IMPORTED_MODULE_4__.ethers.formatEther(proposal[4]),\n                    beneficiary: proposal[5],\n                    forVotes: ethers__WEBPACK_IMPORTED_MODULE_4__.ethers.formatEther(proposal[6]),\n                    againstVotes: ethers__WEBPACK_IMPORTED_MODULE_4__.ethers.formatEther(proposal[7]),\n                    startTime: new Date(Number(proposal[8]) * 1000),\n                    endTime: new Date(Number(proposal[9]) * 1000),\n                    state: Number(proposal[10]),\n                    executed: proposal[11],\n                    userVoted: voted,\n                    userChoice: choice\n                });\n            } catch (error) {\n                console.error(`加载提案 ${i} 失败:`, error);\n            }\n        }\n        setProposals(proposalsData.reverse()); // 最新的在前面\n    };\n    const handleCreateProposal = async ()=>{\n        if (!daoContract || !newProposal.description) return;\n        try {\n            setLoading(true);\n            const requestedAmount = newProposal.type === ProposalType.FUNDING ? ethers__WEBPACK_IMPORTED_MODULE_4__.ethers.parseEther(newProposal.requestedAmount || \"0\") : 0;\n            const beneficiary = newProposal.type === ProposalType.FUNDING ? newProposal.beneficiary : ethers__WEBPACK_IMPORTED_MODULE_4__.ethers.ZeroAddress;\n            const tx = await daoContract.createProposal(newProposal.description, newProposal.type, requestedAmount, beneficiary);\n            await tx.wait();\n            alert(\"提案创建成功!\");\n            setNewProposal({\n                description: \"\",\n                type: ProposalType.GENERAL,\n                requestedAmount: \"\",\n                beneficiary: \"\"\n            });\n            loadDAOData(daoContract, tokenContract);\n        } catch (error) {\n            console.error(\"创建提案失败:\", error);\n            alert(\"创建提案失败: \" + error.message);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleVote = async (proposalId, support)=>{\n        if (!daoContract) return;\n        try {\n            setLoading(true);\n            const tx = await daoContract.vote(proposalId, support);\n            await tx.wait();\n            alert(\"投票成功!\");\n            loadDAOData(daoContract, tokenContract);\n        } catch (error) {\n            console.error(\"投票失败:\", error);\n            alert(\"投票失败: \" + error.message);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleExecuteProposal = async (proposalId)=>{\n        if (!daoContract) return;\n        try {\n            setLoading(true);\n            const tx = await daoContract.executeProposal(proposalId);\n            await tx.wait();\n            alert(\"提案执行成功!\");\n            loadDAOData(daoContract, tokenContract);\n        } catch (error) {\n            console.error(\"执行提案失败:\", error);\n            alert(\"执行提案失败: \" + error.message);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleDeposit = async ()=>{\n        if (!daoContract || !depositAmount) return;\n        try {\n            setLoading(true);\n            const tx = await daoContract.depositFunds({\n                value: ethers__WEBPACK_IMPORTED_MODULE_4__.ethers.parseEther(depositAmount)\n            });\n            await tx.wait();\n            alert(\"存款成功!\");\n            loadDAOData(daoContract, tokenContract);\n        } catch (error) {\n            console.error(\"存款失败:\", error);\n            alert(\"存款失败: \" + error.message);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const getProposalTypeText = (type)=>{\n        switch(type){\n            case ProposalType.GENERAL:\n                return \"一般提案\";\n            case ProposalType.FUNDING:\n                return \"资金申请\";\n            case ProposalType.PARAMETER_CHANGE:\n                return \"参数修改\";\n            default:\n                return \"未知类型\";\n        }\n    };\n    const getProposalStateText = (state)=>{\n        switch(state){\n            case ProposalState.PENDING:\n                return \"待处理\";\n            case ProposalState.ACTIVE:\n                return \"投票中\";\n            case ProposalState.SUCCEEDED:\n                return \"通过\";\n            case ProposalState.DEFEATED:\n                return \"未通过\";\n            case ProposalState.EXECUTED:\n                return \"已执行\";\n            default:\n                return \"未知状态\";\n        }\n    };\n    const getProposalStateColor = (state)=>{\n        switch(state){\n            case ProposalState.PENDING:\n                return \"text-yellow-600\";\n            case ProposalState.ACTIVE:\n                return \"text-blue-600\";\n            case ProposalState.SUCCEEDED:\n                return \"text-green-600\";\n            case ProposalState.DEFEATED:\n                return \"text-red-600\";\n            case ProposalState.EXECUTED:\n                return \"text-purple-600\";\n            default:\n                return \"text-gray-600\";\n        }\n    };\n    if (!isConnected) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-8 text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-3xl font-bold mb-4\",\n                    children: \"DAO 治理\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                    lineNumber: 292,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-600\",\n                    children: \"请先连接钱包以使用 DAO 治理功能\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                    lineNumber: 293,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n            lineNumber: 291,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                    children: \"DAO 治理 - Web3 生态系统\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                    lineNumber: 301,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                lineNumber: 300,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"bg-white rounded-lg shadow-lg p-4 mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center space-x-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/\",\n                                    className: \"text-gray-600 font-medium hover:text-primary-600\",\n                                    children: \"首页\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                    lineNumber: 308,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/nft-marketplace\",\n                                    className: \"text-gray-600 font-medium hover:text-primary-600\",\n                                    children: \"NFT 市场\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                    lineNumber: 311,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/defi\",\n                                    className: \"text-gray-600 font-medium hover:text-primary-600\",\n                                    children: \"DeFi 协议\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                    lineNumber: 314,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/dao\",\n                                    className: \"text-primary-600 font-medium hover:text-primary-800\",\n                                    children: \"DAO 治理\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                    lineNumber: 317,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                            lineNumber: 307,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                        lineNumber: 306,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-4xl font-bold text-center mb-8 gradient-text\",\n                        children: \"\\uD83C\\uDFDB️ DAO 治理系统\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                        lineNumber: 323,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg p-1 shadow-lg\",\n                            children: [\n                                \"overview\",\n                                \"proposals\",\n                                \"create\",\n                                \"treasury\"\n                            ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setActiveTab(tab),\n                                    className: `px-6 py-2 rounded-md font-medium transition-colors ${activeTab === tab ? \"bg-primary-600 text-white\" : \"text-gray-600 hover:text-primary-600\"}`,\n                                    children: [\n                                        tab === \"overview\" && \"概览\",\n                                        tab === \"proposals\" && \"提案\",\n                                        tab === \"create\" && \"创建提案\",\n                                        tab === \"treasury\" && \"资金池\"\n                                    ]\n                                }, tab, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                    lineNumber: 331,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                            lineNumber: 329,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                        lineNumber: 328,\n                        columnNumber: 9\n                    }, this),\n                    activeTab === \"overview\" && daoInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid-responsive\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"card\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold mb-4\",\n                                        children: \"DAO 统计\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                        lineNumber: 353,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-600\",\n                                                        children: \"总提案数:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                        lineNumber: 356,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-semibold\",\n                                                        children: daoInfo.proposalCount\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                        lineNumber: 357,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                lineNumber: 355,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-600\",\n                                                        children: \"资金池:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                        lineNumber: 360,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-semibold\",\n                                                        children: [\n                                                            parseFloat(daoInfo.treasuryBalance).toFixed(4),\n                                                            \" ETH\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                        lineNumber: 361,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                lineNumber: 359,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-600\",\n                                                        children: \"投票期限:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                        lineNumber: 364,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-semibold\",\n                                                        children: [\n                                                            daoInfo.votingPeriod,\n                                                            \" 天\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                        lineNumber: 365,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                lineNumber: 363,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-600\",\n                                                        children: \"法定人数:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                        lineNumber: 368,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-semibold\",\n                                                        children: [\n                                                            daoInfo.quorumPercentage,\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                        lineNumber: 369,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                lineNumber: 367,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                        lineNumber: 354,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                lineNumber: 352,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"card\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold mb-4\",\n                                        children: \"我的治理权\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                        lineNumber: 375,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-600\",\n                                                        children: \"持有代币:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                        lineNumber: 378,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-semibold\",\n                                                        children: [\n                                                            parseFloat(daoInfo.userBalance).toFixed(2),\n                                                            \" \",\n                                                            daoInfo.symbol\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                        lineNumber: 379,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                lineNumber: 377,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-600\",\n                                                        children: \"投票权重:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                        lineNumber: 382,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-semibold\",\n                                                        children: [\n                                                            (parseFloat(daoInfo.userBalance) / parseFloat(daoInfo.totalSupply) * 100).toFixed(2),\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                        lineNumber: 383,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                lineNumber: 381,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-600\",\n                                                        children: \"贡献资金:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                        lineNumber: 388,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-semibold\",\n                                                        children: [\n                                                            parseFloat(daoInfo.userContribution).toFixed(4),\n                                                            \" ETH\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                        lineNumber: 389,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                lineNumber: 387,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-600\",\n                                                        children: \"提案阈值:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                        lineNumber: 392,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-semibold\",\n                                                        children: [\n                                                            daoInfo.proposalThreshold,\n                                                            \" \",\n                                                            daoInfo.symbol\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                        lineNumber: 393,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                lineNumber: 391,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                        lineNumber: 376,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                lineNumber: 374,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"card\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold mb-4\",\n                                        children: \"快速操作\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                        lineNumber: 399,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setActiveTab(\"create\"),\n                                                disabled: parseFloat(daoInfo.userBalance) < parseFloat(daoInfo.proposalThreshold),\n                                                className: \"btn-primary w-full disabled:opacity-50\",\n                                                children: \"创建提案\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                lineNumber: 401,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setActiveTab(\"treasury\"),\n                                                className: \"btn-secondary w-full\",\n                                                children: \"向资金池存款\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                lineNumber: 408,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                        lineNumber: 400,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                lineNumber: 398,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                        lineNumber: 351,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === \"proposals\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            proposals.map((proposal)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-start mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-xl font-bold mb-2\",\n                                                            children: [\n                                                                \"提案 #\",\n                                                                proposal.id\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                            lineNumber: 426,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: `inline-block px-2 py-1 rounded text-sm font-medium ${getProposalStateColor(proposal.state)}`,\n                                                            children: getProposalStateText(proposal.state)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                            lineNumber: 427,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"ml-2 inline-block px-2 py-1 rounded text-sm bg-gray-100\",\n                                                            children: getProposalTypeText(proposal.type)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                            lineNumber: 430,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                    lineNumber: 425,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-right text-sm text-gray-600\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: [\n                                                                \"提案者: \",\n                                                                proposal.proposer.slice(0, 6),\n                                                                \"...\",\n                                                                proposal.proposer.slice(-4)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                            lineNumber: 435,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: [\n                                                                \"结束时间: \",\n                                                                proposal.endTime.toLocaleDateString()\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                            lineNumber: 436,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                    lineNumber: 434,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                            lineNumber: 424,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-700 mb-4\",\n                                            children: proposal.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                            lineNumber: 440,\n                                            columnNumber: 17\n                                        }, this),\n                                        proposal.type === ProposalType.FUNDING && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-yellow-50 p-3 rounded mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"申请金额:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                            lineNumber: 445,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \" \",\n                                                        proposal.requestedAmount,\n                                                        \" ETH\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                    lineNumber: 444,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"受益人:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                            lineNumber: 448,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \" \",\n                                                        proposal.beneficiary\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                    lineNumber: 447,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                            lineNumber: 443,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-4 mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"支持票\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                            lineNumber: 455,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-lg font-bold text-green-600\",\n                                                            children: parseFloat(proposal.forVotes).toFixed(2)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                            lineNumber: 456,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                    lineNumber: 454,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"反对票\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                            lineNumber: 459,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-lg font-bold text-red-600\",\n                                                            children: parseFloat(proposal.againstVotes).toFixed(2)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                            lineNumber: 460,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                    lineNumber: 458,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                            lineNumber: 453,\n                                            columnNumber: 17\n                                        }, this),\n                                        proposal.state === ProposalState.ACTIVE && !proposal.userVoted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleVote(proposal.id, true),\n                                                    disabled: loading,\n                                                    className: \"btn-primary flex-1\",\n                                                    children: \"支持\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                    lineNumber: 466,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleVote(proposal.id, false),\n                                                    disabled: loading,\n                                                    className: \"bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg flex-1\",\n                                                    children: \"反对\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                    lineNumber: 473,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                            lineNumber: 465,\n                                            columnNumber: 19\n                                        }, this),\n                                        proposal.userVoted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-center text-sm text-gray-600\",\n                                            children: [\n                                                \"您已投票: \",\n                                                proposal.userChoice ? \"支持\" : \"反对\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                            lineNumber: 484,\n                                            columnNumber: 19\n                                        }, this),\n                                        proposal.state === ProposalState.SUCCEEDED && !proposal.executed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>handleExecuteProposal(proposal.id),\n                                            disabled: loading,\n                                            className: \"btn-primary w-full\",\n                                            children: \"执行提案\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                            lineNumber: 490,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, proposal.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                    lineNumber: 423,\n                                    columnNumber: 15\n                                }, this)),\n                            proposals.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: \"暂无提案\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                    lineNumber: 503,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                lineNumber: 502,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                        lineNumber: 421,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === \"create\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-2xl mx-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-bold mb-6\",\n                                    children: \"创建新提案\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                    lineNumber: 513,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                    children: \"提案类型\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                    lineNumber: 517,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    value: newProposal.type,\n                                                    onChange: (e)=>setNewProposal({\n                                                            ...newProposal,\n                                                            type: parseInt(e.target.value)\n                                                        }),\n                                                    className: \"input-field\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: ProposalType.GENERAL,\n                                                            children: \"一般提案\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                            lineNumber: 525,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: ProposalType.FUNDING,\n                                                            children: \"资金申请\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                            lineNumber: 526,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: ProposalType.PARAMETER_CHANGE,\n                                                            children: \"参数修改\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                            lineNumber: 527,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                    lineNumber: 520,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                            lineNumber: 516,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                    children: \"提案描述\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                    lineNumber: 532,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                    value: newProposal.description,\n                                                    onChange: (e)=>setNewProposal({\n                                                            ...newProposal,\n                                                            description: e.target.value\n                                                        }),\n                                                    placeholder: \"详细描述您的提案...\",\n                                                    rows: 4,\n                                                    className: \"input-field\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                    lineNumber: 535,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                            lineNumber: 531,\n                                            columnNumber: 17\n                                        }, this),\n                                        newProposal.type === ProposalType.FUNDING && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                            children: \"申请金额 (ETH)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                            lineNumber: 547,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"number\",\n                                                            step: \"0.01\",\n                                                            value: newProposal.requestedAmount,\n                                                            onChange: (e)=>setNewProposal({\n                                                                    ...newProposal,\n                                                                    requestedAmount: e.target.value\n                                                                }),\n                                                            placeholder: \"0.0\",\n                                                            className: \"input-field\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                            lineNumber: 550,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                    lineNumber: 546,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                            children: \"受益人地址\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                            lineNumber: 560,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: newProposal.beneficiary,\n                                                            onChange: (e)=>setNewProposal({\n                                                                    ...newProposal,\n                                                                    beneficiary: e.target.value\n                                                                }),\n                                                            placeholder: \"0x...\",\n                                                            className: \"input-field\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                            lineNumber: 563,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                    lineNumber: 559,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleCreateProposal,\n                                            disabled: loading || !newProposal.description || newProposal.type === ProposalType.FUNDING && (!newProposal.requestedAmount || !newProposal.beneficiary),\n                                            className: \"btn-primary w-full disabled:opacity-50\",\n                                            children: loading ? \"创建中...\" : \"创建提案\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                            lineNumber: 574,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                    lineNumber: 515,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                            lineNumber: 512,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                        lineNumber: 511,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === \"treasury\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-md mx-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-bold mb-4\",\n                                    children: \"向 DAO 资金池存款\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                    lineNumber: 591,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                    children: \"存款金额 (ETH)\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                    lineNumber: 594,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    step: \"0.01\",\n                                                    value: depositAmount,\n                                                    onChange: (e)=>setDepositAmount(e.target.value),\n                                                    className: \"input-field\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                                    lineNumber: 597,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                            lineNumber: 593,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleDeposit,\n                                            disabled: loading || !depositAmount || parseFloat(depositAmount) <= 0,\n                                            className: \"btn-primary w-full disabled:opacity-50\",\n                                            children: loading ? \"存款中...\" : `存入 ${depositAmount} ETH`\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                            lineNumber: 605,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 text-center\",\n                                            children: \"存款将增加您在 DAO 中的贡献记录\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                            lineNumber: 612,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                                    lineNumber: 592,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                            lineNumber: 590,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                        lineNumber: 589,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\dao.js\",\n                lineNumber: 304,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/dao.js\n");

/***/ }),

/***/ "./frontend/styles/globals.css":
/*!*************************************!*\
  !*** ./frontend/styles/globals.css ***!
  \*************************************/
/***/ (() => {



/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "next/head":
/*!****************************!*\
  !*** external "next/head" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/head");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "ethers":
/*!*************************!*\
  !*** external "ethers" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = import("ethers");;

/***/ }),

/***/ "./frontend/config/contracts.json":
/*!****************************************!*\
  !*** ./frontend/config/contracts.json ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = /*#__PURE__*/JSON.parse('{"contracts":{"SimpleToken":"******************************************","Web3NFT":"******************************************","NFTMarketplace":"******************************************","SimpleDAO":"******************************************"},"network":{"chainId":31337,"name":"localhost"}}');

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fdao&preferredRegion=&absolutePagePath=.%2Fpages%5Cdao.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();