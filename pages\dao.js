import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useWeb3 } from './_app';
import { ethers } from 'ethers';
import contractsConfig from '../frontend/config/contracts.json';

const DAO_ABI = [
  "function governanceToken() view returns (address)",
  "function proposalCount() view returns (uint256)",
  "function treasuryBalance() view returns (uint256)",
  "function votingPeriod() view returns (uint256)",
  "function proposalThreshold() view returns (uint256)",
  "function quorumPercentage() view returns (uint256)",
  "function createProposal(string description, uint8 proposalType, uint256 requestedAmount, address beneficiary) returns (uint256)",
  "function vote(uint256 proposalId, bool support)",
  "function executeProposal(uint256 proposalId)",
  "function updateProposalState(uint256 proposalId)",
  "function getProposal(uint256 proposalId) view returns (uint256, address, string, uint8, uint256, address, uint256, uint256, uint256, uint256, uint8, bool)",
  "function getUserVote(uint256 proposalId, address user) view returns (bool, bool)",
  "function depositFunds() payable",
  "function memberContributions(address) view returns (uint256)"
];

const TOKEN_ABI = [
  "function balanceOf(address) view returns (uint256)",
  "function totalSupply() view returns (uint256)",
  "function symbol() view returns (string)"
];

const ProposalType = {
  GENERAL: 0,
  FUNDING: 1,
  PARAMETER_CHANGE: 2
};

const ProposalState = {
  PENDING: 0,
  ACTIVE: 1,
  SUCCEEDED: 2,
  DEFEATED: 3,
  EXECUTED: 4
};

export default function DAO() {
  const { account, signer, isConnected } = useWeb3();
  const [daoContract, setDaoContract] = useState(null);
  const [tokenContract, setTokenContract] = useState(null);
  const [daoInfo, setDaoInfo] = useState(null);
  const [proposals, setProposals] = useState([]);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');

  // 表单状态
  const [newProposal, setNewProposal] = useState({
    description: '',
    type: ProposalType.GENERAL,
    requestedAmount: '',
    beneficiary: ''
  });
  const [depositAmount, setDepositAmount] = useState('0.1');

  useEffect(() => {
    if (isConnected && signer) {
      initializeContracts();
    }
  }, [isConnected, signer]);

  const initializeContracts = async () => {
    try {
      const daoAddress = contractsConfig.contracts.SimpleDAO;
      const dao = new ethers.Contract(daoAddress, DAO_ABI, signer);
      setDaoContract(dao);

      const tokenAddress = await dao.governanceToken();
      const token = new ethers.Contract(tokenAddress, TOKEN_ABI, signer);
      setTokenContract(token);

      loadDAOData(dao, token);
    } catch (error) {
      console.error('初始化合约失败:', error);
    }
  };

  const loadDAOData = async (dao, token) => {
    setLoading(true);
    try {
      // 加载 DAO 基本信息
      const [
        proposalCount,
        treasuryBalance,
        votingPeriod,
        proposalThreshold,
        quorumPercentage,
        userBalance,
        totalSupply,
        symbol,
        userContribution
      ] = await Promise.all([
        dao.proposalCount(),
        dao.treasuryBalance(),
        dao.votingPeriod(),
        dao.proposalThreshold(),
        dao.quorumPercentage(),
        token.balanceOf(account),
        token.totalSupply(),
        token.symbol(),
        dao.memberContributions(account)
      ]);

      setDaoInfo({
        proposalCount: proposalCount.toString(),
        treasuryBalance: ethers.formatEther(treasuryBalance),
        votingPeriod: Number(votingPeriod) / 86400, // 转换为天数
        proposalThreshold: ethers.formatEther(proposalThreshold),
        quorumPercentage: quorumPercentage.toString(),
        userBalance: ethers.formatEther(userBalance),
        totalSupply: ethers.formatEther(totalSupply),
        symbol: symbol,
        userContribution: ethers.formatEther(userContribution)
      });

      // 加载提案
      await loadProposals(dao, Number(proposalCount));
    } catch (error) {
      console.error('加载 DAO 数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadProposals = async (dao, count) => {
    const proposalsData = [];
    
    for (let i = 0; i < count; i++) {
      try {
        const proposal = await dao.getProposal(i);
        const [voted, choice] = await dao.getUserVote(i, account);
        
        proposalsData.push({
          id: i,
          proposer: proposal[1],
          description: proposal[2],
          type: Number(proposal[3]),
          requestedAmount: ethers.formatEther(proposal[4]),
          beneficiary: proposal[5],
          forVotes: ethers.formatEther(proposal[6]),
          againstVotes: ethers.formatEther(proposal[7]),
          startTime: new Date(Number(proposal[8]) * 1000),
          endTime: new Date(Number(proposal[9]) * 1000),
          state: Number(proposal[10]),
          executed: proposal[11],
          userVoted: voted,
          userChoice: choice
        });
      } catch (error) {
        console.error(`加载提案 ${i} 失败:`, error);
      }
    }
    
    setProposals(proposalsData.reverse()); // 最新的在前面
  };

  const handleCreateProposal = async () => {
    if (!daoContract || !newProposal.description) return;

    try {
      setLoading(true);
      
      const requestedAmount = newProposal.type === ProposalType.FUNDING 
        ? ethers.parseEther(newProposal.requestedAmount || '0')
        : 0;
      
      const beneficiary = newProposal.type === ProposalType.FUNDING 
        ? newProposal.beneficiary 
        : ethers.ZeroAddress;

      const tx = await daoContract.createProposal(
        newProposal.description,
        newProposal.type,
        requestedAmount,
        beneficiary
      );
      await tx.wait();

      alert('提案创建成功!');
      setNewProposal({
        description: '',
        type: ProposalType.GENERAL,
        requestedAmount: '',
        beneficiary: ''
      });
      
      loadDAOData(daoContract, tokenContract);
    } catch (error) {
      console.error('创建提案失败:', error);
      alert('创建提案失败: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleVote = async (proposalId, support) => {
    if (!daoContract) return;

    try {
      setLoading(true);
      const tx = await daoContract.vote(proposalId, support);
      await tx.wait();

      alert('投票成功!');
      loadDAOData(daoContract, tokenContract);
    } catch (error) {
      console.error('投票失败:', error);
      alert('投票失败: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleExecuteProposal = async (proposalId) => {
    if (!daoContract) return;

    try {
      setLoading(true);
      const tx = await daoContract.executeProposal(proposalId);
      await tx.wait();

      alert('提案执行成功!');
      loadDAOData(daoContract, tokenContract);
    } catch (error) {
      console.error('执行提案失败:', error);
      alert('执行提案失败: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleDeposit = async () => {
    if (!daoContract || !depositAmount) return;

    try {
      setLoading(true);
      const tx = await daoContract.depositFunds({
        value: ethers.parseEther(depositAmount)
      });
      await tx.wait();

      alert('存款成功!');
      loadDAOData(daoContract, tokenContract);
    } catch (error) {
      console.error('存款失败:', error);
      alert('存款失败: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const getProposalTypeText = (type) => {
    switch (type) {
      case ProposalType.GENERAL: return '一般提案';
      case ProposalType.FUNDING: return '资金申请';
      case ProposalType.PARAMETER_CHANGE: return '参数修改';
      default: return '未知类型';
    }
  };

  const getProposalStateText = (state) => {
    switch (state) {
      case ProposalState.PENDING: return '待处理';
      case ProposalState.ACTIVE: return '投票中';
      case ProposalState.SUCCEEDED: return '通过';
      case ProposalState.DEFEATED: return '未通过';
      case ProposalState.EXECUTED: return '已执行';
      default: return '未知状态';
    }
  };

  const getProposalStateColor = (state) => {
    switch (state) {
      case ProposalState.PENDING: return 'text-yellow-600';
      case ProposalState.ACTIVE: return 'text-blue-600';
      case ProposalState.SUCCEEDED: return 'text-green-600';
      case ProposalState.DEFEATED: return 'text-red-600';
      case ProposalState.EXECUTED: return 'text-purple-600';
      default: return 'text-gray-600';
    }
  };

  if (!isConnected) {
    return (
      <div className="container mx-auto px-4 py-8 text-center">
        <h1 className="text-3xl font-bold mb-4">DAO 治理</h1>
        <p className="text-gray-600">请先连接钱包以使用 DAO 治理功能</p>
      </div>
    );
  }

  return (
    <>
      <Head>
        <title>DAO 治理 - Web3 生态系统</title>
      </Head>

      <div className="container mx-auto px-4 py-8">
        {/* 导航栏 */}
        <nav className="bg-white rounded-lg shadow-lg p-4 mb-8">
          <div className="flex justify-center space-x-6">
            <a href="/" className="text-gray-600 font-medium hover:text-primary-600">
              首页
            </a>
            <a href="/nft-marketplace" className="text-gray-600 font-medium hover:text-primary-600">
              NFT 市场
            </a>
            <a href="/defi" className="text-gray-600 font-medium hover:text-primary-600">
              DeFi 协议
            </a>
            <a href="/dao" className="text-primary-600 font-medium hover:text-primary-800">
              DAO 治理
            </a>
          </div>
        </nav>

        <h1 className="text-4xl font-bold text-center mb-8 gradient-text">
          🏛️ DAO 治理系统
        </h1>

        {/* 标签页导航 */}
        <div className="flex justify-center mb-8">
          <div className="bg-white rounded-lg p-1 shadow-lg">
            {['overview', 'proposals', 'create', 'treasury'].map((tab) => (
              <button
                key={tab}
                onClick={() => setActiveTab(tab)}
                className={`px-6 py-2 rounded-md font-medium transition-colors ${
                  activeTab === tab
                    ? 'bg-primary-600 text-white'
                    : 'text-gray-600 hover:text-primary-600'
                }`}
              >
                {tab === 'overview' && '概览'}
                {tab === 'proposals' && '提案'}
                {tab === 'create' && '创建提案'}
                {tab === 'treasury' && '资金池'}
              </button>
            ))}
          </div>
        </div>

        {/* 概览 */}
        {activeTab === 'overview' && daoInfo && (
          <div className="grid-responsive">
            <div className="card">
              <h2 className="text-2xl font-bold mb-4">DAO 统计</h2>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600">总提案数:</span>
                  <span className="font-semibold">{daoInfo.proposalCount}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">资金池:</span>
                  <span className="font-semibold">{parseFloat(daoInfo.treasuryBalance).toFixed(4)} ETH</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">投票期限:</span>
                  <span className="font-semibold">{daoInfo.votingPeriod} 天</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">法定人数:</span>
                  <span className="font-semibold">{daoInfo.quorumPercentage}%</span>
                </div>
              </div>
            </div>

            <div className="card">
              <h2 className="text-2xl font-bold mb-4">我的治理权</h2>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600">持有代币:</span>
                  <span className="font-semibold">{parseFloat(daoInfo.userBalance).toFixed(2)} {daoInfo.symbol}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">投票权重:</span>
                  <span className="font-semibold">
                    {((parseFloat(daoInfo.userBalance) / parseFloat(daoInfo.totalSupply)) * 100).toFixed(2)}%
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">贡献资金:</span>
                  <span className="font-semibold">{parseFloat(daoInfo.userContribution).toFixed(4)} ETH</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">提案阈值:</span>
                  <span className="font-semibold">{daoInfo.proposalThreshold} {daoInfo.symbol}</span>
                </div>
              </div>
            </div>

            <div className="card">
              <h2 className="text-2xl font-bold mb-4">快速操作</h2>
              <div className="space-y-4">
                <button
                  onClick={() => setActiveTab('create')}
                  disabled={parseFloat(daoInfo.userBalance) < parseFloat(daoInfo.proposalThreshold)}
                  className="btn-primary w-full disabled:opacity-50"
                >
                  创建提案
                </button>
                <button
                  onClick={() => setActiveTab('treasury')}
                  className="btn-secondary w-full"
                >
                  向资金池存款
                </button>
              </div>
            </div>
          </div>
        )}

        {/* 提案列表 */}
        {activeTab === 'proposals' && (
          <div className="space-y-6">
            {proposals.map((proposal) => (
              <div key={proposal.id} className="card">
                <div className="flex justify-between items-start mb-4">
                  <div>
                    <h3 className="text-xl font-bold mb-2">提案 #{proposal.id}</h3>
                    <span className={`inline-block px-2 py-1 rounded text-sm font-medium ${getProposalStateColor(proposal.state)}`}>
                      {getProposalStateText(proposal.state)}
                    </span>
                    <span className="ml-2 inline-block px-2 py-1 rounded text-sm bg-gray-100">
                      {getProposalTypeText(proposal.type)}
                    </span>
                  </div>
                  <div className="text-right text-sm text-gray-600">
                    <p>提案者: {proposal.proposer.slice(0, 6)}...{proposal.proposer.slice(-4)}</p>
                    <p>结束时间: {proposal.endTime.toLocaleDateString()}</p>
                  </div>
                </div>

                <p className="text-gray-700 mb-4">{proposal.description}</p>

                {proposal.type === ProposalType.FUNDING && (
                  <div className="bg-yellow-50 p-3 rounded mb-4">
                    <p className="text-sm">
                      <strong>申请金额:</strong> {proposal.requestedAmount} ETH
                    </p>
                    <p className="text-sm">
                      <strong>受益人:</strong> {proposal.beneficiary}
                    </p>
                  </div>
                )}

                <div className="grid grid-cols-2 gap-4 mb-4">
                  <div className="text-center">
                    <p className="text-sm text-gray-600">支持票</p>
                    <p className="text-lg font-bold text-green-600">{parseFloat(proposal.forVotes).toFixed(2)}</p>
                  </div>
                  <div className="text-center">
                    <p className="text-sm text-gray-600">反对票</p>
                    <p className="text-lg font-bold text-red-600">{parseFloat(proposal.againstVotes).toFixed(2)}</p>
                  </div>
                </div>

                {proposal.state === ProposalState.ACTIVE && !proposal.userVoted && (
                  <div className="flex space-x-2">
                    <button
                      onClick={() => handleVote(proposal.id, true)}
                      disabled={loading}
                      className="btn-primary flex-1"
                    >
                      支持
                    </button>
                    <button
                      onClick={() => handleVote(proposal.id, false)}
                      disabled={loading}
                      className="bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg flex-1"
                    >
                      反对
                    </button>
                  </div>
                )}

                {proposal.userVoted && (
                  <p className="text-center text-sm text-gray-600">
                    您已投票: {proposal.userChoice ? '支持' : '反对'}
                  </p>
                )}

                {proposal.state === ProposalState.SUCCEEDED && !proposal.executed && (
                  <button
                    onClick={() => handleExecuteProposal(proposal.id)}
                    disabled={loading}
                    className="btn-primary w-full"
                  >
                    执行提案
                  </button>
                )}
              </div>
            ))}

            {proposals.length === 0 && (
              <div className="text-center py-8">
                <p className="text-gray-600">暂无提案</p>
              </div>
            )}
          </div>
        )}

        {/* 创建提案 */}
        {activeTab === 'create' && (
          <div className="max-w-2xl mx-auto">
            <div className="card">
              <h2 className="text-2xl font-bold mb-6">创建新提案</h2>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    提案类型
                  </label>
                  <select
                    value={newProposal.type}
                    onChange={(e) => setNewProposal({...newProposal, type: parseInt(e.target.value)})}
                    className="input-field"
                  >
                    <option value={ProposalType.GENERAL}>一般提案</option>
                    <option value={ProposalType.FUNDING}>资金申请</option>
                    <option value={ProposalType.PARAMETER_CHANGE}>参数修改</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    提案描述
                  </label>
                  <textarea
                    value={newProposal.description}
                    onChange={(e) => setNewProposal({...newProposal, description: e.target.value})}
                    placeholder="详细描述您的提案..."
                    rows={4}
                    className="input-field"
                  />
                </div>

                {newProposal.type === ProposalType.FUNDING && (
                  <>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        申请金额 (ETH)
                      </label>
                      <input
                        type="number"
                        step="0.01"
                        value={newProposal.requestedAmount}
                        onChange={(e) => setNewProposal({...newProposal, requestedAmount: e.target.value})}
                        placeholder="0.0"
                        className="input-field"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        受益人地址
                      </label>
                      <input
                        type="text"
                        value={newProposal.beneficiary}
                        onChange={(e) => setNewProposal({...newProposal, beneficiary: e.target.value})}
                        placeholder="0x..."
                        className="input-field"
                      />
                    </div>
                  </>
                )}

                <button
                  onClick={handleCreateProposal}
                  disabled={loading || !newProposal.description || 
                    (newProposal.type === ProposalType.FUNDING && (!newProposal.requestedAmount || !newProposal.beneficiary))}
                  className="btn-primary w-full disabled:opacity-50"
                >
                  {loading ? '创建中...' : '创建提案'}
                </button>
              </div>
            </div>
          </div>
        )}

        {/* 资金池 */}
        {activeTab === 'treasury' && (
          <div className="max-w-md mx-auto">
            <div className="card">
              <h2 className="text-2xl font-bold mb-4">向 DAO 资金池存款</h2>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    存款金额 (ETH)
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    value={depositAmount}
                    onChange={(e) => setDepositAmount(e.target.value)}
                    className="input-field"
                  />
                </div>
                <button
                  onClick={handleDeposit}
                  disabled={loading || !depositAmount || parseFloat(depositAmount) <= 0}
                  className="btn-primary w-full disabled:opacity-50"
                >
                  {loading ? '存款中...' : `存入 ${depositAmount} ETH`}
                </button>
                <p className="text-sm text-gray-600 text-center">
                  存款将增加您在 DAO 中的贡献记录
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </>
  );
}
