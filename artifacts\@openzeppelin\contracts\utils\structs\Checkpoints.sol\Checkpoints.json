{"_format": "hh-sol-artifact-1", "contractName": "Checkpoints", "sourceName": "@openzeppelin/contracts/utils/structs/Checkpoints.sol", "abi": [{"inputs": [], "name": "CheckpointUnorderedInsertion", "type": "error"}], "bytecode": "0x60808060405234601757603a9081601d823930815050f35b600080fdfe600080fdfea2646970667358221220ad98723cc2096d3261c3d65a8a8ed7d5f9b02c7ce77729510f3dd7164d36aca764736f6c63430008180033", "deployedBytecode": "0x600080fdfea2646970667358221220ad98723cc2096d3261c3d65a8a8ed7d5f9b02c7ce77729510f3dd7164d36aca764736f6c63430008180033", "linkReferences": {}, "deployedLinkReferences": {}}