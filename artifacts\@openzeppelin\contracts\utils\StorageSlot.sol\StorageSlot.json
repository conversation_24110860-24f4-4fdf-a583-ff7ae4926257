{"_format": "hh-sol-artifact-1", "contractName": "StorageSlot", "sourceName": "@openzeppelin/contracts/utils/StorageSlot.sol", "abi": [], "bytecode": "0x60808060405234601757603a9081601d823930815050f35b600080fdfe600080fdfea2646970667358221220349fad931e95b8b8d069aea6857f4fc049eea29c313ad774ba26f08f6d7aaa3964736f6c63430008180033", "deployedBytecode": "0x600080fdfea2646970667358221220349fad931e95b8b8d069aea6857f4fc049eea29c313ad774ba26f08f6d7aaa3964736f6c63430008180033", "linkReferences": {}, "deployedLinkReferences": {}}