{"timestamp": "2025-08-06T00:49:19.110Z", "summary": {"critical": 0, "high": 3, "medium": 0, "low": 0, "info": 0}, "issues": [{"severity": "HIGH", "contract": "SimpleToken", "issue": "ERC20 Audit Error", "description": "ERC20审计失败: could not decode result data (value=\"0x\", info={ \"method\": \"name\", \"signature\": \"name()\" }, code=BAD_DATA, version=6.15.0)", "recommendation": "检查合约是否正确实现了ERC20标准", "timestamp": "2025-08-06T00:49:19.088Z"}, {"severity": "HIGH", "contract": "Web3NFT", "issue": "NFT Audit Error", "description": "NFT审计失败: could not decode result data (value=\"0x\", info={ \"method\": \"name\", \"signature\": \"name()\" }, code=BAD_DATA, version=6.15.0)", "recommendation": "检查NFT合约是否正确实现了ERC721标准", "timestamp": "2025-08-06T00:49:19.095Z"}, {"severity": "HIGH", "contract": "SimpleDAO", "issue": "DAO Audit Error", "description": "DAO审计失败: could not decode result data (value=\"0x\", info={ \"method\": \"proposalCount\", \"signature\": \"proposalCount()\" }, code=BAD_DATA, version=6.15.0)", "recommendation": "检查DAO合约的基本功能是否正常", "timestamp": "2025-08-06T00:49:19.107Z"}], "warnings": [], "info": []}