# 📚 API 文档

本文档详细介绍了 Web3 生态系统中所有智能合约的接口和前端 API。

## 📋 目录

- [智能合约 API](#智能合约-api)
- [前端工具函数](#前端工具函数)
- [IPFS 工具](#ipfs-工具)
- [错误处理](#错误处理)
- [示例代码](#示例代码)

## 🔗 智能合约 API

### SimpleToken (ERC-20)

#### 基本信息
```solidity
function name() external view returns (string memory)
function symbol() external view returns (string memory)
function decimals() external view returns (uint8)
function totalSupply() external view returns (uint256)
function MAX_SUPPLY() external view returns (uint256)
```

#### 余额和转账
```solidity
function balanceOf(address account) external view returns (uint256)
function transfer(address to, uint256 amount) external returns (bool)
function transferFrom(address from, address to, uint256 amount) external returns (bool)
```

#### 授权
```solidity
function allowance(address owner, address spender) external view returns (uint256)
function approve(address spender, uint256 amount) external returns (bool)
```

#### 铸造和燃烧 (仅所有者)
```solidity
function mint(address to, uint256 amount) external onlyOwner
function burn(uint256 amount) external
```

#### 事件
```solidity
event Transfer(address indexed from, address indexed to, uint256 value)
event Approval(address indexed owner, address indexed spender, uint256 value)
```

### Web3NFT (ERC-721)

#### 基本信息
```solidity
function name() external view returns (string memory)
function symbol() external view returns (string memory)
function totalSupply() external view returns (uint256)
function MAX_SUPPLY() external view returns (uint256)
function mintPrice() external view returns (uint256)
```

#### NFT 操作
```solidity
function mint(uint256 quantity) external payable
function tokenURI(uint256 tokenId) external view returns (string memory)
function ownerOf(uint256 tokenId) external view returns (address)
function balanceOf(address owner) external view returns (uint256)
```

#### 铸造阶段管理
```solidity
enum MintPhase { CLOSED, WHITELIST, PUBLIC }
function setMintPhase(MintPhase phase) external onlyOwner
function addToWhitelist(address[] calldata addresses) external onlyOwner
```

#### 版税 (EIP-2981)
```solidity
function royaltyInfo(uint256 tokenId, uint256 salePrice) 
    external view returns (address receiver, uint256 royaltyAmount)
function setRoyaltyInfo(address receiver, uint96 feeNumerator) external onlyOwner
```

### SimpleDAO

#### 治理参数
```solidity
function governanceToken() external view returns (address)
function proposalCount() external view returns (uint256)
function treasuryBalance() external view returns (uint256)
function votingPeriod() external view returns (uint256)
function proposalThreshold() external view returns (uint256)
function quorumPercentage() external view returns (uint256)
```

#### 提案管理
```solidity
enum ProposalType { GENERAL, FUNDING, PARAMETER_CHANGE }
enum ProposalState { PENDING, ACTIVE, SUCCEEDED, DEFEATED, EXECUTED }

function createProposal(
    string memory description,
    ProposalType proposalType,
    uint256 requestedAmount,
    address beneficiary
) external returns (uint256)

function vote(uint256 proposalId, bool support) external
function executeProposal(uint256 proposalId) external
function updateProposalState(uint256 proposalId) external
```

#### 资金管理
```solidity
function depositFunds() external payable
function memberContributions(address member) external view returns (uint256)
```

#### 查询函数
```solidity
function getProposal(uint256 proposalId) external view returns (
    uint256 id,
    address proposer,
    string memory description,
    ProposalType proposalType,
    uint256 requestedAmount,
    address beneficiary,
    uint256 forVotes,
    uint256 againstVotes,
    uint256 startTime,
    uint256 endTime,
    ProposalState state,
    bool executed
)

function getUserVote(uint256 proposalId, address user) 
    external view returns (bool voted, bool choice)
```

### NFTMarketplace

#### 固定价格销售
```solidity
function listItem(address nftContract, uint256 tokenId, uint256 price) external
function buyItem(bytes32 listingId) external payable
function cancelListing(bytes32 listingId) external
```

#### 拍卖
```solidity
function createAuction(
    address nftContract,
    uint256 tokenId,
    uint256 startingPrice,
    uint256 duration
) external

function placeBid(bytes32 auctionId) external payable
function endAuction(bytes32 auctionId) external
```

#### 查询函数
```solidity
function getActiveListings() external view returns (bytes32[] memory)
function getActiveAuctions() external view returns (bytes32[] memory)
function pendingReturns(address user) external view returns (uint256)
```

### CrossChainBridge

#### 跨链转账
```solidity
function initiateTransfer(
    address token,
    uint256 amount,
    address to,
    uint256 targetChainId
) external payable

function completeTransfer(
    bytes32 transferId,
    address token,
    address to,
    uint256 amount,
    uint256 sourceChainId,
    bytes[] calldata signatures
) external
```

#### 配置管理
```solidity
function addSupportedChain(uint256 chainId) external onlyOwner
function addSupportedToken(address token) external onlyOwner
function setBridgeFee(uint256 chainId, uint256 fee) external onlyOwner
function setTokenFee(address token, uint256 feePercentage) external onlyOwner
```

#### 验证者管理
```solidity
function addValidator(address validator) external onlyOwner
function removeValidator(address validator) external onlyOwner
function updateRequiredSignatures(uint256 newRequirement) external onlyOwner
```

## 🛠️ 前端工具函数

### Web3 连接

```javascript
// 连接钱包
const { connectWallet, disconnectWallet, isConnected } = useWeb3();

// 获取账户信息
const { account, chainId, provider, signer } = useWeb3();

// 切换网络
const switchNetwork = async (chainId) => {
  await window.ethereum.request({
    method: 'wallet_switchEthereumChain',
    params: [{ chainId: `0x${chainId.toString(16)}` }],
  });
};
```

### 合约交互

```javascript
// 创建合约实例
const contract = new ethers.Contract(address, abi, signer);

// 调用只读函数
const balance = await contract.balanceOf(account);

// 发送交易
const tx = await contract.transfer(recipient, amount);
await tx.wait();

// 监听事件
contract.on('Transfer', (from, to, amount) => {
  console.log(`Transfer: ${from} -> ${to}, Amount: ${ethers.formatEther(amount)}`);
});
```

### 错误处理

```javascript
try {
  const tx = await contract.someFunction();
  await tx.wait();
} catch (error) {
  if (error.code === 'ACTION_REJECTED') {
    console.log('用户拒绝了交易');
  } else if (error.code === 'INSUFFICIENT_FUNDS') {
    console.log('余额不足');
  } else {
    console.error('交易失败:', error.message);
  }
}
```

## 📁 IPFS 工具

### 文件上传

```javascript
import { uploadFileToIPFS, uploadJSONToIPFS } from '../utils/ipfs';

// 上传文件
const hash = await uploadFileToIPFS(file);
console.log('IPFS Hash:', hash);

// 上传 JSON 数据
const metadata = {
  name: "My NFT",
  description: "A unique NFT",
  image: "ipfs://QmXXXXXX"
};
const metadataHash = await uploadJSONToIPFS(metadata);
```

### 文件检索

```javascript
import { getFileFromIPFS, getJSONFromIPFS } from '../utils/ipfs';

// 获取文件
const fileData = await getFileFromIPFS(hash);

// 获取 JSON 数据
const jsonData = await getJSONFromIPFS(hash);
```

### NFT 元数据

```javascript
import { createNFTMetadata } from '../utils/ipfs';

const metadata = {
  name: "Awesome NFT",
  description: "This is an awesome NFT",
  image: "QmImageHash",
  attributes: [
    { trait_type: "Color", value: "Blue" },
    { trait_type: "Rarity", value: "Rare" }
  ]
};

const metadataHash = await createNFTMetadata(metadata);
```

## ⚠️ 错误处理

### 常见错误代码

```javascript
const ERROR_CODES = {
  // 用户错误
  ACTION_REJECTED: '用户拒绝了操作',
  INSUFFICIENT_FUNDS: '余额不足',
  UNPREDICTABLE_GAS_LIMIT: 'Gas 估算失败',
  
  // 网络错误
  NETWORK_ERROR: '网络连接错误',
  TIMEOUT: '请求超时',
  
  // 合约错误
  CALL_EXCEPTION: '合约调用失败',
  INVALID_ARGUMENT: '参数无效',
  
  // 自定义错误
  CONTRACT_NOT_DEPLOYED: '合约未部署',
  INVALID_CHAIN: '不支持的网络',
};
```

### 错误处理工具

```javascript
export const handleContractError = (error) => {
  if (error.code === 'ACTION_REJECTED') {
    return { type: 'warning', message: '操作被用户取消' };
  }
  
  if (error.code === 'INSUFFICIENT_FUNDS') {
    return { type: 'error', message: '余额不足，请检查账户余额' };
  }
  
  if (error.message.includes('execution reverted')) {
    const reason = error.message.split('execution reverted: ')[1];
    return { type: 'error', message: `交易失败: ${reason}` };
  }
  
  return { type: 'error', message: '未知错误，请重试' };
};
```

## 💡 示例代码

### 完整的代币转账示例

```javascript
import { ethers } from 'ethers';
import { useWeb3 } from '../hooks/useWeb3';

const TokenTransfer = () => {
  const { signer, account } = useWeb3();
  const [loading, setLoading] = useState(false);

  const transferTokens = async (recipient, amount) => {
    setLoading(true);
    try {
      // 创建合约实例
      const contract = new ethers.Contract(
        TOKEN_ADDRESS,
        TOKEN_ABI,
        signer
      );

      // 检查余额
      const balance = await contract.balanceOf(account);
      const transferAmount = ethers.parseEther(amount);
      
      if (balance < transferAmount) {
        throw new Error('余额不足');
      }

      // 发送交易
      const tx = await contract.transfer(recipient, transferAmount);
      
      // 等待确认
      const receipt = await tx.wait();
      
      console.log('交易成功:', receipt.hash);
      return receipt;
      
    } catch (error) {
      console.error('转账失败:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      {/* UI 组件 */}
    </div>
  );
};
```

### NFT 铸造示例

```javascript
const mintNFT = async (quantity) => {
  try {
    const contract = new ethers.Contract(NFT_ADDRESS, NFT_ABI, signer);
    
    // 获取铸造价格
    const mintPrice = await contract.mintPrice();
    const totalCost = mintPrice * BigInt(quantity);
    
    // 铸造 NFT
    const tx = await contract.mint(quantity, { value: totalCost });
    await tx.wait();
    
    console.log('NFT 铸造成功');
  } catch (error) {
    console.error('铸造失败:', error);
  }
};
```

### DAO 投票示例

```javascript
const voteOnProposal = async (proposalId, support) => {
  try {
    const contract = new ethers.Contract(DAO_ADDRESS, DAO_ABI, signer);
    
    // 检查投票权
    const votingPower = await governanceToken.balanceOf(account);
    if (votingPower === 0n) {
      throw new Error('没有投票权');
    }
    
    // 投票
    const tx = await contract.vote(proposalId, support);
    await tx.wait();
    
    console.log('投票成功');
  } catch (error) {
    console.error('投票失败:', error);
  }
};
```

---

📖 更多详细信息请参考各个合约的源代码和测试文件。
