import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useWeb3 } from './_app';
import { ethers } from 'ethers';

const NFT_ABI = [
  "function name() view returns (string)",
  "function symbol() view returns (string)",
  "function tokenURI(uint256) view returns (string)",
  "function ownerOf(uint256) view returns (address)",
  "function balanceOf(address) view returns (uint256)",
  "function tokenOfOwnerByIndex(address, uint256) view returns (uint256)",
  "function mint(uint256) payable",
  "function mintPrice() view returns (uint256)",
  "function totalSupply() view returns (uint256)",
  "function approve(address, uint256)",
  "function setApprovalForAll(address, bool)",
  "function isApprovedForAll(address, address) view returns (bool)"
];

const MARKETPLACE_ABI = [
  "function listItem(address, uint256, uint256)",
  "function buyItem(bytes32) payable",
  "function createAuction(address, uint256, uint256, uint256)",
  "function placeBid(bytes32) payable",
  "function endAuction(bytes32)",
  "function withdraw()",
  "function cancelListing(bytes32)",
  "function listings(bytes32) view returns (address, address, uint256, uint256, bool, uint256)",
  "function auctions(bytes32) view returns (address, address, uint256, uint256, uint256, address, uint256, bool, bool)",
  "function getActiveListings() view returns (bytes32[])",
  "function getActiveAuctions() view returns (bytes32[])",
  "function pendingReturns(address) view returns (uint256)"
];

export default function NFTMarketplace() {
  const { account, signer, isConnected } = useWeb3();
  const [nftContract, setNftContract] = useState(null);
  const [marketplaceContract, setMarketplaceContract] = useState(null);
  const [myNFTs, setMyNFTs] = useState([]);
  const [listings, setListings] = useState([]);
  const [auctions, setAuctions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('mint');

  // 表单状态
  const [mintQuantity, setMintQuantity] = useState(1);
  const [listPrice, setListPrice] = useState('');
  const [selectedNFT, setSelectedNFT] = useState('');
  const [auctionStartPrice, setAuctionStartPrice] = useState('');
  const [auctionDuration, setAuctionDuration] = useState('24');

  useEffect(() => {
    if (isConnected && signer) {
      initializeContracts();
    }
  }, [isConnected, signer]);

  const initializeContracts = async () => {
    try {
      // 使用配置文件中的合约地址
      const contractsConfig = await import('../frontend/config/contracts.json');
      const nftAddress = contractsConfig.contracts.Web3NFT;
      const marketplaceAddress = contractsConfig.contracts.NFTMarketplace;
      
      const nft = new ethers.Contract(nftAddress, NFT_ABI, signer);
      const marketplace = new ethers.Contract(marketplaceAddress, MARKETPLACE_ABI, signer);
      
      setNftContract(nft);
      setMarketplaceContract(marketplace);
      
      loadData(nft, marketplace);
    } catch (error) {
      console.error('初始化合约失败:', error);
    }
  };

  const loadData = async (nft, marketplace) => {
    setLoading(true);
    try {
      // 加载我的 NFTs
      await loadMyNFTs(nft);
      
      // 加载市场列表
      await loadMarketListings(marketplace, nft);
      
      // 加载拍卖
      await loadMarketAuctions(marketplace, nft);
    } catch (error) {
      console.error('加载数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadMyNFTs = async (nft) => {
    try {
      const balance = await nft.balanceOf(account);
      const nfts = [];
      
      for (let i = 0; i < balance; i++) {
        const tokenId = await nft.tokenOfOwnerByIndex(account, i);
        const tokenURI = await nft.tokenURI(tokenId);
        nfts.push({
          tokenId: tokenId.toString(),
          tokenURI,
          owner: account
        });
      }
      
      setMyNFTs(nfts);
    } catch (error) {
      console.error('加载 NFT 失败:', error);
    }
  };

  const loadMarketListings = async (marketplace, nft) => {
    try {
      const listingIds = await marketplace.getActiveListings();
      const listingsData = [];
      
      for (const listingId of listingIds) {
        const listing = await marketplace.listings(listingId);
        if (listing[4]) { // active
          const tokenURI = await nft.tokenURI(listing[2]);
          listingsData.push({
            id: listingId,
            seller: listing[0],
            nftContract: listing[1],
            tokenId: listing[2].toString(),
            price: ethers.formatEther(listing[3]),
            tokenURI,
            listingTime: new Date(Number(listing[5]) * 1000)
          });
        }
      }
      
      setListings(listingsData);
    } catch (error) {
      console.error('加载市场列表失败:', error);
    }
  };

  const loadMarketAuctions = async (marketplace, nft) => {
    try {
      const auctionIds = await marketplace.getActiveAuctions();
      const auctionsData = [];
      
      for (const auctionId of auctionIds) {
        const auction = await marketplace.auctions(auctionId);
        if (auction[7] && !auction[8]) { // active and not ended
          const tokenURI = await nft.tokenURI(auction[2]);
          auctionsData.push({
            id: auctionId,
            seller: auction[0],
            nftContract: auction[1],
            tokenId: auction[2].toString(),
            startingPrice: ethers.formatEther(auction[3]),
            highestBid: ethers.formatEther(auction[4]),
            highestBidder: auction[5],
            endTime: new Date(Number(auction[6]) * 1000),
            tokenURI
          });
        }
      }
      
      setAuctions(auctionsData);
    } catch (error) {
      console.error('加载拍卖失败:', error);
    }
  };

  const handleMintNFT = async () => {
    if (!nftContract) return;
    
    try {
      setLoading(true);
      const mintPrice = await nftContract.mintPrice();
      const totalCost = mintPrice * BigInt(mintQuantity);
      
      const tx = await nftContract.mint(mintQuantity, { value: totalCost });
      await tx.wait();
      
      alert('NFT 铸造成功!');
      loadMyNFTs(nftContract);
    } catch (error) {
      console.error('铸造失败:', error);
      alert('铸造失败: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleListNFT = async () => {
    if (!nftContract || !marketplaceContract || !selectedNFT || !listPrice) return;
    
    try {
      setLoading(true);
      
      // 首先授权市场合约
      const isApproved = await nftContract.isApprovedForAll(account, await marketplaceContract.getAddress());
      if (!isApproved) {
        const approveTx = await nftContract.setApprovalForAll(await marketplaceContract.getAddress(), true);
        await approveTx.wait();
      }
      
      // 列出 NFT
      const tx = await marketplaceContract.listItem(
        await nftContract.getAddress(),
        selectedNFT,
        ethers.parseEther(listPrice)
      );
      await tx.wait();
      
      alert('NFT 上架成功!');
      loadData(nftContract, marketplaceContract);
    } catch (error) {
      console.error('上架失败:', error);
      alert('上架失败: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleBuyNFT = async (listingId, price) => {
    if (!marketplaceContract) return;
    
    try {
      setLoading(true);
      const tx = await marketplaceContract.buyItem(listingId, {
        value: ethers.parseEther(price)
      });
      await tx.wait();
      
      alert('购买成功!');
      loadData(nftContract, marketplaceContract);
    } catch (error) {
      console.error('购买失败:', error);
      alert('购买失败: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  if (!isConnected) {
    return (
      <div className="container mx-auto px-4 py-8 text-center">
        <h1 className="text-3xl font-bold mb-4">NFT 市场</h1>
        <p className="text-gray-600">请先连接钱包以使用 NFT 市场功能</p>
      </div>
    );
  }

  return (
    <>
      <Head>
        <title>NFT 市场 - Web3 生态系统</title>
      </Head>

      <div className="container mx-auto px-4 py-8">
        <h1 className="text-4xl font-bold text-center mb-8 gradient-text">
          🎨 NFT 市场
        </h1>

        {/* 标签页导航 */}
        <div className="flex justify-center mb-8">
          <div className="bg-white rounded-lg p-1 shadow-lg">
            {['mint', 'my-nfts', 'marketplace', 'auctions'].map((tab) => (
              <button
                key={tab}
                onClick={() => setActiveTab(tab)}
                className={`px-6 py-2 rounded-md font-medium transition-colors ${
                  activeTab === tab
                    ? 'bg-primary-600 text-white'
                    : 'text-gray-600 hover:text-primary-600'
                }`}
              >
                {tab === 'mint' && '铸造 NFT'}
                {tab === 'my-nfts' && '我的 NFT'}
                {tab === 'marketplace' && '市场'}
                {tab === 'auctions' && '拍卖'}
              </button>
            ))}
          </div>
        </div>

        {/* 铸造 NFT */}
        {activeTab === 'mint' && (
          <div className="max-w-md mx-auto">
            <div className="card">
              <h2 className="text-2xl font-bold mb-4">铸造 NFT</h2>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    数量
                  </label>
                  <input
                    type="number"
                    min="1"
                    max="10"
                    value={mintQuantity}
                    onChange={(e) => setMintQuantity(e.target.value)}
                    className="input-field"
                  />
                </div>
                <button
                  onClick={handleMintNFT}
                  disabled={loading}
                  className="btn-primary w-full"
                >
                  {loading ? '铸造中...' : `铸造 ${mintQuantity} 个 NFT`}
                </button>
              </div>
            </div>
          </div>
        )}

        {/* 我的 NFT */}
        {activeTab === 'my-nfts' && (
          <div>
            <div className="grid-responsive">
              {myNFTs.map((nft) => (
                <div key={nft.tokenId} className="card">
                  <div className="aspect-square bg-gray-200 rounded-lg mb-4 flex items-center justify-center">
                    <span className="text-4xl">🖼️</span>
                  </div>
                  <h3 className="font-bold mb-2">NFT #{nft.tokenId}</h3>
                  <div className="space-y-2">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        售价 (ETH)
                      </label>
                      <input
                        type="number"
                        step="0.01"
                        placeholder="0.1"
                        className="input-field"
                        onChange={(e) => setListPrice(e.target.value)}
                      />
                    </div>
                    <button
                      onClick={() => {
                        setSelectedNFT(nft.tokenId);
                        handleListNFT();
                      }}
                      disabled={loading || !listPrice}
                      className="btn-primary w-full text-sm"
                    >
                      上架销售
                    </button>
                  </div>
                </div>
              ))}
            </div>
            {myNFTs.length === 0 && (
              <div className="text-center py-8">
                <p className="text-gray-600">您还没有任何 NFT</p>
              </div>
            )}
          </div>
        )}

        {/* 市场 */}
        {activeTab === 'marketplace' && (
          <div>
            <div className="grid-responsive">
              {listings.map((listing) => (
                <div key={listing.id} className="card">
                  <div className="aspect-square bg-gray-200 rounded-lg mb-4 flex items-center justify-center">
                    <span className="text-4xl">🖼️</span>
                  </div>
                  <h3 className="font-bold mb-2">NFT #{listing.tokenId}</h3>
                  <p className="text-sm text-gray-600 mb-2">
                    卖家: {listing.seller.slice(0, 6)}...{listing.seller.slice(-4)}
                  </p>
                  <p className="text-lg font-bold text-primary-600 mb-4">
                    {listing.price} ETH
                  </p>
                  <button
                    onClick={() => handleBuyNFT(listing.id, listing.price)}
                    disabled={loading || listing.seller === account}
                    className="btn-primary w-full"
                  >
                    {listing.seller === account ? '我的商品' : '购买'}
                  </button>
                </div>
              ))}
            </div>
            {listings.length === 0 && (
              <div className="text-center py-8">
                <p className="text-gray-600">暂无商品在售</p>
              </div>
            )}
          </div>
        )}

        {/* 拍卖 */}
        {activeTab === 'auctions' && (
          <div>
            <div className="grid-responsive">
              {auctions.map((auction) => (
                <div key={auction.id} className="card">
                  <div className="aspect-square bg-gray-200 rounded-lg mb-4 flex items-center justify-center">
                    <span className="text-4xl">🖼️</span>
                  </div>
                  <h3 className="font-bold mb-2">NFT #{auction.tokenId}</h3>
                  <p className="text-sm text-gray-600 mb-2">
                    卖家: {auction.seller.slice(0, 6)}...{auction.seller.slice(-4)}
                  </p>
                  <div className="space-y-2 mb-4">
                    <p className="text-sm">
                      起拍价: {auction.startingPrice} ETH
                    </p>
                    <p className="text-sm">
                      当前最高价: {auction.highestBid} ETH
                    </p>
                    <p className="text-sm">
                      结束时间: {auction.endTime.toLocaleString()}
                    </p>
                  </div>
                  <button
                    disabled={loading || auction.seller === account || new Date() > auction.endTime}
                    className="btn-primary w-full"
                  >
                    {auction.seller === account ? '我的拍卖' : '出价'}
                  </button>
                </div>
              ))}
            </div>
            {auctions.length === 0 && (
              <div className="text-center py-8">
                <p className="text-gray-600">暂无拍卖进行中</p>
              </div>
            )}
          </div>
        )}
      </div>
    </>
  );
}
