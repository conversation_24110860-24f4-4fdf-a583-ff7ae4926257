// SPDX-License-Identifier: MIT
pragma solidity ^0.8.24;

import "@openzeppelin/contracts/token/ERC721/ERC721.sol";
import "@openzeppelin/contracts/token/ERC721/extensions/ERC721Enumerable.sol";
import "@openzeppelin/contracts/token/ERC721/extensions/ERC721URIStorage.sol";
import "@openzeppelin/contracts/token/ERC721/extensions/ERC721Pausable.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/token/ERC721/extensions/ERC721Burnable.sol";
import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
/**
 * @title Web3NFT
 * @dev 完整的 ERC-721 NFT 实现，支持铸造、交易、版税等功能
 *
 * 功能特性：
 * - 标准 ERC-721 功能
 * - 可枚举和可查询
 * - 元数据存储 (IPFS)
 * - 版税支持 (EIP-2981)
 * - 批量铸造
 * - 白名单铸造
 * - 可暂停/恢复
 */
contract Web3NFT is
    ERC721,
    ERC721Enumerable,
    ERC721URIStorage,
    ERC721Pausable,
    Ownable,
    ERC721Burnable,
    ReentrancyGuard
{
    uint256 private _tokenIdCounter;
    
    // NFT 配置
    uint256 public constant MAX_SUPPLY = 10000;
    uint256 public mintPrice = 0.01 ether;
    uint256 public maxMintPerTx = 10;
    uint256 public maxMintPerWallet = 50;
    
    // 版税配置 (基点，10000 = 100%)
    uint256 public royaltyPercentage = 250; // 2.5%
    address public royaltyRecipient;
    
    // 铸造阶段
    enum MintPhase { CLOSED, WHITELIST, PUBLIC }
    MintPhase public currentPhase = MintPhase.CLOSED;
    
    // 白名单和铸造记录
    mapping(address => bool) public whitelist;
    mapping(address => uint256) public mintedCount;
    
    // 基础 URI
    string private _baseTokenURI;
    
    // 事件
    event MintPhaseChanged(MintPhase newPhase);
    event WhitelistAdded(address[] addresses);
    event WhitelistRemoved(address[] addresses);
    event RoyaltyUpdated(address recipient, uint256 percentage);
    event BaseURIUpdated(string newBaseURI);
    
    constructor(
        string memory name,
        string memory symbol,
        string memory baseURI,
        address initialOwner,
        address _royaltyRecipient
    ) ERC721(name, symbol) Ownable(initialOwner) {
        _baseTokenURI = baseURI;
        royaltyRecipient = _royaltyRecipient;

        // 从 tokenId 1 开始
        _tokenIdCounter = 1;
    }
    
    /**
     * @dev 公开铸造
     */
    function mint(uint256 quantity) external payable nonReentrant whenNotPaused {
        require(currentPhase == MintPhase.PUBLIC, "Public mint not active");
        require(quantity > 0 && quantity <= maxMintPerTx, "Invalid quantity");
        require(totalSupply() + quantity <= MAX_SUPPLY, "Exceeds max supply");
        require(mintedCount[msg.sender] + quantity <= maxMintPerWallet, "Exceeds wallet limit");
        require(msg.value >= mintPrice * quantity, "Insufficient payment");
        
        mintedCount[msg.sender] += quantity;
        
        for (uint256 i = 0; i < quantity; i++) {
            uint256 tokenId = _tokenIdCounter;
            _tokenIdCounter++;
            _safeMint(msg.sender, tokenId);
        }
    }
    
    /**
     * @dev 白名单铸造
     */
    function whitelistMint(uint256 quantity) external payable nonReentrant whenNotPaused {
        require(currentPhase == MintPhase.WHITELIST, "Whitelist mint not active");
        require(whitelist[msg.sender], "Not on whitelist");
        require(quantity > 0 && quantity <= maxMintPerTx, "Invalid quantity");
        require(totalSupply() + quantity <= MAX_SUPPLY, "Exceeds max supply");
        require(mintedCount[msg.sender] + quantity <= maxMintPerWallet, "Exceeds wallet limit");
        require(msg.value >= mintPrice * quantity, "Insufficient payment");
        
        mintedCount[msg.sender] += quantity;
        
        for (uint256 i = 0; i < quantity; i++) {
            uint256 tokenId = _tokenIdCounter;
            _tokenIdCounter++;
            _safeMint(msg.sender, tokenId);
        }
    }
    
    /**
     * @dev 管理员铸造 (免费)
     */
    function adminMint(address to, uint256 quantity) external onlyOwner {
        require(quantity > 0, "Invalid quantity");
        require(totalSupply() + quantity <= MAX_SUPPLY, "Exceeds max supply");
        
        for (uint256 i = 0; i < quantity; i++) {
            uint256 tokenId = _tokenIdCounter;
            _tokenIdCounter++;
            _safeMint(to, tokenId);
        }
    }
    
    /**
     * @dev 批量设置代币 URI
     */
    function batchSetTokenURI(uint256[] calldata tokenIds, string[] calldata uris) 
        external onlyOwner 
    {
        require(tokenIds.length == uris.length, "Arrays length mismatch");
        
        for (uint256 i = 0; i < tokenIds.length; i++) {
            _setTokenURI(tokenIds[i], uris[i]);
        }
    }
    
    /**
     * @dev 设置铸造阶段
     */
    function setMintPhase(MintPhase phase) external onlyOwner {
        currentPhase = phase;
        emit MintPhaseChanged(phase);
    }
    
    /**
     * @dev 添加白名单地址
     */
    function addToWhitelist(address[] calldata addresses) external onlyOwner {
        for (uint256 i = 0; i < addresses.length; i++) {
            whitelist[addresses[i]] = true;
        }
        emit WhitelistAdded(addresses);
    }
    
    /**
     * @dev 移除白名单地址
     */
    function removeFromWhitelist(address[] calldata addresses) external onlyOwner {
        for (uint256 i = 0; i < addresses.length; i++) {
            whitelist[addresses[i]] = false;
        }
        emit WhitelistRemoved(addresses);
    }
    
    /**
     * @dev 设置铸造价格
     */
    function setMintPrice(uint256 newPrice) external onlyOwner {
        mintPrice = newPrice;
    }
    
    /**
     * @dev 设置每次最大铸造数量
     */
    function setMaxMintPerTx(uint256 newMax) external onlyOwner {
        maxMintPerTx = newMax;
    }
    
    /**
     * @dev 设置每钱包最大铸造数量
     */
    function setMaxMintPerWallet(uint256 newMax) external onlyOwner {
        maxMintPerWallet = newMax;
    }
    
    /**
     * @dev 设置基础 URI
     */
    function setBaseURI(string calldata newBaseURI) external onlyOwner {
        _baseTokenURI = newBaseURI;
        emit BaseURIUpdated(newBaseURI);
    }
    
    /**
     * @dev 设置版税信息
     */
    function setRoyalty(address recipient, uint256 percentage) external onlyOwner {
        require(percentage <= 1000, "Royalty too high"); // 最大10%
        royaltyRecipient = recipient;
        royaltyPercentage = percentage;
        emit RoyaltyUpdated(recipient, percentage);
    }
    
    /**
     * @dev 提取合约余额
     */
    function withdraw() external onlyOwner {
        uint256 balance = address(this).balance;
        require(balance > 0, "No funds to withdraw");
        
        (bool success, ) = payable(owner()).call{value: balance}("");
        require(success, "Withdrawal failed");
    }
    
    /**
     * @dev 暂停合约
     */
    function pause() external onlyOwner {
        _pause();
    }
    
    /**
     * @dev 恢复合约
     */
    function unpause() external onlyOwner {
        _unpause();
    }
    
    /**
     * @dev 获取用户拥有的所有代币 ID
     */
    function tokensOfOwner(address owner) external view returns (uint256[] memory) {
        uint256 tokenCount = balanceOf(owner);
        uint256[] memory tokenIds = new uint256[](tokenCount);
        
        for (uint256 i = 0; i < tokenCount; i++) {
            tokenIds[i] = tokenOfOwnerByIndex(owner, i);
        }
        
        return tokenIds;
    }
    
    /**
     * @dev 版税信息 (EIP-2981)
     */
    function royaltyInfo(uint256 tokenId, uint256 salePrice) 
        external view returns (address, uint256) 
    {
        require(_ownerOf(tokenId) != address(0), "Token does not exist");
        uint256 royaltyAmount = (salePrice * royaltyPercentage) / 10000;
        return (royaltyRecipient, royaltyAmount);
    }
    
    // 重写基础 URI 函数
    function _baseURI() internal view override returns (string memory) {
        return _baseTokenURI;
    }
    
    // 重写必要的函数以支持多重继承
    function _update(address to, uint256 tokenId, address auth)
        internal
        override(ERC721, ERC721Enumerable, ERC721Pausable)
        returns (address)
    {
        return super._update(to, tokenId, auth);
    }
    
    function _increaseBalance(address account, uint128 value)
        internal
        override(ERC721, ERC721Enumerable)
    {
        super._increaseBalance(account, value);
    }
    
    function tokenURI(uint256 tokenId)
        public
        view
        override(ERC721, ERC721URIStorage)
        returns (string memory)
    {
        return super.tokenURI(tokenId);
    }
    
    function supportsInterface(bytes4 interfaceId)
        public
        view
        override(ERC721, ERC721Enumerable, ERC721URIStorage)
        returns (bool)
    {
        return super.supportsInterface(interfaceId);
    }
}
