import { define<PERSON>hai<PERSON> } from '../../utils/chain/defineChain.js'

export const zenchainTestnet = /*#__PURE__*/ define<PERSON>hain({
  id: 8408,
  name: 'Zen<PERSON>hain Testnet',
  nativeCurrency: {
    decimals: 18,
    name: '<PERSON><PERSON>',
    symbol: '<PERSON><PERSON>',
  },
  rpcUrls: {
    default: {
      http: ['https://zenchain-testnet.api.onfinality.io/public'],
      webSocket: ['wss://zenchain-testnet.api.onfinality.io/public-ws'],
    },
  },
  contracts: {
    multicall3: {
      address: '0xcA11bde05977b3631167028862bE2a173976CA11',
      blockCreated: 230019,
    },
  },
  blockExplorers: {
    default: {
      name: '<PERSON><PERSON><PERSON>',
      url: 'https://zentrace.io',
    },
  },
  testnet: true,
})
