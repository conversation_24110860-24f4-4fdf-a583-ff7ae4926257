const { expect } = require("chai");
const { ethers } = require("hardhat");

describe("SimpleToken", function () {
  let SimpleToken;
  let simpleToken;
  let owner;
  let addr1;
  let addr2;
  let addrs;

  const INITIAL_SUPPLY = ethers.parseEther("1000000"); // 100万代币
  const MAX_SUPPLY = ethers.parseEther("100000000"); // 1亿代币

  beforeEach(async function () {
    // 获取合约工厂和签名者
    SimpleToken = await ethers.getContractFactory("SimpleToken");
    [owner, addr1, addr2, ...addrs] = await ethers.getSigners();

    // 部署合约
    simpleToken = await SimpleToken.deploy(
      "Test Token",
      "TEST",
      INITIAL_SUPPLY,
      owner.address
    );
  });

  describe("部署", function () {
    it("应该设置正确的名称和符号", async function () {
      expect(await simpleToken.name()).to.equal("Test Token");
      expect(await simpleToken.symbol()).to.equal("TEST");
    });

    it("应该设置正确的初始供应量", async function () {
      expect(await simpleToken.totalSupply()).to.equal(INITIAL_SUPPLY);
    });

    it("应该将初始供应量分配给所有者", async function () {
      const ownerBalance = await simpleToken.balanceOf(owner.address);
      expect(ownerBalance).to.equal(INITIAL_SUPPLY);
    });

    it("应该设置正确的所有者", async function () {
      expect(await simpleToken.owner()).to.equal(owner.address);
    });

    it("应该设置正确的最大供应量", async function () {
      expect(await simpleToken.MAX_SUPPLY()).to.equal(MAX_SUPPLY);
    });
  });

  describe("转账", function () {
    it("应该能够转账代币", async function () {
      const transferAmount = ethers.parseEther("100");
      
      await simpleToken.transfer(addr1.address, transferAmount);
      
      expect(await simpleToken.balanceOf(addr1.address)).to.equal(transferAmount);
      expect(await simpleToken.balanceOf(owner.address)).to.equal(
        INITIAL_SUPPLY - transferAmount
      );
    });

    it("应该在余额不足时失败", async function () {
      const transferAmount = INITIAL_SUPPLY + ethers.parseEther("1");
      
      await expect(
        simpleToken.transfer(addr1.address, transferAmount)
      ).to.be.revertedWithCustomError(simpleToken, "ERC20InsufficientBalance");
    });

    it("应该触发转账事件", async function () {
      const transferAmount = ethers.parseEther("100");
      
      await expect(simpleToken.transfer(addr1.address, transferAmount))
        .to.emit(simpleToken, "Transfer")
        .withArgs(owner.address, addr1.address, transferAmount);
    });
  });

  describe("授权", function () {
    it("应该能够授权代币", async function () {
      const approveAmount = ethers.parseEther("100");
      
      await simpleToken.approve(addr1.address, approveAmount);
      
      expect(await simpleToken.allowance(owner.address, addr1.address))
        .to.equal(approveAmount);
    });

    it("应该能够使用授权转账", async function () {
      const approveAmount = ethers.parseEther("100");
      
      await simpleToken.approve(addr1.address, approveAmount);
      await simpleToken.connect(addr1).transferFrom(
        owner.address,
        addr2.address,
        approveAmount
      );
      
      expect(await simpleToken.balanceOf(addr2.address)).to.equal(approveAmount);
      expect(await simpleToken.allowance(owner.address, addr1.address)).to.equal(0);
    });

    it("应该在授权不足时失败", async function () {
      const approveAmount = ethers.parseEther("50");
      const transferAmount = ethers.parseEther("100");
      
      await simpleToken.approve(addr1.address, approveAmount);
      
      await expect(
        simpleToken.connect(addr1).transferFrom(
          owner.address,
          addr2.address,
          transferAmount
        )
      ).to.be.revertedWithCustomError(simpleToken, "ERC20InsufficientAllowance");
    });
  });

  describe("铸造", function () {
    it("所有者应该能够铸造代币", async function () {
      const mintAmount = ethers.parseEther("1000");
      
      await simpleToken.mint(addr1.address, mintAmount);
      
      expect(await simpleToken.balanceOf(addr1.address)).to.equal(mintAmount);
      expect(await simpleToken.totalSupply()).to.equal(INITIAL_SUPPLY + mintAmount);
    });

    it("非所有者不应该能够铸造代币", async function () {
      const mintAmount = ethers.parseEther("1000");
      
      await expect(
        simpleToken.connect(addr1).mint(addr2.address, mintAmount)
      ).to.be.revertedWithCustomError(simpleToken, "OwnableUnauthorizedAccount");
    });

    it("不应该能够超过最大供应量铸造", async function () {
      const mintAmount = MAX_SUPPLY - INITIAL_SUPPLY + ethers.parseEther("1");
      
      await expect(
        simpleToken.mint(addr1.address, mintAmount)
      ).to.be.revertedWith("Exceeds max supply");
    });

    it("应该触发转账事件", async function () {
      const mintAmount = ethers.parseEther("1000");
      
      await expect(simpleToken.mint(addr1.address, mintAmount))
        .to.emit(simpleToken, "Transfer")
        .withArgs(ethers.ZeroAddress, addr1.address, mintAmount);
    });
  });

  describe("燃烧", function () {
    it("应该能够燃烧代币", async function () {
      const burnAmount = ethers.parseEther("1000");
      
      await simpleToken.burn(burnAmount);
      
      expect(await simpleToken.balanceOf(owner.address)).to.equal(
        INITIAL_SUPPLY - burnAmount
      );
      expect(await simpleToken.totalSupply()).to.equal(INITIAL_SUPPLY - burnAmount);
    });

    it("应该在余额不足时失败", async function () {
      const burnAmount = INITIAL_SUPPLY + ethers.parseEther("1");
      
      await expect(
        simpleToken.burn(burnAmount)
      ).to.be.revertedWithCustomError(simpleToken, "ERC20InsufficientBalance");
    });

    it("应该触发转账事件", async function () {
      const burnAmount = ethers.parseEther("1000");
      
      await expect(simpleToken.burn(burnAmount))
        .to.emit(simpleToken, "Transfer")
        .withArgs(owner.address, ethers.ZeroAddress, burnAmount);
    });
  });

  describe("所有权", function () {
    it("应该能够转移所有权", async function () {
      await simpleToken.transferOwnership(addr1.address);
      expect(await simpleToken.owner()).to.equal(addr1.address);
    });

    it("新所有者应该能够铸造代币", async function () {
      await simpleToken.transferOwnership(addr1.address);
      
      const mintAmount = ethers.parseEther("1000");
      await simpleToken.connect(addr1).mint(addr2.address, mintAmount);
      
      expect(await simpleToken.balanceOf(addr2.address)).to.equal(mintAmount);
    });

    it("旧所有者不应该能够铸造代币", async function () {
      await simpleToken.transferOwnership(addr1.address);
      
      const mintAmount = ethers.parseEther("1000");
      await expect(
        simpleToken.mint(addr2.address, mintAmount)
      ).to.be.revertedWithCustomError(simpleToken, "OwnableUnauthorizedAccount");
    });
  });

  describe("边界条件", function () {
    it("应该处理零地址转账", async function () {
      await expect(
        simpleToken.transfer(ethers.ZeroAddress, ethers.parseEther("100"))
      ).to.be.revertedWithCustomError(simpleToken, "ERC20InvalidReceiver");
    });

    it("应该处理零数量转账", async function () {
      await expect(simpleToken.transfer(addr1.address, 0))
        .to.emit(simpleToken, "Transfer")
        .withArgs(owner.address, addr1.address, 0);
    });

    it("应该处理最大数量铸造", async function () {
      const maxMintAmount = MAX_SUPPLY - INITIAL_SUPPLY;
      
      await simpleToken.mint(addr1.address, maxMintAmount);
      
      expect(await simpleToken.totalSupply()).to.equal(MAX_SUPPLY);
    });

    it("应该处理全部余额燃烧", async function () {
      await simpleToken.burn(INITIAL_SUPPLY);
      
      expect(await simpleToken.balanceOf(owner.address)).to.equal(0);
      expect(await simpleToken.totalSupply()).to.equal(0);
    });
  });

  describe("Gas 优化测试", function () {
    it("批量转账应该高效", async function () {
      const recipients = [addr1.address, addr2.address];
      const amounts = [ethers.parseEther("100"), ethers.parseEther("200")];
      
      // 模拟批量转账
      for (let i = 0; i < recipients.length; i++) {
        await simpleToken.transfer(recipients[i], amounts[i]);
      }
      
      expect(await simpleToken.balanceOf(addr1.address)).to.equal(amounts[0]);
      expect(await simpleToken.balanceOf(addr2.address)).to.equal(amounts[1]);
    });
  });
});
