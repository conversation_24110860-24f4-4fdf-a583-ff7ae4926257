import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useWeb3 } from './_app';
import { ethers } from 'ethers';

const BRIDGE_ABI = [
  "function supportedChains(uint256) view returns (bool)",
  "function supportedTokens(address) view returns (bool)",
  "function bridgeFees(uint256) view returns (uint256)",
  "function tokenFees(address) view returns (uint256)",
  "function userNonces(address) view returns (uint256)",
  "function pendingReturns(address) view returns (uint256)",
  "function initiateTransfer(address token, uint256 amount, address to, uint256 targetChainId) payable",
  "function withdraw()",
  "function calculateFees(address token, uint256 amount, uint256 targetChainId) view returns (uint256, uint256, uint256)"
];

const TOKEN_ABI = [
  "function name() view returns (string)",
  "function symbol() view returns (string)",
  "function balanceOf(address) view returns (uint256)",
  "function allowance(address, address) view returns (uint256)",
  "function approve(address, uint256) returns (bool)"
];

const SUPPORTED_CHAINS = {
  1: { name: "以太坊主网", symbol: "ETH", rpc: "https://mainnet.infura.io/v3/YOUR_KEY" },
  137: { name: "Polygon", symbol: "MATIC", rpc: "https://polygon-rpc.com/" },
  56: { name: "BSC", symbol: "BNB", rpc: "https://bsc-dataseed1.binance.org/" },
  31337: { name: "本地测试网", symbol: "ETH", rpc: "http://127.0.0.1:8545" }
};

export default function Bridge() {
  const { account, signer, isConnected, chainId } = useWeb3();
  const [bridgeContract, setBridgeContract] = useState(null);
  const [tokenContract, setTokenContract] = useState(null);
  const [bridgeInfo, setBridgeInfo] = useState(null);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('transfer');

  // 表单状态
  const [transferForm, setTransferForm] = useState({
    token: '',
    amount: '',
    recipient: '',
    targetChain: '137'
  });

  const [fees, setFees] = useState({
    bridgeFee: '0',
    tokenFee: '0',
    totalAmount: '0'
  });

  useEffect(() => {
    if (isConnected && signer) {
      initializeContracts();
    }
  }, [isConnected, signer]);

  useEffect(() => {
    if (transferForm.token && transferForm.amount && transferForm.targetChain) {
      calculateTransferFees();
    }
  }, [transferForm.token, transferForm.amount, transferForm.targetChain]);

  const initializeContracts = async () => {
    try {
      // 注意：这里需要实际部署跨链桥合约后更新地址
      const bridgeAddress = "******************************************"; // 需要部署后更新
      
      if (bridgeAddress === "******************************************") {
        console.log("跨链桥合约尚未部署");
        return;
      }

      const bridge = new ethers.Contract(bridgeAddress, BRIDGE_ABI, signer);
      setBridgeContract(bridge);

      loadBridgeData(bridge);
    } catch (error) {
      console.error('初始化合约失败:', error);
    }
  };

  const loadBridgeData = async (bridge) => {
    setLoading(true);
    try {
      const [userNonce, pendingReturns] = await Promise.all([
        bridge.userNonces(account),
        bridge.pendingReturns(account)
      ]);

      setBridgeInfo({
        userNonce: userNonce.toString(),
        pendingReturns: ethers.formatEther(pendingReturns),
        currentChain: SUPPORTED_CHAINS[chainId] || { name: "未知网络", symbol: "ETH" }
      });

    } catch (error) {
      console.error('加载桥接数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const calculateTransferFees = async () => {
    if (!bridgeContract || !transferForm.token || !transferForm.amount || !transferForm.targetChain) return;

    try {
      const amount = ethers.parseEther(transferForm.amount);
      const [bridgeFee, tokenFee, totalAmount] = await bridgeContract.calculateFees(
        transferForm.token,
        amount,
        transferForm.targetChain
      );

      setFees({
        bridgeFee: ethers.formatEther(bridgeFee),
        tokenFee: ethers.formatEther(tokenFee),
        totalAmount: ethers.formatEther(totalAmount)
      });
    } catch (error) {
      console.error('计算费用失败:', error);
    }
  };

  const handleInitiateTransfer = async () => {
    if (!bridgeContract || !transferForm.token || !transferForm.amount || !transferForm.recipient) return;

    try {
      setLoading(true);

      const amount = ethers.parseEther(transferForm.amount);
      const bridgeFee = ethers.parseEther(fees.bridgeFee);

      // 检查代币授权
      if (transferForm.token !== ethers.ZeroAddress) {
        const token = new ethers.Contract(transferForm.token, TOKEN_ABI, signer);
        const allowance = await token.allowance(account, await bridgeContract.getAddress());
        
        if (allowance < amount) {
          const approveTx = await token.approve(await bridgeContract.getAddress(), amount);
          await approveTx.wait();
        }
      }

      // 发起跨链转账
      const tx = await bridgeContract.initiateTransfer(
        transferForm.token,
        amount,
        transferForm.recipient,
        transferForm.targetChain,
        { value: bridgeFee }
      );

      await tx.wait();
      alert('跨链转账已发起！请等待验证者确认。');

      setTransferForm({
        token: '',
        amount: '',
        recipient: '',
        targetChain: '137'
      });

      loadBridgeData(bridgeContract);
    } catch (error) {
      console.error('发起转账失败:', error);
      alert('发起转账失败: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleWithdraw = async () => {
    if (!bridgeContract) return;

    try {
      setLoading(true);
      const tx = await bridgeContract.withdraw();
      await tx.wait();

      alert('提取成功!');
      loadBridgeData(bridgeContract);
    } catch (error) {
      console.error('提取失败:', error);
      alert('提取失败: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  if (!isConnected) {
    return (
      <div className="container mx-auto px-4 py-8 text-center">
        <h1 className="text-3xl font-bold mb-4">跨链桥接</h1>
        <p className="text-gray-600">请先连接钱包以使用跨链桥接功能</p>
      </div>
    );
  }

  // 如果跨链桥合约未部署，显示提示
  if (!bridgeContract) {
    return (
      <div className="container mx-auto px-4 py-8 text-center">
        <h1 className="text-3xl font-bold mb-4">跨链桥接</h1>
        <div className="card max-w-md mx-auto">
          <h2 className="text-xl font-bold mb-4">🚧 开发中</h2>
          <p className="text-gray-600 mb-4">
            跨链桥接功能正在开发中，将支持：
          </p>
          <ul className="text-left space-y-2 mb-6">
            <li>• 以太坊 ↔ Polygon</li>
            <li>• 以太坊 ↔ BSC</li>
            <li>• Polygon ↔ BSC</li>
            <li>• 多重签名验证</li>
            <li>• 自动化桥接</li>
          </ul>
          <p className="text-sm text-gray-500">
            请等待合约部署完成后再使用此功能
          </p>
        </div>
      </div>
    );
  }

  return (
    <>
      <Head>
        <title>跨链桥接 - Web3 生态系统</title>
      </Head>

      <div className="container mx-auto px-4 py-8">
        {/* 导航栏 */}
        <nav className="bg-white rounded-lg shadow-lg p-4 mb-8">
          <div className="flex justify-center space-x-6">
            <a href="/" className="text-gray-600 font-medium hover:text-primary-600">
              首页
            </a>
            <a href="/nft-marketplace" className="text-gray-600 font-medium hover:text-primary-600">
              NFT 市场
            </a>
            <a href="/defi" className="text-gray-600 font-medium hover:text-primary-600">
              DeFi 协议
            </a>
            <a href="/dao" className="text-gray-600 font-medium hover:text-primary-600">
              DAO 治理
            </a>
            <a href="/bridge" className="text-primary-600 font-medium hover:text-primary-800">
              跨链桥接
            </a>
          </div>
        </nav>

        <h1 className="text-4xl font-bold text-center mb-8 gradient-text">
          🌉 跨链桥接
        </h1>

        {/* 当前网络信息 */}
        {bridgeInfo && (
          <div className="card max-w-md mx-auto mb-8">
            <h2 className="text-xl font-bold mb-4">当前网络</h2>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-gray-600">网络:</span>
                <span className="font-semibold">{bridgeInfo.currentChain.name}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">链 ID:</span>
                <span className="font-semibold">{chainId}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">待提取资金:</span>
                <span className="font-semibold">{parseFloat(bridgeInfo.pendingReturns).toFixed(4)} ETH</span>
              </div>
              {parseFloat(bridgeInfo.pendingReturns) > 0 && (
                <button
                  onClick={handleWithdraw}
                  disabled={loading}
                  className="btn-primary w-full mt-4"
                >
                  {loading ? '提取中...' : '提取资金'}
                </button>
              )}
            </div>
          </div>
        )}

        {/* 标签页导航 */}
        <div className="flex justify-center mb-8">
          <div className="bg-white rounded-lg p-1 shadow-lg">
            {['transfer', 'history'].map((tab) => (
              <button
                key={tab}
                onClick={() => setActiveTab(tab)}
                className={`px-6 py-2 rounded-md font-medium transition-colors ${
                  activeTab === tab
                    ? 'bg-primary-600 text-white'
                    : 'text-gray-600 hover:text-primary-600'
                }`}
              >
                {tab === 'transfer' && '跨链转账'}
                {tab === 'history' && '转账历史'}
              </button>
            ))}
          </div>
        </div>

        {/* 跨链转账 */}
        {activeTab === 'transfer' && (
          <div className="max-w-md mx-auto">
            <div className="card">
              <h2 className="text-2xl font-bold mb-6">跨链转账</h2>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    代币地址
                  </label>
                  <input
                    type="text"
                    value={transferForm.token}
                    onChange={(e) => setTransferForm({...transferForm, token: e.target.value})}
                    placeholder="0x... (留空表示原生代币)"
                    className="input-field"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    转账数量
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    value={transferForm.amount}
                    onChange={(e) => setTransferForm({...transferForm, amount: e.target.value})}
                    placeholder="0.0"
                    className="input-field"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    目标链
                  </label>
                  <select
                    value={transferForm.targetChain}
                    onChange={(e) => setTransferForm({...transferForm, targetChain: e.target.value})}
                    className="input-field"
                  >
                    {Object.entries(SUPPORTED_CHAINS)
                      .filter(([id]) => parseInt(id) !== chainId)
                      .map(([id, chain]) => (
                        <option key={id} value={id}>
                          {chain.name}
                        </option>
                      ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    接收地址
                  </label>
                  <input
                    type="text"
                    value={transferForm.recipient}
                    onChange={(e) => setTransferForm({...transferForm, recipient: e.target.value})}
                    placeholder="0x..."
                    className="input-field"
                  />
                </div>

                {/* 费用信息 */}
                {fees.bridgeFee !== '0' && (
                  <div className="bg-yellow-50 p-4 rounded-lg">
                    <h3 className="font-medium mb-2">费用明细</h3>
                    <div className="space-y-1 text-sm">
                      <div className="flex justify-between">
                        <span>桥接费用:</span>
                        <span>{parseFloat(fees.bridgeFee).toFixed(6)} ETH</span>
                      </div>
                      <div className="flex justify-between">
                        <span>代币费用:</span>
                        <span>{parseFloat(fees.tokenFee).toFixed(6)} 代币</span>
                      </div>
                      <div className="flex justify-between font-medium">
                        <span>实际到账:</span>
                        <span>{parseFloat(fees.totalAmount).toFixed(6)} 代币</span>
                      </div>
                    </div>
                  </div>
                )}

                <button
                  onClick={handleInitiateTransfer}
                  disabled={loading || !transferForm.token || !transferForm.amount || !transferForm.recipient}
                  className="btn-primary w-full disabled:opacity-50"
                >
                  {loading ? '发起中...' : '发起跨链转账'}
                </button>

                <div className="text-xs text-gray-500 text-center">
                  <p>⚠️ 跨链转账需要验证者确认，通常需要几分钟时间</p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* 转账历史 */}
        {activeTab === 'history' && (
          <div className="max-w-4xl mx-auto">
            <div className="card">
              <h2 className="text-2xl font-bold mb-6">转账历史</h2>
              
              <div className="text-center py-8">
                <p className="text-gray-600">暂无转账记录</p>
                <p className="text-sm text-gray-500 mt-2">
                  您的跨链转账记录将在这里显示
                </p>
              </div>
            </div>
          </div>
        )}

        {/* 支持的网络 */}
        <div className="mt-12">
          <h2 className="text-2xl font-bold text-center mb-6">支持的网络</h2>
          <div className="grid-responsive">
            {Object.entries(SUPPORTED_CHAINS).map(([id, chain]) => (
              <div key={id} className={`card text-center ${parseInt(id) === chainId ? 'ring-2 ring-primary-500' : ''}`}>
                <h3 className="text-lg font-bold mb-2">{chain.name}</h3>
                <p className="text-gray-600">链 ID: {id}</p>
                <p className="text-gray-600">原生代币: {chain.symbol}</p>
                {parseInt(id) === chainId && (
                  <span className="inline-block mt-2 px-2 py-1 bg-primary-100 text-primary-800 text-xs rounded">
                    当前网络
                  </span>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
    </>
  );
}
