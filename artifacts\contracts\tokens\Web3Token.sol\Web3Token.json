{"_format": "hh-sol-artifact-1", "contractName": "Web3Token", "sourceName": "contracts/tokens/Web3Token.sol", "abi": [{"inputs": [{"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "symbol", "type": "string"}, {"internalType": "uint256", "name": "initialSupply", "type": "uint256"}, {"internalType": "address", "name": "initialOwner", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "name": "CheckpointUnorderedInsertion", "type": "error"}, {"inputs": [], "name": "ECDSAInvalidSignature", "type": "error"}, {"inputs": [{"internalType": "uint256", "name": "length", "type": "uint256"}], "name": "ECDSAInvalidSignatureLength", "type": "error"}, {"inputs": [{"internalType": "bytes32", "name": "s", "type": "bytes32"}], "name": "ECDSAInvalidSignatureS", "type": "error"}, {"inputs": [{"internalType": "uint256", "name": "increasedSupply", "type": "uint256"}, {"internalType": "uint256", "name": "cap", "type": "uint256"}], "name": "ERC20ExceededSafeSupply", "type": "error"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "allowance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "name": "ERC20InsufficientAllowance", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "uint256", "name": "balance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "name": "ERC20InsufficientBalance", "type": "error"}, {"inputs": [{"internalType": "address", "name": "approver", "type": "address"}], "name": "ERC20InvalidApprover", "type": "error"}, {"inputs": [{"internalType": "address", "name": "receiver", "type": "address"}], "name": "ERC20InvalidReceiver", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}], "name": "ERC20InvalidSender", "type": "error"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}], "name": "ERC20InvalidSpender", "type": "error"}, {"inputs": [{"internalType": "uint256", "name": "deadline", "type": "uint256"}], "name": "ERC2612ExpiredSignature", "type": "error"}, {"inputs": [{"internalType": "address", "name": "signer", "type": "address"}, {"internalType": "address", "name": "owner", "type": "address"}], "name": "ERC2612InvalidSigner", "type": "error"}, {"inputs": [{"internalType": "uint256", "name": "timepoint", "type": "uint256"}, {"internalType": "uint48", "name": "clock", "type": "uint48"}], "name": "ERC5805FutureLookup", "type": "error"}, {"inputs": [], "name": "ERC6372InconsistentClock", "type": "error"}, {"inputs": [], "name": "EnforcedPause", "type": "error"}, {"inputs": [], "name": "ExpectedPause", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "uint256", "name": "currentNonce", "type": "uint256"}], "name": "InvalidAccountNonce", "type": "error"}, {"inputs": [], "name": "InvalidShortString", "type": "error"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "OwnableInvalidOwner", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "OwnableUnauthorizedAccount", "type": "error"}, {"inputs": [], "name": "ReentrancyGuardReentrantCall", "type": "error"}, {"inputs": [{"internalType": "uint8", "name": "bits", "type": "uint8"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "SafeCastOverflowedUintDowncast", "type": "error"}, {"inputs": [{"internalType": "string", "name": "str", "type": "string"}], "name": "StringTooLong", "type": "error"}, {"inputs": [{"internalType": "uint256", "name": "expiry", "type": "uint256"}], "name": "VotesExpiredSignature", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "spender", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Approval", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "delegator", "type": "address"}, {"indexed": true, "internalType": "address", "name": "fromDelegate", "type": "address"}, {"indexed": true, "internalType": "address", "name": "toDelegate", "type": "address"}], "name": "DelegateChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "delegate", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "previousVotes", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "newVotes", "type": "uint256"}], "name": "DelegateVotesChanged", "type": "event"}, {"anonymous": false, "inputs": [], "name": "EIP712DomainChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Paused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "reward", "type": "uint256"}], "name": "<PERSON><PERSON><PERSON>laimed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "newRate", "type": "uint256"}], "name": "RewardRateUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "Staked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Transfer", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Unpaused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "Unstaked", "type": "event"}, {"inputs": [], "name": "CLOCK_MODE", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "DOMAIN_SEPARATOR", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MAX_SUPPLY", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "REWARD_PRECISION", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}], "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "burn", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "burnFrom", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "name": "calculateReward", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "uint32", "name": "pos", "type": "uint32"}], "name": "checkpoints", "outputs": [{"components": [{"internalType": "uint48", "name": "_key", "type": "uint48"}, {"internalType": "uint208", "name": "_value", "type": "uint208"}], "internalType": "struct Checkpoints.Checkpoint208", "name": "", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "claimReward", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "clock", "outputs": [{"internalType": "uint48", "name": "", "type": "uint48"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "delegatee", "type": "address"}], "name": "delegate", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "delegatee", "type": "address"}, {"internalType": "uint256", "name": "nonce", "type": "uint256"}, {"internalType": "uint256", "name": "expiry", "type": "uint256"}, {"internalType": "uint8", "name": "v", "type": "uint8"}, {"internalType": "bytes32", "name": "r", "type": "bytes32"}, {"internalType": "bytes32", "name": "s", "type": "bytes32"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "delegates", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "eip712Domain", "outputs": [{"internalType": "bytes1", "name": "fields", "type": "bytes1"}, {"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "version", "type": "string"}, {"internalType": "uint256", "name": "chainId", "type": "uint256"}, {"internalType": "address", "name": "verifyingContract", "type": "address"}, {"internalType": "bytes32", "name": "salt", "type": "bytes32"}, {"internalType": "uint256[]", "name": "extensions", "type": "uint256[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "timepoint", "type": "uint256"}], "name": "getPastTotalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "uint256", "name": "timepoint", "type": "uint256"}], "name": "getPastVotes", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "name": "getStakeInfo", "outputs": [{"internalType": "uint256", "name": "stakedAmount", "type": "uint256"}, {"internalType": "uint256", "name": "stakeTimestamp", "type": "uint256"}, {"internalType": "uint256", "name": "pendingReward", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "getVotes", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "mint", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "nonces", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "numCheckpoints", "outputs": [{"internalType": "uint32", "name": "", "type": "uint32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "pause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "paused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "uint256", "name": "deadline", "type": "uint256"}, {"internalType": "uint8", "name": "v", "type": "uint8"}, {"internalType": "bytes32", "name": "r", "type": "bytes32"}, {"internalType": "bytes32", "name": "s", "type": "bytes32"}], "name": "permit", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "rewardRate", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "newRate", "type": "uint256"}], "name": "setRewardRate", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "stake", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "stakes", "outputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "uint256", "name": "timestamp", "type": "uint256"}, {"internalType": "uint256", "name": "rewardDebt", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalStaked", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "unpause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "unstake", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}