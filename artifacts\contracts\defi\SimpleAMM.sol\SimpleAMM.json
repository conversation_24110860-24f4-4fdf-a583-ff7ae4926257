{"_format": "hh-sol-artifact-1", "contractName": "SimpleAMM", "sourceName": "contracts/defi/SimpleAMM.sol", "abi": [{"inputs": [{"internalType": "address", "name": "_tokenA", "type": "address"}, {"internalType": "address", "name": "_tokenB", "type": "address"}, {"internalType": "string", "name": "_name", "type": "string"}, {"internalType": "string", "name": "_symbol", "type": "string"}, {"internalType": "address", "name": "_owner", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "allowance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "name": "ERC20InsufficientAllowance", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "uint256", "name": "balance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "name": "ERC20InsufficientBalance", "type": "error"}, {"inputs": [{"internalType": "address", "name": "approver", "type": "address"}], "name": "ERC20InvalidApprover", "type": "error"}, {"inputs": [{"internalType": "address", "name": "receiver", "type": "address"}], "name": "ERC20InvalidReceiver", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}], "name": "ERC20InvalidSender", "type": "error"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}], "name": "ERC20InvalidSpender", "type": "error"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "OwnableInvalidOwner", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "OwnableUnauthorizedAccount", "type": "error"}, {"inputs": [], "name": "ReentrancyGuardReentrantCall", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "spender", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Approval", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "feeA", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "feeB", "type": "uint256"}], "name": "FeesCollected", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "provider", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amountA", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "amountB", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "liquidity", "type": "uint256"}], "name": "LiquidityAdded", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "provider", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amountA", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "amountB", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "liquidity", "type": "uint256"}], "name": "LiquidityRemoved", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": true, "internalType": "address", "name": "tokenIn", "type": "address"}, {"indexed": true, "internalType": "address", "name": "tokenOut", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amountIn", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "amountOut", "type": "uint256"}], "name": "<PERSON><PERSON><PERSON>", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Transfer", "type": "event"}, {"inputs": [], "name": "FEE_DENOMINATOR", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "FEE_RATE", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MINIMUM_LIQUIDITY", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "amountADesired", "type": "uint256"}, {"internalType": "uint256", "name": "amountBDesired", "type": "uint256"}, {"internalType": "uint256", "name": "amountAMin", "type": "uint256"}, {"internalType": "uint256", "name": "amountBMin", "type": "uint256"}, {"internalType": "address", "name": "to", "type": "address"}], "name": "addLiquidity", "outputs": [{"internalType": "uint256", "name": "amountA", "type": "uint256"}, {"internalType": "uint256", "name": "amountB", "type": "uint256"}, {"internalType": "uint256", "name": "liquidity", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}], "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "collectFees", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "emergencyWithdraw", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "amountOut", "type": "uint256"}, {"internalType": "uint256", "name": "reserveIn", "type": "uint256"}, {"internalType": "uint256", "name": "reserveOut", "type": "uint256"}], "name": "getAmountIn", "outputs": [{"internalType": "uint256", "name": "amountIn", "type": "uint256"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "amountIn", "type": "uint256"}, {"internalType": "uint256", "name": "reserveIn", "type": "uint256"}, {"internalType": "uint256", "name": "reserveOut", "type": "uint256"}], "name": "getAmountOut", "outputs": [{"internalType": "uint256", "name": "amountOut", "type": "uint256"}], "stateMutability": "pure", "type": "function"}, {"inputs": [], "name": "getPoolInfo", "outputs": [{"internalType": "uint256", "name": "_reserveA", "type": "uint256"}, {"internalType": "uint256", "name": "_reserveB", "type": "uint256"}, {"internalType": "uint256", "name": "_totalSupply", "type": "uint256"}, {"internalType": "uint256", "name": "_totalFeeA", "type": "uint256"}, {"internalType": "uint256", "name": "_totalFeeB", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getPrice", "outputs": [{"internalType": "uint256", "name": "priceAB", "type": "uint256"}, {"internalType": "uint256", "name": "priceBA", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "liquidity", "type": "uint256"}, {"internalType": "uint256", "name": "amountAMin", "type": "uint256"}, {"internalType": "uint256", "name": "amountBMin", "type": "uint256"}, {"internalType": "address", "name": "to", "type": "address"}], "name": "removeLiquidity", "outputs": [{"internalType": "uint256", "name": "amountA", "type": "uint256"}, {"internalType": "uint256", "name": "amountB", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "reserveA", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "reserveB", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "amountAIn", "type": "uint256"}, {"internalType": "uint256", "name": "amountBOutMin", "type": "uint256"}, {"internalType": "address", "name": "to", "type": "address"}], "name": "swapAForB", "outputs": [{"internalType": "uint256", "name": "amountBOut", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "amountBIn", "type": "uint256"}, {"internalType": "uint256", "name": "amountAOutMin", "type": "uint256"}, {"internalType": "address", "name": "to", "type": "address"}], "name": "swapBForA", "outputs": [{"internalType": "uint256", "name": "amountAOut", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "tokenA", "outputs": [{"internalType": "contract IERC20", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "tokenB", "outputs": [{"internalType": "contract IERC20", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalFeeA", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalFeeB", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "0x604060c0815234620004ae5762002325803803806200001e81620004b3565b928339810160a082820312620004ae576200003982620004d9565b91602062000049818301620004d9565b8286015190936001600160401b0391828111620004ae57816200006e918601620004ee565b90606085015190838211620004ae576200009160809162000099938801620004ee565b9501620004d9565b918151818111620003ae576003908154906001948583811c93168015620004a3575b888410146200048d578190601f9384811162000437575b508890848311600114620003d057600092620003c4575b505060001982851b1c191690851b1782555b8651928311620003ae5760049687548581811c91168015620003a3575b888210146200038e57908183869594931162000334575b5087918411600114620002c957600093620002bd575b505082841b92600019911b1c19161784555b6005556001600160a01b03908116938415620002a657600680546001600160a01b03198116871790915587519583929183167f8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e0600080a316941692848414620002745750821515806200026a575b156200023957505060805260a05251611dc49081620005618239608051818181610327015281816105af01528181610aa701528181610cbe015281816113fc01526114a3015260a05181818161038d01528181610616015281816109f301528181610c2e0152818161123001526115330152f35b845162461bcd60e51b815291820152600c60248201526b5a65726f206164647265737360a01b604482015260649150fd5b50831515620001c5565b62461bcd60e51b815291820152601060248201526f4964656e746963616c20746f6b656e7360801b6044820152606490fd5b8651631e4fbdf760e01b8152600081860152602490fd5b01519150388062000145565b9190859450601f1984169289600052886000209360005b8a8282106200031d575050851162000302575b50505050811b01845562000157565b01519060f884600019921b161c1916905538808080620002f3565b8385015187558998909601959384019301620002e0565b909192935088600052876000208380870160051c8201928a881062000384575b9188918897969594930160051c01915b828110620003745750506200012f565b6000815587965088910162000364565b9250819262000354565b602289634e487b7160e01b6000525260246000fd5b90607f169062000118565b634e487b7160e01b600052604160045260246000fd5b015190503880620000e9565b90879350601f19831691866000528a6000209260005b8c82821062000420575050841162000407575b505050811b018255620000fb565b015160001983871b60f8161c19169055388080620003f9565b8385015186558b97909501949384019301620003e6565b90915084600052886000208480850160051c8201928b861062000483575b918991869594930160051c01915b82811062000473575050620000d2565b6000815585945089910162000463565b9250819262000455565b634e487b7160e01b600052602260045260246000fd5b92607f1692620000bb565b600080fd5b6040519190601f01601f191682016001600160401b03811183821017620003ae57604052565b51906001600160a01b0382168203620004ae57565b919080601f84011215620004ae5782516001600160401b038111620003ae5760209062000524601f8201601f19168301620004b3565b92818452828287010111620004ae5760005b8181106200054c57508260009394955001015290565b85810183015184820184015282016200053656fe6040608081526004908136101561001557600080fd5b600091823560e01c8063054d50d4146117a357806306fdde03146116ad578063095ea7b3146116045780630e58123f1461142b5780630fc63d10146113e7578063150e02ed146113c857806318160ddd146113a957806319e36f3b1461138a57806323b872dd146112975780632d11c58a1461127b578063313ce5671461125f5780635f64b55b1461121b57806360246c88146111da57806370a08231146111a3578063715018a61461114657806385f8c259146110555780638da5cb5b1461102c57806395ccea6714610f8f57806395d89b4114610e8d57806398d5fdca14610db75780639a552bf214610bad5780639e71267214610b8e578063a9059cbb14610b5d578063ba9a7a5614610b40578063c87965721461093c578063d73792a91461091f578063dc5fa6c514610900578063dd62ed3e146108b7578063e0ab07721461051e578063e39b0eb5146102085763f2fde38b1461017657600080fd5b346102045760203660031901126102045761018f61182e565b90610198611c42565b6001600160a01b039182169283156101ee575050600654826bffffffffffffffffffffffff60a01b821617600655167f8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e08380a380f35b51631e4fbdf760e01b8152908101849052602490fd5b8280fd5b5082903461051a57608036600319011261051a57606435916001600160a01b0391908284169082359082860361051757610240611b41565b61024b831515611a15565b81156104d4576002549561027a61026d8861026860075487611946565b611966565b9761026860085486611946565b95610289602435891015611ab9565b610297604435881015611afd565b33156104bd5733835260209583875289842054858110610491578590338652858952038a8520558460025403600255838a518681527fddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef893392a3895163a9059cbb60e01b8082526001600160a01b038516838301908152602081018c9052909190899082908190604001038189887f0000000000000000000000000000000000000000000000000000000000000000165af1801561048757958994926103898e839896948e9a61046a575b5051988996879586948552840160209093929193604081019460018060a01b031681520152565b03927f0000000000000000000000000000000000000000000000000000000000000000165af190811561045f575091610420917f1dc8bb69df2b8e91fbdcbfcf93d951b3f0000f085a95fe3f7946d6161439245d93610432575b506103f08760075461190c565b6007556103ff8660085461190c565b60085587519182918789846040919493926060820195825260208201520152565b0390a260016005558351928352820152f35b61045190863d8811610458575b61044981836119dd565b810190611aa1565b50886103e3565b503d61043f565b8851903d90823e3d90fd5b61048090893d8b116104585761044981836119dd565b5038610362565b8c513d88823e3d90fd5b8a5163391434e360e21b8152339281019283526020830191909152604082018690529081906060010390fd5b8851634b637e8f60e11b8152808701849052602490fd5b865162461bcd60e51b8152602081860152601860248201527f496e76616c6964206c697175696469747920616d6f756e7400000000000000006044820152606490fd5b80fd5b5080fd5b5082346105175760a0366003190112610517578135926001600160a01b03602435608435828116908190036108b357610555611b41565b8015916105628315611a15565b6007548015806108a9575b1561084757509680935b85516323b872dd60e01b808252338a830190815230602082810191909152604082019590955290919084908290819060600103818c877f0000000000000000000000000000000000000000000000000000000000000000165af1801561083d57928a8a86948e9461061297610820575b508b519081523392810192835230602084015260408301949094529294859384929091839160600190565b03927f0000000000000000000000000000000000000000000000000000000000000000165af18015610816576107f9575b5060025480610692575050505061065f60249561066492611946565b611c6e565b6103e719810111610680575163ec442f0560e01b815291820152fd5b50634e487b7160e01b81526011909152fd5b8896976106ae6106a58388999798611946565b60075490611966565b6106c46106bb848b611946565b60085490611966565b9081808210911802189586156107b65761079f5750967fddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef839261070c8761079b999a9b611959565b6002558383528281528583208781540190558551878152a361073085600754611959565b60075561073f86600854611959565b600855815185815260208101879052604081018490527f64b83944e79c3ce8d4c297411de637c3e102d064677aac0c163976ebdcd6f50e90606090a2600160055551938493846040919493926060820195825260208201520152565b0390f35b845163ec442f0560e01b8152908101899052602490fd5b855162461bcd60e51b8152808301859052601d60248201527f496e73756666696369656e74206c6971756964697479206d696e7465640000006044820152606490fd5b61080f90823d84116104585761044981836119dd565b5088610643565b86513d89823e3d90fd5b61083690873d89116104585761044981836119dd565b508f6105e7565b88513d8b823e3d90fd5b9060085461085983610268838d611946565b9282841161087a57505050610872606435821015611afd565b968093610577565b8293506102689061088a93611946565b978811158061089d575b61087290611ab9565b50604435881015610894565b506008541561056d565b8480fd5b50503461051a578060031936011261051a57806020926108d561182e565b6108dd611844565b6001600160a01b0391821683526001865283832091168252845220549051908152f35b50503461051a578160031936011261051a576020906007549051908152f35b50503461051a578160031936011261051a57602090516127108152f35b509034610204578260031936011261020457610956611c42565b600954801580158091610b35575b15610afd57610a6c575b50600a5491826109b8575b837f49d512bf9cb224241c05691e73eb9fab078cf350c7dbcbcf66788f1fc0cc8b0b83600954600a5482519182526020820152a18060095580600a5580f35b600654825163a9059cbb60e01b81526001600160a01b03918216928101928352602083810195909552939184919082908790829060400103927f0000000000000000000000000000000000000000000000000000000000000000165af1918215610a62577f49d512bf9cb224241c05691e73eb9fab078cf350c7dbcbcf66788f1fc0cc8b0b921561097957610a5b9060203d6020116104585761044981836119dd565b5038610979565b81513d85823e3d90fd5b600654825163a9059cbb60e01b81526001600160a01b03918216858201908152602081810194909452909291839182908890829060400103927f0000000000000000000000000000000000000000000000000000000000000000165af18015610af3571561096e57610aec9060203d6020116104585761044981836119dd565b503861096e565b82513d86823e3d90fd5b825162461bcd60e51b81526020818601526012602482015271139bc81999595cc81d1bc818dbdb1b1958dd60721b6044820152606490fd5b50600a541515610964565b50503461051a578160031936011261051a57602090516103e88152f35b50503461051a578060031936011261051a57602090610b87610b7d61182e565b6024359033611b64565b5160018152f35b50503461051a578160031936011261051a57602090600a549051908152f35b50903461020457610bbd3661185a565b909391610bc8611b41565b801595610bd58715611884565b6001600160a01b0392610beb8185161515611a15565b610c07610bfe6008546007549086611986565b97881015611a55565b85516323b872dd60e01b8152338682019081523060208281019190915260408201869052997f00000000000000000000000000000000000000000000000000000000000000008716969290918b90829081906060010381888b5af18015610dad57610d90575b50601e850290858204601e141715610d7d57918991610c946127108b969504600a54611959565b600a55885163a9059cbb60e01b81526001600160a01b0390921697820197885260208801949094527f0000000000000000000000000000000000000000000000000000000000000000939093169583908190604001038184895af1908115610d725750610d55575b50610d0981600854611959565b600855610d188560075461190c565b600755835190815284868201527fcd3829a3813dc3cdd188fd3d01dcf3268c16be2fdd2dd21d0665418816e46062843392a4600160055551908152f35b610d6b90873d89116104585761044981836119dd565b5038610cfc565b8651903d90823e3d90fd5b634e487b7160e01b845260118752602484fd5b610da6908b3d8d116104585761044981836119dd565b5038610c6d565b89513d87823e3d90fd5b50913461051757806003193601126105175760075490811591821580610e82575b15610e505760085491670de0b6b3a764000093848402848104861485151715610e3d5783610e0591611966565b94808402938404141715610e2a575090610e1e91611966565b82519182526020820152f35b634e487b7160e01b815260118652602490fd5b634e487b7160e01b835260118852602483fd5b835162461bcd60e51b8152602081870152600c60248201526b4e6f206c697175696469747960a01b6044820152606490fd5b506008541515610dd8565b5091903461051a578160031936011261051a5780519180938054916001908360011c9260018516948515610f85575b6020958686108114610f7257858952908115610f4e5750600114610ef6575b61079b8787610eec828c03836119dd565b51918291826117e5565b81529295507f8a35acfbc15ff81a39ae7d344fd709f28e8600b4aa8c65c6b64bfe7fe36bd19b5b828410610f3b575050508261079b94610eec92820101943880610edb565b8054868501880152928601928101610f1d565b60ff19168887015250505050151560051b8301019250610eec8261079b3880610edb565b634e487b7160e01b845260228352602484fd5b93607f1693610ebc565b5090346102045780600319360112610204576020610ff292610faf61182e565b610fb7611c42565b600654845163a9059cbb60e01b81526001600160a01b0391821693810193845260243560208501529586938492899290918491604090910190565b0393165af19081156110235750611007575080f35b61101f9060203d6020116104585761044981836119dd565b5080f35b513d84823e3d90fd5b50503461051a578160031936011261051a5760065490516001600160a01b039091168152602090f35b5091903461051a57611066366117c6565b9190811561110b57818161109592151580611102575b611085906118c7565b6110908583106118c7565b611946565b91612710928381029381850414901517156110ef576110bb6110c693926110c09261190c565b61192f565b90611966565b91600183018093116110dc576020838351908152f35b634e487b7160e01b815260118452602490fd5b634e487b7160e01b855260118652602485fd5b5084151561107c565b835162461bcd60e51b81526020818801526015602482015274125b9d985b1a59081bdd5d1c1d5d08185b5bdd5b9d605a1b6044820152606490fd5b833461051757806003193601126105175761115f611c42565b600680546001600160a01b0319811690915581906001600160a01b03167f8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e08280a380f35b50503461051a57602036600319011261051a5760209181906001600160a01b036111cb61182e565b16815280845220549051908152f35b50503461051a578160031936011261051a5760a090600754906008549060025460095491600a54938151958652602086015284015260608301526080820152f35b50503461051a578160031936011261051a57517f00000000000000000000000000000000000000000000000000000000000000006001600160a01b03168152602090f35b50503461051a578160031936011261051a576020905160128152f35b50503461051a578160031936011261051a5760209051601e8152f35b508234610517576060366003190112610517576112b261182e565b6112ba611844565b916044359360018060a01b0383168083526001602052868320338452602052868320549160001983106112f6575b602088610b87898989611b64565b86831061135e57811561134757331561133057508252600160209081528683203384528152918690209085900390558290610b87876112e8565b8751634a1406b160e11b8152908101849052602490fd5b875163e602df0560e01b8152908101849052602490fd5b8751637dc7a0d960e11b8152339181019182526020820193909352604081018790528291506060010390fd5b50503461051a578160031936011261051a576020906008549051908152f35b50503461051a578160031936011261051a576020906002549051908152f35b50503461051a578160031936011261051a576020906009549051908152f35b50503461051a578160031936011261051a57517f00000000000000000000000000000000000000000000000000000000000000006001600160a01b03168152602090f35b5090346102045761143b3661185a565b909391611446611b41565b8015956114538715611884565b6001600160a01b03926114698185161515611a15565b61147c610bfe6007546008549086611986565b85516323b872dd60e01b8152338682019081523060208281019190915260408201869052997f00000000000000000000000000000000000000000000000000000000000000008716969290918b90829081906060010381888b5af18015610dad576115e7575b50601e850290858204601e141715610d7d579189916115096127108b969504600954611959565b600955885163a9059cbb60e01b81526001600160a01b0390921697820197885260208801949094527f0000000000000000000000000000000000000000000000000000000000000000939093169583908190604001038184895af1908115610d7257506115ca575b5061157e81600754611959565b60075561158d8560085461190c565b600855835190815284868201527fcd3829a3813dc3cdd188fd3d01dcf3268c16be2fdd2dd21d0665418816e46062843392a4600160055551908152f35b6115e090873d89116104585761044981836119dd565b5038611571565b6115fd908b3d8d116104585761044981836119dd565b50386114e2565b503461020457816003193601126102045761161d61182e565b602435903315611696576001600160a01b031691821561167f57508083602095338152600187528181208582528752205582519081527f8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b925843392a35160018152f35b8351634a1406b160e11b8152908101859052602490fd5b835163e602df0560e01b8152808401869052602490fd5b50903461020457826003193601126102045780519183600354906001908260011c92600181168015611799575b60209586861082146117865750848852908115611764575060011461170b575b61079b8686610eec828b03836119dd565b929550600383527fc2575a0e9e593c00f959f8c92f12db2869c3395a3b0502d05e2516446f71f85b5b828410611751575050508261079b94610eec9282010194386116fa565b8054868501880152928601928101611734565b60ff191687860152505050151560051b8301019250610eec8261079b386116fa565b634e487b7160e01b845260229052602483fd5b93607f16936116da565b50503461051a576020906117bf6117b9366117c6565b91611986565b9051908152f35b60609060031901126117e057600435906024359060443590565b600080fd5b6020808252825181830181905290939260005b82811061181a57505060409293506000838284010152601f8019910116010190565b8181018601518482016040015285016117f8565b600435906001600160a01b03821682036117e057565b602435906001600160a01b03821682036117e057565b60609060031901126117e05760043590602435906044356001600160a01b03811681036117e05790565b1561188b57565b60405162461bcd60e51b8152602060048201526014602482015273125b9d985b1a59081a5b9c1d5d08185b5bdd5b9d60621b6044820152606490fd5b156118ce57565b60405162461bcd60e51b8152602060048201526016602482015275496e73756666696369656e74206c697175696469747960501b6044820152606490fd5b9190820391821161191957565b634e487b7160e01b600052601160045260246000fd5b906126f29182810292818404149015171561191957565b8181029291811591840414171561191957565b9190820180921161191957565b8115611970570490565b634e487b7160e01b600052601260045260246000fd5b90611992821515611884565b6119b36119ac8215938415806119d4575b6110bb906118c7565b9384611946565b91612710808302928304141715611919576119d1926110c091611959565b90565b508515156119a3565b90601f8019910116810190811067ffffffffffffffff8211176119ff57604052565b634e487b7160e01b600052604160045260246000fd5b15611a1c57565b60405162461bcd60e51b8152602060048201526011602482015270125b9d985b1a59081c9958da5c1a595b9d607a1b6044820152606490fd5b15611a5c57565b60405162461bcd60e51b815260206004820152601a60248201527f496e73756666696369656e74206f757470757420616d6f756e740000000000006044820152606490fd5b908160209103126117e0575180151581036117e05790565b15611ac057565b60405162461bcd60e51b8152602060048201526015602482015274125b9cdd59999a58da595b9d081048185b5bdd5b9d605a1b6044820152606490fd5b15611b0457565b60405162461bcd60e51b8152602060048201526015602482015274125b9cdd59999a58da595b9d081088185b5bdd5b9d605a1b6044820152606490fd5b600260055414611b52576002600555565b604051633ee5aeb560e01b8152600490fd5b916001600160a01b03808416928315611c295716928315611c105760009083825281602052604082205490838210611bde575091604082827fddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef958760209652828652038282205586815220818154019055604051908152a3565b60405163391434e360e21b81526001600160a01b03919091166004820152602481019190915260448101839052606490fd5b60405163ec442f0560e01b815260006004820152602490fd5b604051634b637e8f60e11b815260006004820152602490fd5b6006546001600160a01b03163303611c5657565b60405163118cdaa760e01b8152336004820152602490fd5b600180821115611d8a57611d2b908083600160801b811015611d78575b80600160401b6004921015611d6b575b640100000000811015611d5e575b62010000811015611d51575b610100811015611d45575b6010811015611d39575b1015611d32575b600302811c611ce08185611966565b01811c611ced8185611966565b01811c611cfa8185611966565b01811c611d078185611966565b01811c611d148185611966565b01811c611d218185611966565b01901c8092611966565b8111900390565b811b611cd1565b811c9160021b91611cca565b60081c91811b91611cc0565b60101c9160081b91611cb5565b60201c9160101b91611ca9565b60401c9160201b91611c9b565b50600160401b9050608084901c611c8b565b509056fea2646970667358221220e7f10dd0591c96a1aaf40df37dccde1377fd9469bc4f922eb4070faf841a7adb64736f6c63430008180033", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}