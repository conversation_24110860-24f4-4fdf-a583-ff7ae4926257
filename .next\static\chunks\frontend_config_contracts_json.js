"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["frontend_config_contracts_json"],{

/***/ "./frontend/config/contracts.json":
/*!****************************************!*\
  !*** ./frontend/config/contracts.json ***!
  \****************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

module.exports = /*#__PURE__*/JSON.parse('{"contracts":{"SimpleToken":"0xa513E6E4b8f2a923D98304ec87F64353C4D5C853","Web3NFT":"0x2279B7A0a67DB372996a5FaB50D91eAA73d2eBe6","NFTMarketplace":"0x8A791620dd6260079BF849Dc5567aDC3F2FdC318","SimpleDAO":"0x610178dA211FEF7D417bC0e6FeD39F05609AD788"},"network":{"chainId":31337,"name":"localhost"}}');

/***/ })

}]);