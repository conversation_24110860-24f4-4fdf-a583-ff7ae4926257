"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./frontend/config/contracts.json":
/*!****************************************!*\
  !*** ./frontend/config/contracts.json ***!
  \****************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

module.exports = /*#__PURE__*/JSON.parse('{"contracts":{"SimpleToken":"0x9fE46736679d2D9a65F0992F2272dE9f3c7fa6e0","Web3NFT":"0xCf7Ed3AccA5a467e9e704C703E8D87F634fB0Fc9","NFTMarketplace":"0xDc64a140Aa3E981100a9becA4E685f962f0cF6C9","SimpleDAO":"0x5FC8d32690cc91D4c39d9d3abcBD16989F875707"},"network":{"chainId":31337,"name":"localhost"}}');

/***/ })

});