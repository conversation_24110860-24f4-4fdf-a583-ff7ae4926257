{"id": "e07c9e8b00e10ba55986a434bc65459e", "_format": "hh-sol-build-info-1", "solcVersion": "0.8.24", "solcLongVersion": "0.8.24+commit.e11b9ed9", "input": {"language": "Solidity", "sources": {"@openzeppelin/contracts/access/Ownable.sol": {"content": "// SPDX-License-Identifier: MIT\n// OpenZeppelin Contracts (last updated v5.0.0) (access/Ownable.sol)\n\npragma solidity ^0.8.20;\n\nimport {Context} from \"../utils/Context.sol\";\n\n/**\n * @dev Contract module which provides a basic access control mechanism, where\n * there is an account (an owner) that can be granted exclusive access to\n * specific functions.\n *\n * The initial owner is set to the address provided by the deployer. This can\n * later be changed with {transferOwnership}.\n *\n * This module is used through inheritance. It will make available the modifier\n * `onlyOwner`, which can be applied to your functions to restrict their use to\n * the owner.\n */\nabstract contract Ownable is Context {\n    address private _owner;\n\n    /**\n     * @dev The caller account is not authorized to perform an operation.\n     */\n    error OwnableUnauthorizedAccount(address account);\n\n    /**\n     * @dev The owner is not a valid owner account. (eg. `address(0)`)\n     */\n    error OwnableInvalidOwner(address owner);\n\n    event OwnershipTransferred(address indexed previousOwner, address indexed newOwner);\n\n    /**\n     * @dev Initializes the contract setting the address provided by the deployer as the initial owner.\n     */\n    constructor(address initialOwner) {\n        if (initialOwner == address(0)) {\n            revert OwnableInvalidOwner(address(0));\n        }\n        _transferOwnership(initialOwner);\n    }\n\n    /**\n     * @dev Throws if called by any account other than the owner.\n     */\n    modifier onlyOwner() {\n        _checkOwner();\n        _;\n    }\n\n    /**\n     * @dev Returns the address of the current owner.\n     */\n    function owner() public view virtual returns (address) {\n        return _owner;\n    }\n\n    /**\n     * @dev Throws if the sender is not the owner.\n     */\n    function _checkOwner() internal view virtual {\n        if (owner() != _msgSender()) {\n            revert OwnableUnauthorizedAccount(_msgSender());\n        }\n    }\n\n    /**\n     * @dev Leaves the contract without owner. It will not be possible to call\n     * `onlyOwner` functions. Can only be called by the current owner.\n     *\n     * NOTE: Renouncing ownership will leave the contract without an owner,\n     * thereby disabling any functionality that is only available to the owner.\n     */\n    function renounceOwnership() public virtual onlyOwner {\n        _transferOwnership(address(0));\n    }\n\n    /**\n     * @dev Transfers ownership of the contract to a new account (`newOwner`).\n     * Can only be called by the current owner.\n     */\n    function transferOwnership(address newOwner) public virtual onlyOwner {\n        if (newOwner == address(0)) {\n            revert OwnableInvalidOwner(address(0));\n        }\n        _transferOwnership(newOwner);\n    }\n\n    /**\n     * @dev Transfers ownership of the contract to a new account (`newOwner`).\n     * Internal function without access restriction.\n     */\n    function _transferOwnership(address newOwner) internal virtual {\n        address oldOwner = _owner;\n        _owner = newOwner;\n        emit OwnershipTransferred(oldOwner, newOwner);\n    }\n}\n"}, "@openzeppelin/contracts/token/ERC20/IERC20.sol": {"content": "// SPDX-License-Identifier: MIT\n// OpenZeppelin Contracts (last updated v5.4.0) (token/ERC20/IERC20.sol)\n\npragma solidity >=0.4.16;\n\n/**\n * @dev Interface of the ERC-20 standard as defined in the ERC.\n */\ninterface IERC20 {\n    /**\n     * @dev Emitted when `value` tokens are moved from one account (`from`) to\n     * another (`to`).\n     *\n     * Note that `value` may be zero.\n     */\n    event Transfer(address indexed from, address indexed to, uint256 value);\n\n    /**\n     * @dev Emitted when the allowance of a `spender` for an `owner` is set by\n     * a call to {approve}. `value` is the new allowance.\n     */\n    event Approval(address indexed owner, address indexed spender, uint256 value);\n\n    /**\n     * @dev Returns the value of tokens in existence.\n     */\n    function totalSupply() external view returns (uint256);\n\n    /**\n     * @dev Returns the value of tokens owned by `account`.\n     */\n    function balanceOf(address account) external view returns (uint256);\n\n    /**\n     * @dev Moves a `value` amount of tokens from the caller's account to `to`.\n     *\n     * Returns a boolean value indicating whether the operation succeeded.\n     *\n     * Emits a {Transfer} event.\n     */\n    function transfer(address to, uint256 value) external returns (bool);\n\n    /**\n     * @dev Returns the remaining number of tokens that `spender` will be\n     * allowed to spend on behalf of `owner` through {transferFrom}. This is\n     * zero by default.\n     *\n     * This value changes when {approve} or {transferFrom} are called.\n     */\n    function allowance(address owner, address spender) external view returns (uint256);\n\n    /**\n     * @dev Sets a `value` amount of tokens as the allowance of `spender` over the\n     * caller's tokens.\n     *\n     * Returns a boolean value indicating whether the operation succeeded.\n     *\n     * IMPORTANT: Beware that changing an allowance with this method brings the risk\n     * that someone may use both the old and the new allowance by unfortunate\n     * transaction ordering. One possible solution to mitigate this race\n     * condition is to first reduce the spender's allowance to 0 and set the\n     * desired value afterwards:\n     * https://github.com/ethereum/EIPs/issues/20#issuecomment-*********\n     *\n     * Emits an {Approval} event.\n     */\n    function approve(address spender, uint256 value) external returns (bool);\n\n    /**\n     * @dev Moves a `value` amount of tokens from `from` to `to` using the\n     * allowance mechanism. `value` is then deducted from the caller's\n     * allowance.\n     *\n     * Returns a boolean value indicating whether the operation succeeded.\n     *\n     * Emits a {Transfer} event.\n     */\n    function transferFrom(address from, address to, uint256 value) external returns (bool);\n}\n"}, "@openzeppelin/contracts/utils/Context.sol": {"content": "// SPDX-License-Identifier: MIT\n// OpenZeppelin Contracts (last updated v5.0.1) (utils/Context.sol)\n\npragma solidity ^0.8.20;\n\n/**\n * @dev Provides information about the current execution context, including the\n * sender of the transaction and its data. While these are generally available\n * via msg.sender and msg.data, they should not be accessed in such a direct\n * manner, since when dealing with meta-transactions the account sending and\n * paying for execution may not be the actual sender (as far as an application\n * is concerned).\n *\n * This contract is only required for intermediate, library-like contracts.\n */\nabstract contract Context {\n    function _msgSender() internal view virtual returns (address) {\n        return msg.sender;\n    }\n\n    function _msgData() internal view virtual returns (bytes calldata) {\n        return msg.data;\n    }\n\n    function _contextSuffixLength() internal view virtual returns (uint256) {\n        return 0;\n    }\n}\n"}, "@openzeppelin/contracts/utils/ReentrancyGuard.sol": {"content": "// SPDX-License-Identifier: MIT\n// OpenZeppelin Contracts (last updated v5.1.0) (utils/ReentrancyGuard.sol)\n\npragma solidity ^0.8.20;\n\n/**\n * @dev Contract module that helps prevent reentrant calls to a function.\n *\n * Inheriting from `ReentrancyGuard` will make the {nonReentrant} modifier\n * available, which can be applied to functions to make sure there are no nested\n * (reentrant) calls to them.\n *\n * Note that because there is a single `nonReentrant` guard, functions marked as\n * `nonReentrant` may not call one another. This can be worked around by making\n * those functions `private`, and then adding `external` `nonReentrant` entry\n * points to them.\n *\n * TIP: If EIP-1153 (transient storage) is available on the chain you're deploying at,\n * consider using {ReentrancyGuardTransient} instead.\n *\n * TIP: If you would like to learn more about reentrancy and alternative ways\n * to protect against it, check out our blog post\n * https://blog.openzeppelin.com/reentrancy-after-istanbul/[Reentrancy After Istanbul].\n */\nabstract contract ReentrancyGuard {\n    // Booleans are more expensive than uint256 or any type that takes up a full\n    // word because each write operation emits an extra SLOAD to first read the\n    // slot's contents, replace the bits taken up by the boolean, and then write\n    // back. This is the compiler's defense against contract upgrades and\n    // pointer aliasing, and it cannot be disabled.\n\n    // The values being non-zero value makes deployment a bit more expensive,\n    // but in exchange the refund on every call to nonReentrant will be lower in\n    // amount. Since refunds are capped to a percentage of the total\n    // transaction's gas, it is best to keep them low in cases like this one, to\n    // increase the likelihood of the full refund coming into effect.\n    uint256 private constant NOT_ENTERED = 1;\n    uint256 private constant ENTERED = 2;\n\n    uint256 private _status;\n\n    /**\n     * @dev Unauthorized reentrant call.\n     */\n    error ReentrancyGuardReentrantCall();\n\n    constructor() {\n        _status = NOT_ENTERED;\n    }\n\n    /**\n     * @dev Prevents a contract from calling itself, directly or indirectly.\n     * Calling a `nonReentrant` function from another `nonReentrant`\n     * function is not supported. It is possible to prevent this from happening\n     * by making the `nonReentrant` function external, and making it call a\n     * `private` function that does the actual work.\n     */\n    modifier nonReentrant() {\n        _nonReentrantBefore();\n        _;\n        _nonReentrantAfter();\n    }\n\n    function _nonReentrantBefore() private {\n        // On the first call to nonReentrant, _status will be NOT_ENTERED\n        if (_status == ENTERED) {\n            revert ReentrancyGuardReentrantCall();\n        }\n\n        // Any calls to nonReentrant after this point will fail\n        _status = ENTERED;\n    }\n\n    function _nonReentrantAfter() private {\n        // By storing the original value once again, a refund is triggered (see\n        // https://eips.ethereum.org/EIPS/eip-2200)\n        _status = NOT_ENTERED;\n    }\n\n    /**\n     * @dev Returns true if the reentrancy guard is currently set to \"entered\", which indicates there is a\n     * `nonReentrant` function in the call stack.\n     */\n    function _reentrancyGuardEntered() internal view returns (bool) {\n        return _status == ENTERED;\n    }\n}\n"}, "contracts/dao/SimpleDAO.sol": {"content": "// SPDX-License-Identifier: MIT\npragma solidity ^0.8.24;\n\nimport \"@openzeppelin/contracts/token/ERC20/IERC20.sol\";\nimport \"@openzeppelin/contracts/access/Ownable.sol\";\nimport \"@openzeppelin/contracts/utils/ReentrancyGuard.sol\";\n\n/**\n * @title SimpleDAO\n * @dev 简化的 DAO 治理合约\n * \n * 功能特性：\n * - 基于代币的投票权重\n * - 提案创建和执行\n * - 投票机制\n * - 资金管理\n */\ncontract SimpleDAO is Ownable, ReentrancyGuard {\n    \n    IERC20 public governanceToken;\n    \n    // 提案状态\n    enum ProposalState { PENDING, ACTIVE, SUCCEEDED, DEFEATED, EXECUTED }\n    \n    // 提案类型\n    enum ProposalType { GENERAL, FUNDING, PARAMETER_CHANGE }\n    \n    // 提案结构\n    struct Proposal {\n        uint256 id;\n        address proposer;\n        string description;\n        ProposalType proposalType;\n        uint256 requestedAmount;\n        address beneficiary;\n        uint256 forVotes;\n        uint256 againstVotes;\n        uint256 startTime;\n        uint256 endTime;\n        ProposalState state;\n        bool executed;\n    }\n    \n    mapping(uint256 => Proposal) public proposals;\n    mapping(uint256 => mapping(address => bool)) public hasVoted;\n    mapping(uint256 => mapping(address => bool)) public voteChoice;\n    uint256 public proposalCount;\n    \n    // 治理参数\n    uint256 public votingPeriod = 3 days;\n    uint256 public proposalThreshold = 1000 * 10**18; // 1000 代币\n    uint256 public quorumPercentage = 4; // 4%\n    \n    // DAO 资金池\n    uint256 public treasuryBalance;\n    mapping(address => uint256) public memberContributions;\n    \n    // 事件\n    event ProposalCreated(\n        uint256 indexed proposalId,\n        address indexed proposer,\n        string description,\n        ProposalType proposalType\n    );\n    event VoteCast(\n        uint256 indexed proposalId,\n        address indexed voter,\n        bool support,\n        uint256 weight\n    );\n    event ProposalExecuted(uint256 indexed proposalId);\n    event FundsDeposited(address indexed depositor, uint256 amount);\n    event FundsWithdrawn(address indexed recipient, uint256 amount);\n    \n    constructor(\n        address _governanceToken,\n        address _owner\n    ) Ownable(_owner) {\n        governanceToken = IERC20(_governanceToken);\n    }\n    \n    /**\n     * @dev 创建提案\n     */\n    function createProposal(\n        string memory description,\n        ProposalType proposalType,\n        uint256 requestedAmount,\n        address beneficiary\n    ) external returns (uint256) {\n        require(bytes(description).length > 0, \"Description cannot be empty\");\n        require(\n            governanceToken.balanceOf(msg.sender) >= proposalThreshold,\n            \"Insufficient tokens to create proposal\"\n        );\n        \n        uint256 proposalId = proposalCount++;\n        \n        proposals[proposalId] = Proposal({\n            id: proposalId,\n            proposer: msg.sender,\n            description: description,\n            proposalType: proposalType,\n            requestedAmount: requestedAmount,\n            beneficiary: beneficiary,\n            forVotes: 0,\n            againstVotes: 0,\n            startTime: block.timestamp,\n            endTime: block.timestamp + votingPeriod,\n            state: ProposalState.ACTIVE,\n            executed: false\n        });\n        \n        emit ProposalCreated(proposalId, msg.sender, description, proposalType);\n        \n        return proposalId;\n    }\n    \n    /**\n     * @dev 投票\n     */\n    function vote(uint256 proposalId, bool support) external {\n        Proposal storage proposal = proposals[proposalId];\n        require(proposal.state == ProposalState.ACTIVE, \"Proposal not active\");\n        require(block.timestamp <= proposal.endTime, \"Voting period ended\");\n        require(!hasVoted[proposalId][msg.sender], \"Already voted\");\n        \n        uint256 weight = governanceToken.balanceOf(msg.sender);\n        require(weight > 0, \"No voting power\");\n        \n        hasVoted[proposalId][msg.sender] = true;\n        voteChoice[proposalId][msg.sender] = support;\n        \n        if (support) {\n            proposal.forVotes += weight;\n        } else {\n            proposal.againstVotes += weight;\n        }\n        \n        emit VoteCast(proposalId, msg.sender, support, weight);\n    }\n    \n    /**\n     * @dev 更新提案状态\n     */\n    function updateProposalState(uint256 proposalId) public {\n        Proposal storage proposal = proposals[proposalId];\n        require(proposal.state == ProposalState.ACTIVE, \"Proposal not active\");\n        \n        if (block.timestamp > proposal.endTime) {\n            uint256 totalVotes = proposal.forVotes + proposal.againstVotes;\n            uint256 totalSupply = governanceToken.totalSupply();\n            uint256 quorum = (totalSupply * quorumPercentage) / 100;\n            \n            if (totalVotes >= quorum && proposal.forVotes > proposal.againstVotes) {\n                proposal.state = ProposalState.SUCCEEDED;\n            } else {\n                proposal.state = ProposalState.DEFEATED;\n            }\n        }\n    }\n    \n    /**\n     * @dev 执行提案\n     */\n    function executeProposal(uint256 proposalId) external nonReentrant {\n        Proposal storage proposal = proposals[proposalId];\n        updateProposalState(proposalId);\n        \n        require(proposal.state == ProposalState.SUCCEEDED, \"Proposal not succeeded\");\n        require(!proposal.executed, \"Proposal already executed\");\n        \n        proposal.executed = true;\n        proposal.state = ProposalState.EXECUTED;\n        \n        if (proposal.proposalType == ProposalType.FUNDING) {\n            require(proposal.requestedAmount <= treasuryBalance, \"Insufficient treasury funds\");\n            require(proposal.beneficiary != address(0), \"Invalid beneficiary\");\n            \n            treasuryBalance -= proposal.requestedAmount;\n            \n            (bool success, ) = payable(proposal.beneficiary).call{value: proposal.requestedAmount}(\"\");\n            require(success, \"Transfer failed\");\n            \n            emit FundsWithdrawn(proposal.beneficiary, proposal.requestedAmount);\n        }\n        \n        emit ProposalExecuted(proposalId);\n    }\n    \n    /**\n     * @dev 向 DAO 资金池存入资金\n     */\n    function depositFunds() external payable nonReentrant {\n        require(msg.value > 0, \"Must deposit some ETH\");\n        \n        treasuryBalance += msg.value;\n        memberContributions[msg.sender] += msg.value;\n        \n        emit FundsDeposited(msg.sender, msg.value);\n    }\n    \n    /**\n     * @dev 设置投票期限\n     */\n    function setVotingPeriod(uint256 newPeriod) external onlyOwner {\n        require(newPeriod > 0, \"Invalid period\");\n        votingPeriod = newPeriod;\n    }\n    \n    /**\n     * @dev 设置提案阈值\n     */\n    function setProposalThreshold(uint256 newThreshold) external onlyOwner {\n        proposalThreshold = newThreshold;\n    }\n    \n    /**\n     * @dev 设置法定人数百分比\n     */\n    function setQuorumPercentage(uint256 newPercentage) external onlyOwner {\n        require(newPercentage <= 100, \"Invalid percentage\");\n        quorumPercentage = newPercentage;\n    }\n    \n    /**\n     * @dev 获取提案信息\n     */\n    function getProposal(uint256 proposalId) external view returns (\n        uint256 id,\n        address proposer,\n        string memory description,\n        ProposalType proposalType,\n        uint256 requestedAmount,\n        address beneficiary,\n        uint256 forVotes,\n        uint256 againstVotes,\n        uint256 startTime,\n        uint256 endTime,\n        ProposalState state,\n        bool executed\n    ) {\n        Proposal storage proposal = proposals[proposalId];\n        return (\n            proposal.id,\n            proposal.proposer,\n            proposal.description,\n            proposal.proposalType,\n            proposal.requestedAmount,\n            proposal.beneficiary,\n            proposal.forVotes,\n            proposal.againstVotes,\n            proposal.startTime,\n            proposal.endTime,\n            proposal.state,\n            proposal.executed\n        );\n    }\n    \n    /**\n     * @dev 获取用户投票信息\n     */\n    function getUserVote(uint256 proposalId, address user) external view returns (bool voted, bool choice) {\n        return (hasVoted[proposalId][user], voteChoice[proposalId][user]);\n    }\n    \n    // 接收 ETH\n    receive() external payable {\n        treasuryBalance += msg.value;\n        memberContributions[msg.sender] += msg.value;\n        emit FundsDeposited(msg.sender, msg.value);\n    }\n}\n"}}, "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "output": {"sources": {"@openzeppelin/contracts/access/Ownable.sol": {"ast": {"absolutePath": "@openzeppelin/contracts/access/Ownable.sol", "exportedSymbols": {"Context": [255], "Ownable": [147]}, "id": 148, "license": "MIT", "nodeType": "SourceUnit", "nodes": [{"id": 1, "literals": ["solidity", "^", "0.8", ".20"], "nodeType": "PragmaDirective", "src": "102:24:0"}, {"absolutePath": "@openzeppelin/contracts/utils/Context.sol", "file": "../utils/Context.sol", "id": 3, "nameLocation": "-1:-1:-1", "nodeType": "ImportDirective", "scope": 148, "sourceUnit": 256, "src": "128:45:0", "symbolAliases": [{"foreign": {"id": 2, "name": "Context", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 255, "src": "136:7:0", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"abstract": true, "baseContracts": [{"baseName": {"id": 5, "name": "Context", "nameLocations": ["692:7:0"], "nodeType": "IdentifierPath", "referencedDeclaration": 255, "src": "692:7:0"}, "id": 6, "nodeType": "InheritanceSpecifier", "src": "692:7:0"}], "canonicalName": "Ownable", "contractDependencies": [], "contractKind": "contract", "documentation": {"id": 4, "nodeType": "StructuredDocumentation", "src": "175:487:0", "text": " @dev Contract module which provides a basic access control mechanism, where\n there is an account (an owner) that can be granted exclusive access to\n specific functions.\n The initial owner is set to the address provided by the deployer. This can\n later be changed with {transferOwnership}.\n This module is used through inheritance. It will make available the modifier\n `onlyOwner`, which can be applied to your functions to restrict their use to\n the owner."}, "fullyImplemented": true, "id": 147, "linearizedBaseContracts": [147, 255], "name": "Ownable", "nameLocation": "681:7:0", "nodeType": "ContractDefinition", "nodes": [{"constant": false, "id": 8, "mutability": "mutable", "name": "_owner", "nameLocation": "722:6:0", "nodeType": "VariableDeclaration", "scope": 147, "src": "706:22:0", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 7, "name": "address", "nodeType": "ElementaryTypeName", "src": "706:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "private"}, {"documentation": {"id": 9, "nodeType": "StructuredDocumentation", "src": "735:85:0", "text": " @dev The caller account is not authorized to perform an operation."}, "errorSelector": "118cdaa7", "id": 13, "name": "OwnableUnauthorizedAccount", "nameLocation": "831:26:0", "nodeType": "ErrorDefinition", "parameters": {"id": 12, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11, "mutability": "mutable", "name": "account", "nameLocation": "866:7:0", "nodeType": "VariableDeclaration", "scope": 13, "src": "858:15:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 10, "name": "address", "nodeType": "ElementaryTypeName", "src": "858:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "857:17:0"}, "src": "825:50:0"}, {"documentation": {"id": 14, "nodeType": "StructuredDocumentation", "src": "881:82:0", "text": " @dev The owner is not a valid owner account. (eg. `address(0)`)"}, "errorSelector": "1e4fbdf7", "id": 18, "name": "OwnableInvalidOwner", "nameLocation": "974:19:0", "nodeType": "ErrorDefinition", "parameters": {"id": 17, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 16, "mutability": "mutable", "name": "owner", "nameLocation": "1002:5:0", "nodeType": "VariableDeclaration", "scope": 18, "src": "994:13:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 15, "name": "address", "nodeType": "ElementaryTypeName", "src": "994:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "993:15:0"}, "src": "968:41:0"}, {"anonymous": false, "eventSelector": "8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e0", "id": 24, "name": "OwnershipTransferred", "nameLocation": "1021:20:0", "nodeType": "EventDefinition", "parameters": {"id": 23, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 20, "indexed": true, "mutability": "mutable", "name": "previousOwner", "nameLocation": "1058:13:0", "nodeType": "VariableDeclaration", "scope": 24, "src": "1042:29:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 19, "name": "address", "nodeType": "ElementaryTypeName", "src": "1042:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 22, "indexed": true, "mutability": "mutable", "name": "new<PERSON>wner", "nameLocation": "1089:8:0", "nodeType": "VariableDeclaration", "scope": 24, "src": "1073:24:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 21, "name": "address", "nodeType": "ElementaryTypeName", "src": "1073:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "1041:57:0"}, "src": "1015:84:0"}, {"body": {"id": 49, "nodeType": "Block", "src": "1259:153:0", "statements": [{"condition": {"commonType": {"typeIdentifier": "t_address", "typeString": "address"}, "id": 35, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 30, "name": "initialOwner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 27, "src": "1273:12:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"arguments": [{"hexValue": "30", "id": 33, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "1297:1:0", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}], "id": 32, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "1289:7:0", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 31, "name": "address", "nodeType": "ElementaryTypeName", "src": "1289:7:0", "typeDescriptions": {}}}, "id": 34, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1289:10:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "src": "1273:26:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 44, "nodeType": "IfStatement", "src": "1269:95:0", "trueBody": {"id": 43, "nodeType": "Block", "src": "1301:63:0", "statements": [{"errorCall": {"arguments": [{"arguments": [{"hexValue": "30", "id": 39, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "1350:1:0", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}], "id": 38, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "1342:7:0", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 37, "name": "address", "nodeType": "ElementaryTypeName", "src": "1342:7:0", "typeDescriptions": {}}}, "id": 40, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1342:10:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 36, "name": "OwnableInvalidOwner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 18, "src": "1322:19:0", "typeDescriptions": {"typeIdentifier": "t_function_error_pure$_t_address_$returns$__$", "typeString": "function (address) pure"}}, "id": 41, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1322:31:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 42, "nodeType": "RevertStatement", "src": "1315:38:0"}]}}, {"expression": {"arguments": [{"id": 46, "name": "initialOwner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 27, "src": "1392:12:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 45, "name": "_transferOwnership", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 146, "src": "1373:18:0", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_address_$returns$__$", "typeString": "function (address)"}}, "id": 47, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1373:32:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 48, "nodeType": "ExpressionStatement", "src": "1373:32:0"}]}, "documentation": {"id": 25, "nodeType": "StructuredDocumentation", "src": "1105:115:0", "text": " @dev Initializes the contract setting the address provided by the deployer as the initial owner."}, "id": 50, "implemented": true, "kind": "constructor", "modifiers": [], "name": "", "nameLocation": "-1:-1:-1", "nodeType": "FunctionDefinition", "parameters": {"id": 28, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 27, "mutability": "mutable", "name": "initialOwner", "nameLocation": "1245:12:0", "nodeType": "VariableDeclaration", "scope": 50, "src": "1237:20:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 26, "name": "address", "nodeType": "ElementaryTypeName", "src": "1237:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "1236:22:0"}, "returnParameters": {"id": 29, "nodeType": "ParameterList", "parameters": [], "src": "1259:0:0"}, "scope": 147, "src": "1225:187:0", "stateMutability": "nonpayable", "virtual": false, "visibility": "internal"}, {"body": {"id": 57, "nodeType": "Block", "src": "1521:41:0", "statements": [{"expression": {"arguments": [], "expression": {"argumentTypes": [], "id": 53, "name": "_checkOwner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 84, "src": "1531:11:0", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$__$returns$__$", "typeString": "function () view"}}, "id": 54, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1531:13:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 55, "nodeType": "ExpressionStatement", "src": "1531:13:0"}, {"id": 56, "nodeType": "PlaceholderStatement", "src": "1554:1:0"}]}, "documentation": {"id": 51, "nodeType": "StructuredDocumentation", "src": "1418:77:0", "text": " @dev Throws if called by any account other than the owner."}, "id": 58, "name": "only<PERSON><PERSON>er", "nameLocation": "1509:9:0", "nodeType": "ModifierDefinition", "parameters": {"id": 52, "nodeType": "ParameterList", "parameters": [], "src": "1518:2:0"}, "src": "1500:62:0", "virtual": false, "visibility": "internal"}, {"body": {"id": 66, "nodeType": "Block", "src": "1693:30:0", "statements": [{"expression": {"id": 64, "name": "_owner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 8, "src": "1710:6:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "functionReturnParameters": 63, "id": 65, "nodeType": "Return", "src": "1703:13:0"}]}, "documentation": {"id": 59, "nodeType": "StructuredDocumentation", "src": "1568:65:0", "text": " @dev Returns the address of the current owner."}, "functionSelector": "8da5cb5b", "id": 67, "implemented": true, "kind": "function", "modifiers": [], "name": "owner", "nameLocation": "1647:5:0", "nodeType": "FunctionDefinition", "parameters": {"id": 60, "nodeType": "ParameterList", "parameters": [], "src": "1652:2:0"}, "returnParameters": {"id": 63, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 62, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 67, "src": "1684:7:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 61, "name": "address", "nodeType": "ElementaryTypeName", "src": "1684:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "1683:9:0"}, "scope": 147, "src": "1638:85:0", "stateMutability": "view", "virtual": true, "visibility": "public"}, {"body": {"id": 83, "nodeType": "Block", "src": "1841:117:0", "statements": [{"condition": {"commonType": {"typeIdentifier": "t_address", "typeString": "address"}, "id": 75, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"arguments": [], "expression": {"argumentTypes": [], "id": 71, "name": "owner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 67, "src": "1855:5:0", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$__$returns$_t_address_$", "typeString": "function () view returns (address)"}}, "id": 72, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1855:7:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "BinaryOperation", "operator": "!=", "rightExpression": {"arguments": [], "expression": {"argumentTypes": [], "id": 73, "name": "_msgSender", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 237, "src": "1866:10:0", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$__$returns$_t_address_$", "typeString": "function () view returns (address)"}}, "id": 74, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1866:12:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "src": "1855:23:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 82, "nodeType": "IfStatement", "src": "1851:101:0", "trueBody": {"id": 81, "nodeType": "Block", "src": "1880:72:0", "statements": [{"errorCall": {"arguments": [{"arguments": [], "expression": {"argumentTypes": [], "id": 77, "name": "_msgSender", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 237, "src": "1928:10:0", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$__$returns$_t_address_$", "typeString": "function () view returns (address)"}}, "id": 78, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1928:12:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 76, "name": "OwnableUnauthorizedAccount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 13, "src": "1901:26:0", "typeDescriptions": {"typeIdentifier": "t_function_error_pure$_t_address_$returns$__$", "typeString": "function (address) pure"}}, "id": 79, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1901:40:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 80, "nodeType": "RevertStatement", "src": "1894:47:0"}]}}]}, "documentation": {"id": 68, "nodeType": "StructuredDocumentation", "src": "1729:62:0", "text": " @dev Throws if the sender is not the owner."}, "id": 84, "implemented": true, "kind": "function", "modifiers": [], "name": "_checkOwner", "nameLocation": "1805:11:0", "nodeType": "FunctionDefinition", "parameters": {"id": 69, "nodeType": "ParameterList", "parameters": [], "src": "1816:2:0"}, "returnParameters": {"id": 70, "nodeType": "ParameterList", "parameters": [], "src": "1841:0:0"}, "scope": 147, "src": "1796:162:0", "stateMutability": "view", "virtual": true, "visibility": "internal"}, {"body": {"id": 97, "nodeType": "Block", "src": "2347:47:0", "statements": [{"expression": {"arguments": [{"arguments": [{"hexValue": "30", "id": 93, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "2384:1:0", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}], "id": 92, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "2376:7:0", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 91, "name": "address", "nodeType": "ElementaryTypeName", "src": "2376:7:0", "typeDescriptions": {}}}, "id": 94, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2376:10:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 90, "name": "_transferOwnership", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 146, "src": "2357:18:0", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_address_$returns$__$", "typeString": "function (address)"}}, "id": 95, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2357:30:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 96, "nodeType": "ExpressionStatement", "src": "2357:30:0"}]}, "documentation": {"id": 85, "nodeType": "StructuredDocumentation", "src": "1964:324:0", "text": " @dev Leaves the contract without owner. It will not be possible to call\n `onlyOwner` functions. Can only be called by the current owner.\n NOTE: Renouncing ownership will leave the contract without an owner,\n thereby disabling any functionality that is only available to the owner."}, "functionSelector": "715018a6", "id": 98, "implemented": true, "kind": "function", "modifiers": [{"id": 88, "kind": "modifierInvocation", "modifierName": {"id": 87, "name": "only<PERSON><PERSON>er", "nameLocations": ["2337:9:0"], "nodeType": "IdentifierPath", "referencedDeclaration": 58, "src": "2337:9:0"}, "nodeType": "ModifierInvocation", "src": "2337:9:0"}], "name": "renounceOwnership", "nameLocation": "2302:17:0", "nodeType": "FunctionDefinition", "parameters": {"id": 86, "nodeType": "ParameterList", "parameters": [], "src": "2319:2:0"}, "returnParameters": {"id": 89, "nodeType": "ParameterList", "parameters": [], "src": "2347:0:0"}, "scope": 147, "src": "2293:101:0", "stateMutability": "nonpayable", "virtual": true, "visibility": "public"}, {"body": {"id": 125, "nodeType": "Block", "src": "2613:145:0", "statements": [{"condition": {"commonType": {"typeIdentifier": "t_address", "typeString": "address"}, "id": 111, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 106, "name": "new<PERSON>wner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 101, "src": "2627:8:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"arguments": [{"hexValue": "30", "id": 109, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "2647:1:0", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}], "id": 108, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "2639:7:0", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 107, "name": "address", "nodeType": "ElementaryTypeName", "src": "2639:7:0", "typeDescriptions": {}}}, "id": 110, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2639:10:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "src": "2627:22:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 120, "nodeType": "IfStatement", "src": "2623:91:0", "trueBody": {"id": 119, "nodeType": "Block", "src": "2651:63:0", "statements": [{"errorCall": {"arguments": [{"arguments": [{"hexValue": "30", "id": 115, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "2700:1:0", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}], "id": 114, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "2692:7:0", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 113, "name": "address", "nodeType": "ElementaryTypeName", "src": "2692:7:0", "typeDescriptions": {}}}, "id": 116, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2692:10:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 112, "name": "OwnableInvalidOwner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 18, "src": "2672:19:0", "typeDescriptions": {"typeIdentifier": "t_function_error_pure$_t_address_$returns$__$", "typeString": "function (address) pure"}}, "id": 117, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2672:31:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 118, "nodeType": "RevertStatement", "src": "2665:38:0"}]}}, {"expression": {"arguments": [{"id": 122, "name": "new<PERSON>wner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 101, "src": "2742:8:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 121, "name": "_transferOwnership", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 146, "src": "2723:18:0", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_address_$returns$__$", "typeString": "function (address)"}}, "id": 123, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2723:28:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 124, "nodeType": "ExpressionStatement", "src": "2723:28:0"}]}, "documentation": {"id": 99, "nodeType": "StructuredDocumentation", "src": "2400:138:0", "text": " @dev Transfers ownership of the contract to a new account (`newOwner`).\n Can only be called by the current owner."}, "functionSelector": "f2fde38b", "id": 126, "implemented": true, "kind": "function", "modifiers": [{"id": 104, "kind": "modifierInvocation", "modifierName": {"id": 103, "name": "only<PERSON><PERSON>er", "nameLocations": ["2603:9:0"], "nodeType": "IdentifierPath", "referencedDeclaration": 58, "src": "2603:9:0"}, "nodeType": "ModifierInvocation", "src": "2603:9:0"}], "name": "transferOwnership", "nameLocation": "2552:17:0", "nodeType": "FunctionDefinition", "parameters": {"id": 102, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 101, "mutability": "mutable", "name": "new<PERSON>wner", "nameLocation": "2578:8:0", "nodeType": "VariableDeclaration", "scope": 126, "src": "2570:16:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 100, "name": "address", "nodeType": "ElementaryTypeName", "src": "2570:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "2569:18:0"}, "returnParameters": {"id": 105, "nodeType": "ParameterList", "parameters": [], "src": "2613:0:0"}, "scope": 147, "src": "2543:215:0", "stateMutability": "nonpayable", "virtual": true, "visibility": "public"}, {"body": {"id": 145, "nodeType": "Block", "src": "2975:124:0", "statements": [{"assignments": [133], "declarations": [{"constant": false, "id": 133, "mutability": "mutable", "name": "old<PERSON>wner", "nameLocation": "2993:8:0", "nodeType": "VariableDeclaration", "scope": 145, "src": "2985:16:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 132, "name": "address", "nodeType": "ElementaryTypeName", "src": "2985:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "id": 135, "initialValue": {"id": 134, "name": "_owner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 8, "src": "3004:6:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "VariableDeclarationStatement", "src": "2985:25:0"}, {"expression": {"id": 138, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 136, "name": "_owner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 8, "src": "3020:6:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 137, "name": "new<PERSON>wner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 129, "src": "3029:8:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "src": "3020:17:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "id": 139, "nodeType": "ExpressionStatement", "src": "3020:17:0"}, {"eventCall": {"arguments": [{"id": 141, "name": "old<PERSON>wner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 133, "src": "3073:8:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 142, "name": "new<PERSON>wner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 129, "src": "3083:8:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_address", "typeString": "address"}], "id": 140, "name": "OwnershipTransferred", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 24, "src": "3052:20:0", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_address_$_t_address_$returns$__$", "typeString": "function (address,address)"}}, "id": 143, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3052:40:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 144, "nodeType": "EmitStatement", "src": "3047:45:0"}]}, "documentation": {"id": 127, "nodeType": "StructuredDocumentation", "src": "2764:143:0", "text": " @dev Transfers ownership of the contract to a new account (`newOwner`).\n Internal function without access restriction."}, "id": 146, "implemented": true, "kind": "function", "modifiers": [], "name": "_transferOwnership", "nameLocation": "2921:18:0", "nodeType": "FunctionDefinition", "parameters": {"id": 130, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 129, "mutability": "mutable", "name": "new<PERSON>wner", "nameLocation": "2948:8:0", "nodeType": "VariableDeclaration", "scope": 146, "src": "2940:16:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 128, "name": "address", "nodeType": "ElementaryTypeName", "src": "2940:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "2939:18:0"}, "returnParameters": {"id": 131, "nodeType": "ParameterList", "parameters": [], "src": "2975:0:0"}, "scope": 147, "src": "2912:187:0", "stateMutability": "nonpayable", "virtual": true, "visibility": "internal"}], "scope": 148, "src": "663:2438:0", "usedErrors": [13, 18], "usedEvents": [24]}], "src": "102:3000:0"}, "id": 0}, "@openzeppelin/contracts/token/ERC20/IERC20.sol": {"ast": {"absolutePath": "@openzeppelin/contracts/token/ERC20/IERC20.sol", "exportedSymbols": {"IERC20": [225]}, "id": 226, "license": "MIT", "nodeType": "SourceUnit", "nodes": [{"id": 149, "literals": ["solidity", ">=", "0.4", ".16"], "nodeType": "PragmaDirective", "src": "106:25:1"}, {"abstract": false, "baseContracts": [], "canonicalName": "IERC20", "contractDependencies": [], "contractKind": "interface", "documentation": {"id": 150, "nodeType": "StructuredDocumentation", "src": "133:71:1", "text": " @dev Interface of the ERC-20 standard as defined in the ERC."}, "fullyImplemented": false, "id": 225, "linearizedBaseContracts": [225], "name": "IERC20", "nameLocation": "215:6:1", "nodeType": "ContractDefinition", "nodes": [{"anonymous": false, "documentation": {"id": 151, "nodeType": "StructuredDocumentation", "src": "228:158:1", "text": " @dev Emitted when `value` tokens are moved from one account (`from`) to\n another (`to`).\n Note that `value` may be zero."}, "eventSelector": "ddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef", "id": 159, "name": "Transfer", "nameLocation": "397:8:1", "nodeType": "EventDefinition", "parameters": {"id": 158, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 153, "indexed": true, "mutability": "mutable", "name": "from", "nameLocation": "422:4:1", "nodeType": "VariableDeclaration", "scope": 159, "src": "406:20:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 152, "name": "address", "nodeType": "ElementaryTypeName", "src": "406:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 155, "indexed": true, "mutability": "mutable", "name": "to", "nameLocation": "444:2:1", "nodeType": "VariableDeclaration", "scope": 159, "src": "428:18:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 154, "name": "address", "nodeType": "ElementaryTypeName", "src": "428:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 157, "indexed": false, "mutability": "mutable", "name": "value", "nameLocation": "456:5:1", "nodeType": "VariableDeclaration", "scope": 159, "src": "448:13:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 156, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "448:7:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "405:57:1"}, "src": "391:72:1"}, {"anonymous": false, "documentation": {"id": 160, "nodeType": "StructuredDocumentation", "src": "469:148:1", "text": " @dev Emitted when the allowance of a `spender` for an `owner` is set by\n a call to {approve}. `value` is the new allowance."}, "eventSelector": "8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b925", "id": 168, "name": "Approval", "nameLocation": "628:8:1", "nodeType": "EventDefinition", "parameters": {"id": 167, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 162, "indexed": true, "mutability": "mutable", "name": "owner", "nameLocation": "653:5:1", "nodeType": "VariableDeclaration", "scope": 168, "src": "637:21:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 161, "name": "address", "nodeType": "ElementaryTypeName", "src": "637:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 164, "indexed": true, "mutability": "mutable", "name": "spender", "nameLocation": "676:7:1", "nodeType": "VariableDeclaration", "scope": 168, "src": "660:23:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 163, "name": "address", "nodeType": "ElementaryTypeName", "src": "660:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 166, "indexed": false, "mutability": "mutable", "name": "value", "nameLocation": "693:5:1", "nodeType": "VariableDeclaration", "scope": 168, "src": "685:13:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 165, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "685:7:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "636:63:1"}, "src": "622:78:1"}, {"documentation": {"id": 169, "nodeType": "StructuredDocumentation", "src": "706:65:1", "text": " @dev Returns the value of tokens in existence."}, "functionSelector": "18160ddd", "id": 174, "implemented": false, "kind": "function", "modifiers": [], "name": "totalSupply", "nameLocation": "785:11:1", "nodeType": "FunctionDefinition", "parameters": {"id": 170, "nodeType": "ParameterList", "parameters": [], "src": "796:2:1"}, "returnParameters": {"id": 173, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 172, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 174, "src": "822:7:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 171, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "822:7:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "821:9:1"}, "scope": 225, "src": "776:55:1", "stateMutability": "view", "virtual": false, "visibility": "external"}, {"documentation": {"id": 175, "nodeType": "StructuredDocumentation", "src": "837:71:1", "text": " @dev Returns the value of tokens owned by `account`."}, "functionSelector": "70a08231", "id": 182, "implemented": false, "kind": "function", "modifiers": [], "name": "balanceOf", "nameLocation": "922:9:1", "nodeType": "FunctionDefinition", "parameters": {"id": 178, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 177, "mutability": "mutable", "name": "account", "nameLocation": "940:7:1", "nodeType": "VariableDeclaration", "scope": 182, "src": "932:15:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 176, "name": "address", "nodeType": "ElementaryTypeName", "src": "932:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "931:17:1"}, "returnParameters": {"id": 181, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 180, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 182, "src": "972:7:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 179, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "972:7:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "971:9:1"}, "scope": 225, "src": "913:68:1", "stateMutability": "view", "virtual": false, "visibility": "external"}, {"documentation": {"id": 183, "nodeType": "StructuredDocumentation", "src": "987:213:1", "text": " @dev Moves a `value` amount of tokens from the caller's account to `to`.\n Returns a boolean value indicating whether the operation succeeded.\n Emits a {Transfer} event."}, "functionSelector": "a9059cbb", "id": 192, "implemented": false, "kind": "function", "modifiers": [], "name": "transfer", "nameLocation": "1214:8:1", "nodeType": "FunctionDefinition", "parameters": {"id": 188, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 185, "mutability": "mutable", "name": "to", "nameLocation": "1231:2:1", "nodeType": "VariableDeclaration", "scope": 192, "src": "1223:10:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 184, "name": "address", "nodeType": "ElementaryTypeName", "src": "1223:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 187, "mutability": "mutable", "name": "value", "nameLocation": "1243:5:1", "nodeType": "VariableDeclaration", "scope": 192, "src": "1235:13:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 186, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1235:7:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "1222:27:1"}, "returnParameters": {"id": 191, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 190, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 192, "src": "1268:4:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 189, "name": "bool", "nodeType": "ElementaryTypeName", "src": "1268:4:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "1267:6:1"}, "scope": 225, "src": "1205:69:1", "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}, {"documentation": {"id": 193, "nodeType": "StructuredDocumentation", "src": "1280:264:1", "text": " @dev Returns the remaining number of tokens that `spender` will be\n allowed to spend on behalf of `owner` through {transferFrom}. This is\n zero by default.\n This value changes when {approve} or {transferFrom} are called."}, "functionSelector": "dd62ed3e", "id": 202, "implemented": false, "kind": "function", "modifiers": [], "name": "allowance", "nameLocation": "1558:9:1", "nodeType": "FunctionDefinition", "parameters": {"id": 198, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 195, "mutability": "mutable", "name": "owner", "nameLocation": "1576:5:1", "nodeType": "VariableDeclaration", "scope": 202, "src": "1568:13:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 194, "name": "address", "nodeType": "ElementaryTypeName", "src": "1568:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 197, "mutability": "mutable", "name": "spender", "nameLocation": "1591:7:1", "nodeType": "VariableDeclaration", "scope": 202, "src": "1583:15:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 196, "name": "address", "nodeType": "ElementaryTypeName", "src": "1583:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "1567:32:1"}, "returnParameters": {"id": 201, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 200, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 202, "src": "1623:7:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 199, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1623:7:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "1622:9:1"}, "scope": 225, "src": "1549:83:1", "stateMutability": "view", "virtual": false, "visibility": "external"}, {"documentation": {"id": 203, "nodeType": "StructuredDocumentation", "src": "1638:667:1", "text": " @dev Sets a `value` amount of tokens as the allowance of `spender` over the\n caller's tokens.\n Returns a boolean value indicating whether the operation succeeded.\n IMPORTANT: Beware that changing an allowance with this method brings the risk\n that someone may use both the old and the new allowance by unfortunate\n transaction ordering. One possible solution to mitigate this race\n condition is to first reduce the spender's allowance to 0 and set the\n desired value afterwards:\n https://github.com/ethereum/EIPs/issues/20#issuecomment-*********\n Emits an {Approval} event."}, "functionSelector": "095ea7b3", "id": 212, "implemented": false, "kind": "function", "modifiers": [], "name": "approve", "nameLocation": "2319:7:1", "nodeType": "FunctionDefinition", "parameters": {"id": 208, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 205, "mutability": "mutable", "name": "spender", "nameLocation": "2335:7:1", "nodeType": "VariableDeclaration", "scope": 212, "src": "2327:15:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 204, "name": "address", "nodeType": "ElementaryTypeName", "src": "2327:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 207, "mutability": "mutable", "name": "value", "nameLocation": "2352:5:1", "nodeType": "VariableDeclaration", "scope": 212, "src": "2344:13:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 206, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2344:7:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "2326:32:1"}, "returnParameters": {"id": 211, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 210, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 212, "src": "2377:4:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 209, "name": "bool", "nodeType": "ElementaryTypeName", "src": "2377:4:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "2376:6:1"}, "scope": 225, "src": "2310:73:1", "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}, {"documentation": {"id": 213, "nodeType": "StructuredDocumentation", "src": "2389:297:1", "text": " @dev Moves a `value` amount of tokens from `from` to `to` using the\n allowance mechanism. `value` is then deducted from the caller's\n allowance.\n Returns a boolean value indicating whether the operation succeeded.\n Emits a {Transfer} event."}, "functionSelector": "23b872dd", "id": 224, "implemented": false, "kind": "function", "modifiers": [], "name": "transferFrom", "nameLocation": "2700:12:1", "nodeType": "FunctionDefinition", "parameters": {"id": 220, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 215, "mutability": "mutable", "name": "from", "nameLocation": "2721:4:1", "nodeType": "VariableDeclaration", "scope": 224, "src": "2713:12:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 214, "name": "address", "nodeType": "ElementaryTypeName", "src": "2713:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 217, "mutability": "mutable", "name": "to", "nameLocation": "2735:2:1", "nodeType": "VariableDeclaration", "scope": 224, "src": "2727:10:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 216, "name": "address", "nodeType": "ElementaryTypeName", "src": "2727:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 219, "mutability": "mutable", "name": "value", "nameLocation": "2747:5:1", "nodeType": "VariableDeclaration", "scope": 224, "src": "2739:13:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 218, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2739:7:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "2712:41:1"}, "returnParameters": {"id": 223, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 222, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 224, "src": "2772:4:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 221, "name": "bool", "nodeType": "ElementaryTypeName", "src": "2772:4:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "2771:6:1"}, "scope": 225, "src": "2691:87:1", "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}], "scope": 226, "src": "205:2575:1", "usedErrors": [], "usedEvents": [159, 168]}], "src": "106:2675:1"}, "id": 1}, "@openzeppelin/contracts/utils/Context.sol": {"ast": {"absolutePath": "@openzeppelin/contracts/utils/Context.sol", "exportedSymbols": {"Context": [255]}, "id": 256, "license": "MIT", "nodeType": "SourceUnit", "nodes": [{"id": 227, "literals": ["solidity", "^", "0.8", ".20"], "nodeType": "PragmaDirective", "src": "101:24:2"}, {"abstract": true, "baseContracts": [], "canonicalName": "Context", "contractDependencies": [], "contractKind": "contract", "documentation": {"id": 228, "nodeType": "StructuredDocumentation", "src": "127:496:2", "text": " @dev Provides information about the current execution context, including the\n sender of the transaction and its data. While these are generally available\n via msg.sender and msg.data, they should not be accessed in such a direct\n manner, since when dealing with meta-transactions the account sending and\n paying for execution may not be the actual sender (as far as an application\n is concerned).\n This contract is only required for intermediate, library-like contracts."}, "fullyImplemented": true, "id": 255, "linearizedBaseContracts": [255], "name": "Context", "nameLocation": "642:7:2", "nodeType": "ContractDefinition", "nodes": [{"body": {"id": 236, "nodeType": "Block", "src": "718:34:2", "statements": [{"expression": {"expression": {"id": 233, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "735:3:2", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 234, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "739:6:2", "memberName": "sender", "nodeType": "MemberAccess", "src": "735:10:2", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "functionReturnParameters": 232, "id": 235, "nodeType": "Return", "src": "728:17:2"}]}, "id": 237, "implemented": true, "kind": "function", "modifiers": [], "name": "_msgSender", "nameLocation": "665:10:2", "nodeType": "FunctionDefinition", "parameters": {"id": 229, "nodeType": "ParameterList", "parameters": [], "src": "675:2:2"}, "returnParameters": {"id": 232, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 231, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 237, "src": "709:7:2", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 230, "name": "address", "nodeType": "ElementaryTypeName", "src": "709:7:2", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "708:9:2"}, "scope": 255, "src": "656:96:2", "stateMutability": "view", "virtual": true, "visibility": "internal"}, {"body": {"id": 245, "nodeType": "Block", "src": "825:32:2", "statements": [{"expression": {"expression": {"id": 242, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "842:3:2", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 243, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "846:4:2", "memberName": "data", "nodeType": "MemberAccess", "src": "842:8:2", "typeDescriptions": {"typeIdentifier": "t_bytes_calldata_ptr", "typeString": "bytes calldata"}}, "functionReturnParameters": 241, "id": 244, "nodeType": "Return", "src": "835:15:2"}]}, "id": 246, "implemented": true, "kind": "function", "modifiers": [], "name": "_msgData", "nameLocation": "767:8:2", "nodeType": "FunctionDefinition", "parameters": {"id": 238, "nodeType": "ParameterList", "parameters": [], "src": "775:2:2"}, "returnParameters": {"id": 241, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 240, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 246, "src": "809:14:2", "stateVariable": false, "storageLocation": "calldata", "typeDescriptions": {"typeIdentifier": "t_bytes_calldata_ptr", "typeString": "bytes"}, "typeName": {"id": 239, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "809:5:2", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "visibility": "internal"}], "src": "808:16:2"}, "scope": 255, "src": "758:99:2", "stateMutability": "view", "virtual": true, "visibility": "internal"}, {"body": {"id": 253, "nodeType": "Block", "src": "935:25:2", "statements": [{"expression": {"hexValue": "30", "id": 251, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "952:1:2", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "functionReturnParameters": 250, "id": 252, "nodeType": "Return", "src": "945:8:2"}]}, "id": 254, "implemented": true, "kind": "function", "modifiers": [], "name": "_contextSuffixLength", "nameLocation": "872:20:2", "nodeType": "FunctionDefinition", "parameters": {"id": 247, "nodeType": "ParameterList", "parameters": [], "src": "892:2:2"}, "returnParameters": {"id": 250, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 249, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 254, "src": "926:7:2", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 248, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "926:7:2", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "925:9:2"}, "scope": 255, "src": "863:97:2", "stateMutability": "view", "virtual": true, "visibility": "internal"}], "scope": 256, "src": "624:338:2", "usedErrors": [], "usedEvents": []}], "src": "101:862:2"}, "id": 2}, "@openzeppelin/contracts/utils/ReentrancyGuard.sol": {"ast": {"absolutePath": "@openzeppelin/contracts/utils/ReentrancyGuard.sol", "exportedSymbols": {"ReentrancyGuard": [324]}, "id": 325, "license": "MIT", "nodeType": "SourceUnit", "nodes": [{"id": 257, "literals": ["solidity", "^", "0.8", ".20"], "nodeType": "PragmaDirective", "src": "109:24:3"}, {"abstract": true, "baseContracts": [], "canonicalName": "Reentrancy<PERSON><PERSON>", "contractDependencies": [], "contractKind": "contract", "documentation": {"id": 258, "nodeType": "StructuredDocumentation", "src": "135:894:3", "text": " @dev Contract module that helps prevent reentrant calls to a function.\n Inheriting from `ReentrancyGuard` will make the {nonReentrant} modifier\n available, which can be applied to functions to make sure there are no nested\n (reentrant) calls to them.\n Note that because there is a single `nonReentrant` guard, functions marked as\n `nonReentrant` may not call one another. This can be worked around by making\n those functions `private`, and then adding `external` `nonReentrant` entry\n points to them.\n TIP: If EIP-1153 (transient storage) is available on the chain you're deploying at,\n consider using {ReentrancyGuardTransient} instead.\n TIP: If you would like to learn more about reentrancy and alternative ways\n to protect against it, check out our blog post\n https://blog.openzeppelin.com/reentrancy-after-istanbul/[Reentrancy After Istanbul]."}, "fullyImplemented": true, "id": 324, "linearizedBaseContracts": [324], "name": "Reentrancy<PERSON><PERSON>", "nameLocation": "1048:15:3", "nodeType": "ContractDefinition", "nodes": [{"constant": true, "id": 261, "mutability": "constant", "name": "NOT_ENTERED", "nameLocation": "1843:11:3", "nodeType": "VariableDeclaration", "scope": 324, "src": "1818:40:3", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 259, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1818:7:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": {"hexValue": "31", "id": 260, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "1857:1:3", "typeDescriptions": {"typeIdentifier": "t_rational_1_by_1", "typeString": "int_const 1"}, "value": "1"}, "visibility": "private"}, {"constant": true, "id": 264, "mutability": "constant", "name": "ENTERED", "nameLocation": "1889:7:3", "nodeType": "VariableDeclaration", "scope": 324, "src": "1864:36:3", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 262, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1864:7:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": {"hexValue": "32", "id": 263, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "1899:1:3", "typeDescriptions": {"typeIdentifier": "t_rational_2_by_1", "typeString": "int_const 2"}, "value": "2"}, "visibility": "private"}, {"constant": false, "id": 266, "mutability": "mutable", "name": "_status", "nameLocation": "1923:7:3", "nodeType": "VariableDeclaration", "scope": 324, "src": "1907:23:3", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 265, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1907:7:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "private"}, {"documentation": {"id": 267, "nodeType": "StructuredDocumentation", "src": "1937:52:3", "text": " @dev Unauthorized reentrant call."}, "errorSelector": "3ee5aeb5", "id": 269, "name": "ReentrancyGuardReentrantCall", "nameLocation": "2000:28:3", "nodeType": "ErrorDefinition", "parameters": {"id": 268, "nodeType": "ParameterList", "parameters": [], "src": "2028:2:3"}, "src": "1994:37:3"}, {"body": {"id": 276, "nodeType": "Block", "src": "2051:38:3", "statements": [{"expression": {"id": 274, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 272, "name": "_status", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 266, "src": "2061:7:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 273, "name": "NOT_ENTERED", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 261, "src": "2071:11:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "2061:21:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 275, "nodeType": "ExpressionStatement", "src": "2061:21:3"}]}, "id": 277, "implemented": true, "kind": "constructor", "modifiers": [], "name": "", "nameLocation": "-1:-1:-1", "nodeType": "FunctionDefinition", "parameters": {"id": 270, "nodeType": "ParameterList", "parameters": [], "src": "2048:2:3"}, "returnParameters": {"id": 271, "nodeType": "ParameterList", "parameters": [], "src": "2051:0:3"}, "scope": 324, "src": "2037:52:3", "stateMutability": "nonpayable", "virtual": false, "visibility": "internal"}, {"body": {"id": 287, "nodeType": "Block", "src": "2490:79:3", "statements": [{"expression": {"arguments": [], "expression": {"argumentTypes": [], "id": 280, "name": "_nonReentrantBefore", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 304, "src": "2500:19:3", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$__$returns$__$", "typeString": "function ()"}}, "id": 281, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2500:21:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 282, "nodeType": "ExpressionStatement", "src": "2500:21:3"}, {"id": 283, "nodeType": "PlaceholderStatement", "src": "2531:1:3"}, {"expression": {"arguments": [], "expression": {"argumentTypes": [], "id": 284, "name": "_nonReentrantAfter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 312, "src": "2542:18:3", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$__$returns$__$", "typeString": "function ()"}}, "id": 285, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2542:20:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 286, "nodeType": "ExpressionStatement", "src": "2542:20:3"}]}, "documentation": {"id": 278, "nodeType": "StructuredDocumentation", "src": "2095:366:3", "text": " @dev Prevents a contract from calling itself, directly or indirectly.\n Calling a `nonReentrant` function from another `nonReentrant`\n function is not supported. It is possible to prevent this from happening\n by making the `nonReentrant` function external, and making it call a\n `private` function that does the actual work."}, "id": 288, "name": "nonReentrant", "nameLocation": "2475:12:3", "nodeType": "ModifierDefinition", "parameters": {"id": 279, "nodeType": "ParameterList", "parameters": [], "src": "2487:2:3"}, "src": "2466:103:3", "virtual": false, "visibility": "internal"}, {"body": {"id": 303, "nodeType": "Block", "src": "2614:268:3", "statements": [{"condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 293, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 291, "name": "_status", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 266, "src": "2702:7:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"id": 292, "name": "ENTERED", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 264, "src": "2713:7:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "2702:18:3", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 298, "nodeType": "IfStatement", "src": "2698:86:3", "trueBody": {"id": 297, "nodeType": "Block", "src": "2722:62:3", "statements": [{"errorCall": {"arguments": [], "expression": {"argumentTypes": [], "id": 294, "name": "ReentrancyGuardReentrantCall", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 269, "src": "2743:28:3", "typeDescriptions": {"typeIdentifier": "t_function_error_pure$__$returns$__$", "typeString": "function () pure"}}, "id": 295, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2743:30:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 296, "nodeType": "RevertStatement", "src": "2736:37:3"}]}}, {"expression": {"id": 301, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 299, "name": "_status", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 266, "src": "2858:7:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 300, "name": "ENTERED", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 264, "src": "2868:7:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "2858:17:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 302, "nodeType": "ExpressionStatement", "src": "2858:17:3"}]}, "id": 304, "implemented": true, "kind": "function", "modifiers": [], "name": "_nonReentrantBefore", "nameLocation": "2584:19:3", "nodeType": "FunctionDefinition", "parameters": {"id": 289, "nodeType": "ParameterList", "parameters": [], "src": "2603:2:3"}, "returnParameters": {"id": 290, "nodeType": "ParameterList", "parameters": [], "src": "2614:0:3"}, "scope": 324, "src": "2575:307:3", "stateMutability": "nonpayable", "virtual": false, "visibility": "private"}, {"body": {"id": 311, "nodeType": "Block", "src": "2926:170:3", "statements": [{"expression": {"id": 309, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 307, "name": "_status", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 266, "src": "3068:7:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 308, "name": "NOT_ENTERED", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 261, "src": "3078:11:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "3068:21:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 310, "nodeType": "ExpressionStatement", "src": "3068:21:3"}]}, "id": 312, "implemented": true, "kind": "function", "modifiers": [], "name": "_nonReentrantAfter", "nameLocation": "2897:18:3", "nodeType": "FunctionDefinition", "parameters": {"id": 305, "nodeType": "ParameterList", "parameters": [], "src": "2915:2:3"}, "returnParameters": {"id": 306, "nodeType": "ParameterList", "parameters": [], "src": "2926:0:3"}, "scope": 324, "src": "2888:208:3", "stateMutability": "nonpayable", "virtual": false, "visibility": "private"}, {"body": {"id": 322, "nodeType": "Block", "src": "3339:42:3", "statements": [{"expression": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 320, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 318, "name": "_status", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 266, "src": "3356:7:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"id": 319, "name": "ENTERED", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 264, "src": "3367:7:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "3356:18:3", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "functionReturnParameters": 317, "id": 321, "nodeType": "Return", "src": "3349:25:3"}]}, "documentation": {"id": 313, "nodeType": "StructuredDocumentation", "src": "3102:168:3", "text": " @dev Returns true if the reentrancy guard is currently set to \"entered\", which indicates there is a\n `nonReentrant` function in the call stack."}, "id": 323, "implemented": true, "kind": "function", "modifiers": [], "name": "_reentrancyGuardEntered", "nameLocation": "3284:23:3", "nodeType": "FunctionDefinition", "parameters": {"id": 314, "nodeType": "ParameterList", "parameters": [], "src": "3307:2:3"}, "returnParameters": {"id": 317, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 316, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 323, "src": "3333:4:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 315, "name": "bool", "nodeType": "ElementaryTypeName", "src": "3333:4:3", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "3332:6:3"}, "scope": 324, "src": "3275:106:3", "stateMutability": "view", "virtual": false, "visibility": "internal"}], "scope": 325, "src": "1030:2353:3", "usedErrors": [269], "usedEvents": []}], "src": "109:3275:3"}, "id": 3}, "contracts/dao/SimpleDAO.sol": {"ast": {"absolutePath": "contracts/dao/SimpleDAO.sol", "exportedSymbols": {"Context": [255], "IERC20": [225], "Ownable": [147], "ReentrancyGuard": [324], "SimpleDAO": [1039]}, "id": 1040, "license": "MIT", "nodeType": "SourceUnit", "nodes": [{"id": 326, "literals": ["solidity", "^", "0.8", ".24"], "nodeType": "PragmaDirective", "src": "32:24:4"}, {"absolutePath": "@openzeppelin/contracts/token/ERC20/IERC20.sol", "file": "@openzeppelin/contracts/token/ERC20/IERC20.sol", "id": 327, "nameLocation": "-1:-1:-1", "nodeType": "ImportDirective", "scope": 1040, "sourceUnit": 226, "src": "58:56:4", "symbolAliases": [], "unitAlias": ""}, {"absolutePath": "@openzeppelin/contracts/access/Ownable.sol", "file": "@openzeppelin/contracts/access/Ownable.sol", "id": 328, "nameLocation": "-1:-1:-1", "nodeType": "ImportDirective", "scope": 1040, "sourceUnit": 148, "src": "115:52:4", "symbolAliases": [], "unitAlias": ""}, {"absolutePath": "@openzeppelin/contracts/utils/ReentrancyGuard.sol", "file": "@openzeppelin/contracts/utils/ReentrancyGuard.sol", "id": 329, "nameLocation": "-1:-1:-1", "nodeType": "ImportDirective", "scope": 1040, "sourceUnit": 325, "src": "168:59:4", "symbolAliases": [], "unitAlias": ""}, {"abstract": false, "baseContracts": [{"baseName": {"id": 331, "name": "Ownable", "nameLocations": ["433:7:4"], "nodeType": "IdentifierPath", "referencedDeclaration": 147, "src": "433:7:4"}, "id": 332, "nodeType": "InheritanceSpecifier", "src": "433:7:4"}, {"baseName": {"id": 333, "name": "Reentrancy<PERSON><PERSON>", "nameLocations": ["442:15:4"], "nodeType": "IdentifierPath", "referencedDeclaration": 324, "src": "442:15:4"}, "id": 334, "nodeType": "InheritanceSpecifier", "src": "442:15:4"}], "canonicalName": "SimpleDAO", "contractDependencies": [], "contractKind": "contract", "documentation": {"id": 330, "nodeType": "StructuredDocumentation", "src": "229:181:4", "text": " @title SimpleDAO\n @dev 简化的 DAO 治理合约\n \n 功能特性：\n - 基于代币的投票权重\n - 提案创建和执行\n - 投票机制\n - 资金管理"}, "fullyImplemented": true, "id": 1039, "linearizedBaseContracts": [1039, 324, 147, 255], "name": "SimpleDAO", "nameLocation": "420:9:4", "nodeType": "ContractDefinition", "nodes": [{"constant": false, "functionSelector": "f96dae0a", "id": 337, "mutability": "mutable", "name": "governanceToken", "nameLocation": "483:15:4", "nodeType": "VariableDeclaration", "scope": 1039, "src": "469:29:4", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_contract$_IERC20_$225", "typeString": "contract IERC20"}, "typeName": {"id": 336, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 335, "name": "IERC20", "nameLocations": ["469:6:4"], "nodeType": "IdentifierPath", "referencedDeclaration": 225, "src": "469:6:4"}, "referencedDeclaration": 225, "src": "469:6:4", "typeDescriptions": {"typeIdentifier": "t_contract$_IERC20_$225", "typeString": "contract IERC20"}}, "visibility": "public"}, {"canonicalName": "SimpleDAO.ProposalState", "id": 343, "members": [{"id": 338, "name": "PENDING", "nameLocation": "550:7:4", "nodeType": "EnumValue", "src": "550:7:4"}, {"id": 339, "name": "ACTIVE", "nameLocation": "559:6:4", "nodeType": "EnumValue", "src": "559:6:4"}, {"id": 340, "name": "SUCCEEDED", "nameLocation": "567:9:4", "nodeType": "EnumValue", "src": "567:9:4"}, {"id": 341, "name": "DEFEATED", "nameLocation": "578:8:4", "nodeType": "EnumValue", "src": "578:8:4"}, {"id": 342, "name": "EXECUTED", "nameLocation": "588:8:4", "nodeType": "EnumValue", "src": "588:8:4"}], "name": "ProposalState", "nameLocation": "534:13:4", "nodeType": "EnumDefinition", "src": "529:69:4"}, {"canonicalName": "SimpleDAO.ProposalType", "id": 347, "members": [{"id": 344, "name": "GENERAL", "nameLocation": "648:7:4", "nodeType": "EnumValue", "src": "648:7:4"}, {"id": 345, "name": "FUNDING", "nameLocation": "657:7:4", "nodeType": "EnumValue", "src": "657:7:4"}, {"id": 346, "name": "PARAMETER_CHANGE", "nameLocation": "666:16:4", "nodeType": "EnumValue", "src": "666:16:4"}], "name": "ProposalType", "nameLocation": "633:12:4", "nodeType": "EnumDefinition", "src": "628:56:4"}, {"canonicalName": "SimpleDAO.Proposal", "id": 374, "members": [{"constant": false, "id": 349, "mutability": "mutable", "name": "id", "nameLocation": "748:2:4", "nodeType": "VariableDeclaration", "scope": 374, "src": "740:10:4", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 348, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "740:7:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 351, "mutability": "mutable", "name": "proposer", "nameLocation": "768:8:4", "nodeType": "VariableDeclaration", "scope": 374, "src": "760:16:4", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 350, "name": "address", "nodeType": "ElementaryTypeName", "src": "760:7:4", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 353, "mutability": "mutable", "name": "description", "nameLocation": "793:11:4", "nodeType": "VariableDeclaration", "scope": 374, "src": "786:18:4", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}, "typeName": {"id": 352, "name": "string", "nodeType": "ElementaryTypeName", "src": "786:6:4", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 356, "mutability": "mutable", "name": "proposalType", "nameLocation": "827:12:4", "nodeType": "VariableDeclaration", "scope": 374, "src": "814:25:4", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_enum$_ProposalType_$347", "typeString": "enum SimpleDAO.ProposalType"}, "typeName": {"id": 355, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 354, "name": "ProposalType", "nameLocations": ["814:12:4"], "nodeType": "IdentifierPath", "referencedDeclaration": 347, "src": "814:12:4"}, "referencedDeclaration": 347, "src": "814:12:4", "typeDescriptions": {"typeIdentifier": "t_enum$_ProposalType_$347", "typeString": "enum SimpleDAO.ProposalType"}}, "visibility": "internal"}, {"constant": false, "id": 358, "mutability": "mutable", "name": "requestedAmount", "nameLocation": "857:15:4", "nodeType": "VariableDeclaration", "scope": 374, "src": "849:23:4", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 357, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "849:7:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 360, "mutability": "mutable", "name": "beneficiary", "nameLocation": "890:11:4", "nodeType": "VariableDeclaration", "scope": 374, "src": "882:19:4", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 359, "name": "address", "nodeType": "ElementaryTypeName", "src": "882:7:4", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 362, "mutability": "mutable", "name": "forVotes", "nameLocation": "919:8:4", "nodeType": "VariableDeclaration", "scope": 374, "src": "911:16:4", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 361, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "911:7:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 364, "mutability": "mutable", "name": "againstVotes", "nameLocation": "945:12:4", "nodeType": "VariableDeclaration", "scope": 374, "src": "937:20:4", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 363, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "937:7:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 366, "mutability": "mutable", "name": "startTime", "nameLocation": "975:9:4", "nodeType": "VariableDeclaration", "scope": 374, "src": "967:17:4", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 365, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "967:7:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 368, "mutability": "mutable", "name": "endTime", "nameLocation": "1002:7:4", "nodeType": "VariableDeclaration", "scope": 374, "src": "994:15:4", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 367, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "994:7:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 371, "mutability": "mutable", "name": "state", "nameLocation": "1033:5:4", "nodeType": "VariableDeclaration", "scope": 374, "src": "1019:19:4", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_enum$_ProposalState_$343", "typeString": "enum SimpleDAO.ProposalState"}, "typeName": {"id": 370, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 369, "name": "ProposalState", "nameLocations": ["1019:13:4"], "nodeType": "IdentifierPath", "referencedDeclaration": 343, "src": "1019:13:4"}, "referencedDeclaration": 343, "src": "1019:13:4", "typeDescriptions": {"typeIdentifier": "t_enum$_ProposalState_$343", "typeString": "enum SimpleDAO.ProposalState"}}, "visibility": "internal"}, {"constant": false, "id": 373, "mutability": "mutable", "name": "executed", "nameLocation": "1053:8:4", "nodeType": "VariableDeclaration", "scope": 374, "src": "1048:13:4", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 372, "name": "bool", "nodeType": "ElementaryTypeName", "src": "1048:4:4", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "name": "Proposal", "nameLocation": "721:8:4", "nodeType": "StructDefinition", "scope": 1039, "src": "714:354:4", "visibility": "public"}, {"constant": false, "functionSelector": "013cf08b", "id": 379, "mutability": "mutable", "name": "proposals", "nameLocation": "1114:9:4", "nodeType": "VariableDeclaration", "scope": 1039, "src": "1078:45:4", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_uint256_$_t_struct$_Proposal_$374_storage_$", "typeString": "mapping(uint256 => struct SimpleDAO.Proposal)"}, "typeName": {"id": 378, "keyName": "", "keyNameLocation": "-1:-1:-1", "keyType": {"id": 375, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1086:7:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Mapping", "src": "1078:28:4", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_uint256_$_t_struct$_Proposal_$374_storage_$", "typeString": "mapping(uint256 => struct SimpleDAO.Proposal)"}, "valueName": "", "valueNameLocation": "-1:-1:-1", "valueType": {"id": 377, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 376, "name": "Proposal", "nameLocations": ["1097:8:4"], "nodeType": "IdentifierPath", "referencedDeclaration": 374, "src": "1097:8:4"}, "referencedDeclaration": 374, "src": "1097:8:4", "typeDescriptions": {"typeIdentifier": "t_struct$_Proposal_$374_storage_ptr", "typeString": "struct SimpleDAO.Proposal"}}}, "visibility": "public"}, {"constant": false, "functionSelector": "43859632", "id": 385, "mutability": "mutable", "name": "hasVoted", "nameLocation": "1181:8:4", "nodeType": "VariableDeclaration", "scope": 1039, "src": "1129:60:4", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_uint256_$_t_mapping$_t_address_$_t_bool_$_$", "typeString": "mapping(uint256 => mapping(address => bool))"}, "typeName": {"id": 384, "keyName": "", "keyNameLocation": "-1:-1:-1", "keyType": {"id": 380, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1137:7:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Mapping", "src": "1129:44:4", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_uint256_$_t_mapping$_t_address_$_t_bool_$_$", "typeString": "mapping(uint256 => mapping(address => bool))"}, "valueName": "", "valueNameLocation": "-1:-1:-1", "valueType": {"id": 383, "keyName": "", "keyNameLocation": "-1:-1:-1", "keyType": {"id": 381, "name": "address", "nodeType": "ElementaryTypeName", "src": "1156:7:4", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "Mapping", "src": "1148:24:4", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_bool_$", "typeString": "mapping(address => bool)"}, "valueName": "", "valueNameLocation": "-1:-1:-1", "valueType": {"id": 382, "name": "bool", "nodeType": "ElementaryTypeName", "src": "1167:4:4", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}}}, "visibility": "public"}, {"constant": false, "functionSelector": "fb468855", "id": 391, "mutability": "mutable", "name": "voteChoice", "nameLocation": "1247:10:4", "nodeType": "VariableDeclaration", "scope": 1039, "src": "1195:62:4", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_uint256_$_t_mapping$_t_address_$_t_bool_$_$", "typeString": "mapping(uint256 => mapping(address => bool))"}, "typeName": {"id": 390, "keyName": "", "keyNameLocation": "-1:-1:-1", "keyType": {"id": 386, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1203:7:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Mapping", "src": "1195:44:4", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_uint256_$_t_mapping$_t_address_$_t_bool_$_$", "typeString": "mapping(uint256 => mapping(address => bool))"}, "valueName": "", "valueNameLocation": "-1:-1:-1", "valueType": {"id": 389, "keyName": "", "keyNameLocation": "-1:-1:-1", "keyType": {"id": 387, "name": "address", "nodeType": "ElementaryTypeName", "src": "1222:7:4", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "Mapping", "src": "1214:24:4", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_bool_$", "typeString": "mapping(address => bool)"}, "valueName": "", "valueNameLocation": "-1:-1:-1", "valueType": {"id": 388, "name": "bool", "nodeType": "ElementaryTypeName", "src": "1233:4:4", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}}}, "visibility": "public"}, {"constant": false, "functionSelector": "da35c664", "id": 393, "mutability": "mutable", "name": "proposalCount", "nameLocation": "1278:13:4", "nodeType": "VariableDeclaration", "scope": 1039, "src": "1263:28:4", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 392, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1263:7:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "public"}, {"constant": false, "functionSelector": "02a251a3", "id": 396, "mutability": "mutable", "name": "votingPeriod", "nameLocation": "1337:12:4", "nodeType": "VariableDeclaration", "scope": 1039, "src": "1322:36:4", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 394, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1322:7:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": {"hexValue": "33", "id": 395, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "1352:6:4", "subdenomination": "days", "typeDescriptions": {"typeIdentifier": "t_rational_259200_by_1", "typeString": "int_const 259200"}, "value": "3"}, "visibility": "public"}, {"constant": false, "functionSelector": "b58131b0", "id": 403, "mutability": "mutable", "name": "proposalThreshold", "nameLocation": "1379:17:4", "nodeType": "VariableDeclaration", "scope": 1039, "src": "1364:48:4", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 397, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1364:7:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": {"commonType": {"typeIdentifier": "t_rational_1000000000000000000000_by_1", "typeString": "int_const 1000000000000000000000"}, "id": 402, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "leftExpression": {"hexValue": "31303030", "id": 398, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "1399:4:4", "typeDescriptions": {"typeIdentifier": "t_rational_1000_by_1", "typeString": "int_const 1000"}, "value": "1000"}, "nodeType": "BinaryOperation", "operator": "*", "rightExpression": {"commonType": {"typeIdentifier": "t_rational_1000000000000000000_by_1", "typeString": "int_const 1000000000000000000"}, "id": 401, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "leftExpression": {"hexValue": "3130", "id": 399, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "1406:2:4", "typeDescriptions": {"typeIdentifier": "t_rational_10_by_1", "typeString": "int_const 10"}, "value": "10"}, "nodeType": "BinaryOperation", "operator": "**", "rightExpression": {"hexValue": "3138", "id": 400, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "1410:2:4", "typeDescriptions": {"typeIdentifier": "t_rational_18_by_1", "typeString": "int_const 18"}, "value": "18"}, "src": "1406:6:4", "typeDescriptions": {"typeIdentifier": "t_rational_1000000000000000000_by_1", "typeString": "int_const 1000000000000000000"}}, "src": "1399:13:4", "typeDescriptions": {"typeIdentifier": "t_rational_1000000000000000000000_by_1", "typeString": "int_const 1000000000000000000000"}}, "visibility": "public"}, {"constant": false, "functionSelector": "4fa76ec9", "id": 406, "mutability": "mutable", "name": "quorumPercentage", "nameLocation": "1448:16:4", "nodeType": "VariableDeclaration", "scope": 1039, "src": "1433:35:4", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 404, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1433:7:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": {"hexValue": "34", "id": 405, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "1467:1:4", "typeDescriptions": {"typeIdentifier": "t_rational_4_by_1", "typeString": "int_const 4"}, "value": "4"}, "visibility": "public"}, {"constant": false, "functionSelector": "313dab20", "id": 408, "mutability": "mutable", "name": "treasuryBalance", "nameLocation": "1521:15:4", "nodeType": "VariableDeclaration", "scope": 1039, "src": "1506:30:4", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 407, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1506:7:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "public"}, {"constant": false, "functionSelector": "a93271af", "id": 412, "mutability": "mutable", "name": "memberContributions", "nameLocation": "1577:19:4", "nodeType": "VariableDeclaration", "scope": 1039, "src": "1542:54:4", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}, "typeName": {"id": 411, "keyName": "", "keyNameLocation": "-1:-1:-1", "keyType": {"id": 409, "name": "address", "nodeType": "ElementaryTypeName", "src": "1550:7:4", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "Mapping", "src": "1542:27:4", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}, "valueName": "", "valueNameLocation": "-1:-1:-1", "valueType": {"id": 410, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1561:7:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}}, "visibility": "public"}, {"anonymous": false, "eventSelector": "e0269f7953e70365e80e614e4efe7d4bede59b7f5ad80982ea4064c97ee6f4ef", "id": 423, "name": "ProposalCreated", "nameLocation": "1627:15:4", "nodeType": "EventDefinition", "parameters": {"id": 422, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 414, "indexed": true, "mutability": "mutable", "name": "proposalId", "nameLocation": "1668:10:4", "nodeType": "VariableDeclaration", "scope": 423, "src": "1652:26:4", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 413, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1652:7:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 416, "indexed": true, "mutability": "mutable", "name": "proposer", "nameLocation": "1704:8:4", "nodeType": "VariableDeclaration", "scope": 423, "src": "1688:24:4", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 415, "name": "address", "nodeType": "ElementaryTypeName", "src": "1688:7:4", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 418, "indexed": false, "mutability": "mutable", "name": "description", "nameLocation": "1729:11:4", "nodeType": "VariableDeclaration", "scope": 423, "src": "1722:18:4", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 417, "name": "string", "nodeType": "ElementaryTypeName", "src": "1722:6:4", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 421, "indexed": false, "mutability": "mutable", "name": "proposalType", "nameLocation": "1763:12:4", "nodeType": "VariableDeclaration", "scope": 423, "src": "1750:25:4", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_enum$_ProposalType_$347", "typeString": "enum SimpleDAO.ProposalType"}, "typeName": {"id": 420, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 419, "name": "ProposalType", "nameLocations": ["1750:12:4"], "nodeType": "IdentifierPath", "referencedDeclaration": 347, "src": "1750:12:4"}, "referencedDeclaration": 347, "src": "1750:12:4", "typeDescriptions": {"typeIdentifier": "t_enum$_ProposalType_$347", "typeString": "enum SimpleDAO.ProposalType"}}, "visibility": "internal"}], "src": "1642:139:4"}, "src": "1621:161:4"}, {"anonymous": false, "eventSelector": "cbdf6214089cba887ecbf35a0b6a734589959c9763342c756bb2a80ca2bc9f6e", "id": 433, "name": "VoteCast", "nameLocation": "1793:8:4", "nodeType": "EventDefinition", "parameters": {"id": 432, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 425, "indexed": true, "mutability": "mutable", "name": "proposalId", "nameLocation": "1827:10:4", "nodeType": "VariableDeclaration", "scope": 433, "src": "1811:26:4", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 424, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1811:7:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 427, "indexed": true, "mutability": "mutable", "name": "voter", "nameLocation": "1863:5:4", "nodeType": "VariableDeclaration", "scope": 433, "src": "1847:21:4", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 426, "name": "address", "nodeType": "ElementaryTypeName", "src": "1847:7:4", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 429, "indexed": false, "mutability": "mutable", "name": "support", "nameLocation": "1883:7:4", "nodeType": "VariableDeclaration", "scope": 433, "src": "1878:12:4", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 428, "name": "bool", "nodeType": "ElementaryTypeName", "src": "1878:4:4", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}, {"constant": false, "id": 431, "indexed": false, "mutability": "mutable", "name": "weight", "nameLocation": "1908:6:4", "nodeType": "VariableDeclaration", "scope": 433, "src": "1900:14:4", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 430, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1900:7:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "1801:119:4"}, "src": "1787:134:4"}, {"anonymous": false, "eventSelector": "712ae1383f79ac853f8d882153778e0260ef8f03b504e2866e0593e04d2b291f", "id": 437, "name": "ProposalExecuted", "nameLocation": "1932:16:4", "nodeType": "EventDefinition", "parameters": {"id": 436, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 435, "indexed": true, "mutability": "mutable", "name": "proposalId", "nameLocation": "1965:10:4", "nodeType": "VariableDeclaration", "scope": 437, "src": "1949:26:4", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 434, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1949:7:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "1948:28:4"}, "src": "1926:51:4"}, {"anonymous": false, "eventSelector": "543ba50a5eec5e6178218e364b1d0f396157b3c8fa278522c2cb7fd99407d474", "id": 443, "name": "FundsDeposited", "nameLocation": "1988:14:4", "nodeType": "EventDefinition", "parameters": {"id": 442, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 439, "indexed": true, "mutability": "mutable", "name": "depositor", "nameLocation": "2019:9:4", "nodeType": "VariableDeclaration", "scope": 443, "src": "2003:25:4", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 438, "name": "address", "nodeType": "ElementaryTypeName", "src": "2003:7:4", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 441, "indexed": false, "mutability": "mutable", "name": "amount", "nameLocation": "2038:6:4", "nodeType": "VariableDeclaration", "scope": 443, "src": "2030:14:4", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 440, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2030:7:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "2002:43:4"}, "src": "1982:64:4"}, {"anonymous": false, "eventSelector": "eaff4b37086828766ad3268786972c0cd24259d4c87a80f9d3963a3c3d999b0d", "id": 449, "name": "FundsWithdrawn", "nameLocation": "2057:14:4", "nodeType": "EventDefinition", "parameters": {"id": 448, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 445, "indexed": true, "mutability": "mutable", "name": "recipient", "nameLocation": "2088:9:4", "nodeType": "VariableDeclaration", "scope": 449, "src": "2072:25:4", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 444, "name": "address", "nodeType": "ElementaryTypeName", "src": "2072:7:4", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 447, "indexed": false, "mutability": "mutable", "name": "amount", "nameLocation": "2107:6:4", "nodeType": "VariableDeclaration", "scope": 449, "src": "2099:14:4", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 446, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2099:7:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "2071:43:4"}, "src": "2051:64:4"}, {"body": {"id": 465, "nodeType": "Block", "src": "2217:59:4", "statements": [{"expression": {"id": 463, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 459, "name": "governanceToken", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 337, "src": "2227:15:4", "typeDescriptions": {"typeIdentifier": "t_contract$_IERC20_$225", "typeString": "contract IERC20"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"arguments": [{"id": 461, "name": "_governanceToken", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 451, "src": "2252:16:4", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 460, "name": "IERC20", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 225, "src": "2245:6:4", "typeDescriptions": {"typeIdentifier": "t_type$_t_contract$_IERC20_$225_$", "typeString": "type(contract IERC20)"}}, "id": 462, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2245:24:4", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_contract$_IERC20_$225", "typeString": "contract IERC20"}}, "src": "2227:42:4", "typeDescriptions": {"typeIdentifier": "t_contract$_IERC20_$225", "typeString": "contract IERC20"}}, "id": 464, "nodeType": "ExpressionStatement", "src": "2227:42:4"}]}, "id": 466, "implemented": true, "kind": "constructor", "modifiers": [{"arguments": [{"id": 456, "name": "_owner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 453, "src": "2209:6:4", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "id": 457, "kind": "baseConstructorSpecifier", "modifierName": {"id": 455, "name": "Ownable", "nameLocations": ["2201:7:4"], "nodeType": "IdentifierPath", "referencedDeclaration": 147, "src": "2201:7:4"}, "nodeType": "ModifierInvocation", "src": "2201:15:4"}], "name": "", "nameLocation": "-1:-1:-1", "nodeType": "FunctionDefinition", "parameters": {"id": 454, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 451, "mutability": "mutable", "name": "_governanceToken", "nameLocation": "2154:16:4", "nodeType": "VariableDeclaration", "scope": 466, "src": "2146:24:4", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 450, "name": "address", "nodeType": "ElementaryTypeName", "src": "2146:7:4", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 453, "mutability": "mutable", "name": "_owner", "nameLocation": "2188:6:4", "nodeType": "VariableDeclaration", "scope": 466, "src": "2180:14:4", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 452, "name": "address", "nodeType": "ElementaryTypeName", "src": "2180:7:4", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "2136:64:4"}, "returnParameters": {"id": 458, "nodeType": "ParameterList", "parameters": [], "src": "2217:0:4"}, "scope": 1039, "src": "2125:151:4", "stateMutability": "nonpayable", "virtual": false, "visibility": "public"}, {"body": {"id": 543, "nodeType": "Block", "src": "2516:924:4", "statements": [{"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 488, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"expression": {"arguments": [{"id": 484, "name": "description", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 469, "src": "2540:11:4", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 483, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "2534:5:4", "typeDescriptions": {"typeIdentifier": "t_type$_t_bytes_storage_ptr_$", "typeString": "type(bytes storage pointer)"}, "typeName": {"id": 482, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "2534:5:4", "typeDescriptions": {}}}, "id": 485, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2534:18:4", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}, "id": 486, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2553:6:4", "memberName": "length", "nodeType": "MemberAccess", "src": "2534:25:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": ">", "rightExpression": {"hexValue": "30", "id": 487, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "2562:1:4", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "src": "2534:29:4", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"hexValue": "4465736372697074696f6e2063616e6e6f7420626520656d707479", "id": 489, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "2565:29:4", "typeDescriptions": {"typeIdentifier": "t_stringliteral_5136780377cc231258678d13fe3ea7b31bc96a5f8a36a4c81f29820551bb6214", "typeString": "literal_string \"Description cannot be empty\""}, "value": "Description cannot be empty"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_5136780377cc231258678d13fe3ea7b31bc96a5f8a36a4c81f29820551bb6214", "typeString": "literal_string \"Description cannot be empty\""}], "id": 481, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [-18, -18], "referencedDeclaration": -18, "src": "2526:7:4", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 490, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2526:69:4", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 491, "nodeType": "ExpressionStatement", "src": "2526:69:4"}, {"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 499, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"arguments": [{"expression": {"id": 495, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "2652:3:4", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 496, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2656:6:4", "memberName": "sender", "nodeType": "MemberAccess", "src": "2652:10:4", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "expression": {"id": 493, "name": "governanceToken", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 337, "src": "2626:15:4", "typeDescriptions": {"typeIdentifier": "t_contract$_IERC20_$225", "typeString": "contract IERC20"}}, "id": 494, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2642:9:4", "memberName": "balanceOf", "nodeType": "MemberAccess", "referencedDeclaration": 182, "src": "2626:25:4", "typeDescriptions": {"typeIdentifier": "t_function_external_view$_t_address_$returns$_t_uint256_$", "typeString": "function (address) view external returns (uint256)"}}, "id": 497, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2626:37:4", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": ">=", "rightExpression": {"id": 498, "name": "proposalThreshold", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 403, "src": "2667:17:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "2626:58:4", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"hexValue": "496e73756666696369656e7420746f6b656e7320746f206372656174652070726f706f73616c", "id": 500, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "2698:40:4", "typeDescriptions": {"typeIdentifier": "t_stringliteral_3a859c7fb3528a0be2605222ba4ecab7048a9913069439d97339b76854c46f69", "typeString": "literal_string \"Insufficient tokens to create proposal\""}, "value": "Insufficient tokens to create proposal"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_3a859c7fb3528a0be2605222ba4ecab7048a9913069439d97339b76854c46f69", "typeString": "literal_string \"Insufficient tokens to create proposal\""}], "id": 492, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [-18, -18], "referencedDeclaration": -18, "src": "2605:7:4", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 501, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2605:143:4", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 502, "nodeType": "ExpressionStatement", "src": "2605:143:4"}, {"assignments": [504], "declarations": [{"constant": false, "id": 504, "mutability": "mutable", "name": "proposalId", "nameLocation": "2775:10:4", "nodeType": "VariableDeclaration", "scope": 543, "src": "2767:18:4", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 503, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2767:7:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 507, "initialValue": {"id": 506, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "++", "prefix": false, "src": "2788:15:4", "subExpression": {"id": 505, "name": "proposalCount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 393, "src": "2788:13:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "VariableDeclarationStatement", "src": "2767:36:4"}, {"expression": {"id": 531, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"baseExpression": {"id": 508, "name": "proposals", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 379, "src": "2822:9:4", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_uint256_$_t_struct$_Proposal_$374_storage_$", "typeString": "mapping(uint256 => struct SimpleDAO.Proposal storage ref)"}}, "id": 510, "indexExpression": {"id": 509, "name": "proposalId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 504, "src": "2832:10:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "2822:21:4", "typeDescriptions": {"typeIdentifier": "t_struct$_Proposal_$374_storage", "typeString": "struct SimpleDAO.Proposal storage ref"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"arguments": [{"id": 512, "name": "proposalId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 504, "src": "2873:10:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"expression": {"id": 513, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "2907:3:4", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 514, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2911:6:4", "memberName": "sender", "nodeType": "MemberAccess", "src": "2907:10:4", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 515, "name": "description", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 469, "src": "2944:11:4", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 516, "name": "proposalType", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 472, "src": "2983:12:4", "typeDescriptions": {"typeIdentifier": "t_enum$_ProposalType_$347", "typeString": "enum SimpleDAO.ProposalType"}}, {"id": 517, "name": "requestedAmount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 474, "src": "3026:15:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"id": 518, "name": "beneficiary", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 476, "src": "3068:11:4", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"hexValue": "30", "id": 519, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "3103:1:4", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, {"hexValue": "30", "id": 520, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "3132:1:4", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, {"expression": {"id": 521, "name": "block", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -4, "src": "3158:5:4", "typeDescriptions": {"typeIdentifier": "t_magic_block", "typeString": "block"}}, "id": 522, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "3164:9:4", "memberName": "timestamp", "nodeType": "MemberAccess", "src": "3158:15:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 526, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"expression": {"id": 523, "name": "block", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -4, "src": "3196:5:4", "typeDescriptions": {"typeIdentifier": "t_magic_block", "typeString": "block"}}, "id": 524, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "3202:9:4", "memberName": "timestamp", "nodeType": "MemberAccess", "src": "3196:15:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "+", "rightExpression": {"id": 525, "name": "votingPeriod", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 396, "src": "3214:12:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "3196:30:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"expression": {"id": 527, "name": "ProposalState", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 343, "src": "3247:13:4", "typeDescriptions": {"typeIdentifier": "t_type$_t_enum$_ProposalState_$343_$", "typeString": "type(enum SimpleDAO.ProposalState)"}}, "id": 528, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "3261:6:4", "memberName": "ACTIVE", "nodeType": "MemberAccess", "referencedDeclaration": 339, "src": "3247:20:4", "typeDescriptions": {"typeIdentifier": "t_enum$_ProposalState_$343", "typeString": "enum SimpleDAO.ProposalState"}}, {"hexValue": "66616c7365", "id": 529, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "3291:5:4", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "false"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_enum$_ProposalType_$347", "typeString": "enum SimpleDAO.ProposalType"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_enum$_ProposalState_$343", "typeString": "enum SimpleDAO.ProposalState"}, {"typeIdentifier": "t_bool", "typeString": "bool"}], "id": 511, "name": "Proposal", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 374, "src": "2846:8:4", "typeDescriptions": {"typeIdentifier": "t_type$_t_struct$_Proposal_$374_storage_ptr_$", "typeString": "type(struct SimpleDAO.Proposal storage pointer)"}}, "id": 530, "isConstant": false, "isLValue": false, "isPure": false, "kind": "structConstructorCall", "lValueRequested": false, "nameLocations": ["2869:2:4", "2897:8:4", "2931:11:4", "2969:12:4", "3009:15:4", "3055:11:4", "3093:8:4", "3118:12:4", "3147:9:4", "3187:7:4", "3240:5:4", "3281:8:4"], "names": ["id", "proposer", "description", "proposalType", "requestedAmount", "beneficiary", "forVotes", "againstVotes", "startTime", "endTime", "state", "executed"], "nodeType": "FunctionCall", "src": "2846:461:4", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_struct$_Proposal_$374_memory_ptr", "typeString": "struct SimpleDAO.Proposal memory"}}, "src": "2822:485:4", "typeDescriptions": {"typeIdentifier": "t_struct$_Proposal_$374_storage", "typeString": "struct SimpleDAO.Proposal storage ref"}}, "id": 532, "nodeType": "ExpressionStatement", "src": "2822:485:4"}, {"eventCall": {"arguments": [{"id": 534, "name": "proposalId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 504, "src": "3347:10:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"expression": {"id": 535, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "3359:3:4", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 536, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "3363:6:4", "memberName": "sender", "nodeType": "MemberAccess", "src": "3359:10:4", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 537, "name": "description", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 469, "src": "3371:11:4", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 538, "name": "proposalType", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 472, "src": "3384:12:4", "typeDescriptions": {"typeIdentifier": "t_enum$_ProposalType_$347", "typeString": "enum SimpleDAO.ProposalType"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_enum$_ProposalType_$347", "typeString": "enum SimpleDAO.ProposalType"}], "id": 533, "name": "ProposalCreated", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 423, "src": "3331:15:4", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_uint256_$_t_address_$_t_string_memory_ptr_$_t_enum$_ProposalType_$347_$returns$__$", "typeString": "function (uint256,address,string memory,enum SimpleDAO.ProposalType)"}}, "id": 539, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3331:66:4", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 540, "nodeType": "EmitStatement", "src": "3326:71:4"}, {"expression": {"id": 541, "name": "proposalId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 504, "src": "3423:10:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "functionReturnParameters": 480, "id": 542, "nodeType": "Return", "src": "3416:17:4"}]}, "documentation": {"id": 467, "nodeType": "StructuredDocumentation", "src": "2286:36:4", "text": " @dev 创建提案"}, "functionSelector": "1a216bbd", "id": 544, "implemented": true, "kind": "function", "modifiers": [], "name": "createProposal", "nameLocation": "2336:14:4", "nodeType": "FunctionDefinition", "parameters": {"id": 477, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 469, "mutability": "mutable", "name": "description", "nameLocation": "2374:11:4", "nodeType": "VariableDeclaration", "scope": 544, "src": "2360:25:4", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 468, "name": "string", "nodeType": "ElementaryTypeName", "src": "2360:6:4", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 472, "mutability": "mutable", "name": "proposalType", "nameLocation": "2408:12:4", "nodeType": "VariableDeclaration", "scope": 544, "src": "2395:25:4", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_enum$_ProposalType_$347", "typeString": "enum SimpleDAO.ProposalType"}, "typeName": {"id": 471, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 470, "name": "ProposalType", "nameLocations": ["2395:12:4"], "nodeType": "IdentifierPath", "referencedDeclaration": 347, "src": "2395:12:4"}, "referencedDeclaration": 347, "src": "2395:12:4", "typeDescriptions": {"typeIdentifier": "t_enum$_ProposalType_$347", "typeString": "enum SimpleDAO.ProposalType"}}, "visibility": "internal"}, {"constant": false, "id": 474, "mutability": "mutable", "name": "requestedAmount", "nameLocation": "2438:15:4", "nodeType": "VariableDeclaration", "scope": 544, "src": "2430:23:4", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 473, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2430:7:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 476, "mutability": "mutable", "name": "beneficiary", "nameLocation": "2471:11:4", "nodeType": "VariableDeclaration", "scope": 544, "src": "2463:19:4", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 475, "name": "address", "nodeType": "ElementaryTypeName", "src": "2463:7:4", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "2350:138:4"}, "returnParameters": {"id": 480, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 479, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 544, "src": "2507:7:4", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 478, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2507:7:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "2506:9:4"}, "scope": 1039, "src": "2327:1113:4", "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}, {"body": {"id": 645, "nodeType": "Block", "src": "3542:743:4", "statements": [{"assignments": [554], "declarations": [{"constant": false, "id": 554, "mutability": "mutable", "name": "proposal", "nameLocation": "3569:8:4", "nodeType": "VariableDeclaration", "scope": 645, "src": "3552:25:4", "stateVariable": false, "storageLocation": "storage", "typeDescriptions": {"typeIdentifier": "t_struct$_Proposal_$374_storage_ptr", "typeString": "struct SimpleDAO.Proposal"}, "typeName": {"id": 553, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 552, "name": "Proposal", "nameLocations": ["3552:8:4"], "nodeType": "IdentifierPath", "referencedDeclaration": 374, "src": "3552:8:4"}, "referencedDeclaration": 374, "src": "3552:8:4", "typeDescriptions": {"typeIdentifier": "t_struct$_Proposal_$374_storage_ptr", "typeString": "struct SimpleDAO.Proposal"}}, "visibility": "internal"}], "id": 558, "initialValue": {"baseExpression": {"id": 555, "name": "proposals", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 379, "src": "3580:9:4", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_uint256_$_t_struct$_Proposal_$374_storage_$", "typeString": "mapping(uint256 => struct SimpleDAO.Proposal storage ref)"}}, "id": 557, "indexExpression": {"id": 556, "name": "proposalId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 547, "src": "3590:10:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "3580:21:4", "typeDescriptions": {"typeIdentifier": "t_struct$_Proposal_$374_storage", "typeString": "struct SimpleDAO.Proposal storage ref"}}, "nodeType": "VariableDeclarationStatement", "src": "3552:49:4"}, {"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_enum$_ProposalState_$343", "typeString": "enum SimpleDAO.ProposalState"}, "id": 564, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"expression": {"id": 560, "name": "proposal", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 554, "src": "3619:8:4", "typeDescriptions": {"typeIdentifier": "t_struct$_Proposal_$374_storage_ptr", "typeString": "struct SimpleDAO.Proposal storage pointer"}}, "id": 561, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "3628:5:4", "memberName": "state", "nodeType": "MemberAccess", "referencedDeclaration": 371, "src": "3619:14:4", "typeDescriptions": {"typeIdentifier": "t_enum$_ProposalState_$343", "typeString": "enum SimpleDAO.ProposalState"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"expression": {"id": 562, "name": "ProposalState", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 343, "src": "3637:13:4", "typeDescriptions": {"typeIdentifier": "t_type$_t_enum$_ProposalState_$343_$", "typeString": "type(enum SimpleDAO.ProposalState)"}}, "id": 563, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "3651:6:4", "memberName": "ACTIVE", "nodeType": "MemberAccess", "referencedDeclaration": 339, "src": "3637:20:4", "typeDescriptions": {"typeIdentifier": "t_enum$_ProposalState_$343", "typeString": "enum SimpleDAO.ProposalState"}}, "src": "3619:38:4", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"hexValue": "50726f706f73616c206e6f7420616374697665", "id": 565, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "3659:21:4", "typeDescriptions": {"typeIdentifier": "t_stringliteral_4952ffe616f3390d5bf18613b3e7012373772f13f0f0e4152890ed9947100a04", "typeString": "literal_string \"Proposal not active\""}, "value": "Proposal not active"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_4952ffe616f3390d5bf18613b3e7012373772f13f0f0e4152890ed9947100a04", "typeString": "literal_string \"Proposal not active\""}], "id": 559, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [-18, -18], "referencedDeclaration": -18, "src": "3611:7:4", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 566, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3611:70:4", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 567, "nodeType": "ExpressionStatement", "src": "3611:70:4"}, {"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 573, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"expression": {"id": 569, "name": "block", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -4, "src": "3699:5:4", "typeDescriptions": {"typeIdentifier": "t_magic_block", "typeString": "block"}}, "id": 570, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "3705:9:4", "memberName": "timestamp", "nodeType": "MemberAccess", "src": "3699:15:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<=", "rightExpression": {"expression": {"id": 571, "name": "proposal", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 554, "src": "3718:8:4", "typeDescriptions": {"typeIdentifier": "t_struct$_Proposal_$374_storage_ptr", "typeString": "struct SimpleDAO.Proposal storage pointer"}}, "id": 572, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "3727:7:4", "memberName": "endTime", "nodeType": "MemberAccess", "referencedDeclaration": 368, "src": "3718:16:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "3699:35:4", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"hexValue": "566f74696e6720706572696f6420656e646564", "id": 574, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "3736:21:4", "typeDescriptions": {"typeIdentifier": "t_stringliteral_36ba02d776fd732ac627a72d2a4bcd896afdeadba8694171733d88c6775413e0", "typeString": "literal_string \"Voting period ended\""}, "value": "Voting period ended"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_36ba02d776fd732ac627a72d2a4bcd896afdeadba8694171733d88c6775413e0", "typeString": "literal_string \"Voting period ended\""}], "id": 568, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [-18, -18], "referencedDeclaration": -18, "src": "3691:7:4", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 575, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3691:67:4", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 576, "nodeType": "ExpressionStatement", "src": "3691:67:4"}, {"expression": {"arguments": [{"id": 584, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "!", "prefix": true, "src": "3776:33:4", "subExpression": {"baseExpression": {"baseExpression": {"id": 578, "name": "hasVoted", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 385, "src": "3777:8:4", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_uint256_$_t_mapping$_t_address_$_t_bool_$_$", "typeString": "mapping(uint256 => mapping(address => bool))"}}, "id": 580, "indexExpression": {"id": 579, "name": "proposalId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 547, "src": "3786:10:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "3777:20:4", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_bool_$", "typeString": "mapping(address => bool)"}}, "id": 583, "indexExpression": {"expression": {"id": 581, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "3798:3:4", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 582, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "3802:6:4", "memberName": "sender", "nodeType": "MemberAccess", "src": "3798:10:4", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "3777:32:4", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"hexValue": "416c726561647920766f746564", "id": 585, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "3811:15:4", "typeDescriptions": {"typeIdentifier": "t_stringliteral_512fc59044d4f0722f9346c450973ffe8aac7aa1142e536739987018593c53b6", "typeString": "literal_string \"Already voted\""}, "value": "Already voted"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_512fc59044d4f0722f9346c450973ffe8aac7aa1142e536739987018593c53b6", "typeString": "literal_string \"Already voted\""}], "id": 577, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [-18, -18], "referencedDeclaration": -18, "src": "3768:7:4", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 586, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3768:59:4", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 587, "nodeType": "ExpressionStatement", "src": "3768:59:4"}, {"assignments": [589], "declarations": [{"constant": false, "id": 589, "mutability": "mutable", "name": "weight", "nameLocation": "3854:6:4", "nodeType": "VariableDeclaration", "scope": 645, "src": "3846:14:4", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 588, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "3846:7:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 595, "initialValue": {"arguments": [{"expression": {"id": 592, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "3889:3:4", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 593, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "3893:6:4", "memberName": "sender", "nodeType": "MemberAccess", "src": "3889:10:4", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "expression": {"id": 590, "name": "governanceToken", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 337, "src": "3863:15:4", "typeDescriptions": {"typeIdentifier": "t_contract$_IERC20_$225", "typeString": "contract IERC20"}}, "id": 591, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "3879:9:4", "memberName": "balanceOf", "nodeType": "MemberAccess", "referencedDeclaration": 182, "src": "3863:25:4", "typeDescriptions": {"typeIdentifier": "t_function_external_view$_t_address_$returns$_t_uint256_$", "typeString": "function (address) view external returns (uint256)"}}, "id": 594, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3863:37:4", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "VariableDeclarationStatement", "src": "3846:54:4"}, {"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 599, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 597, "name": "weight", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 589, "src": "3918:6:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": ">", "rightExpression": {"hexValue": "30", "id": 598, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "3927:1:4", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "src": "3918:10:4", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"hexValue": "4e6f20766f74696e6720706f776572", "id": 600, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "3930:17:4", "typeDescriptions": {"typeIdentifier": "t_stringliteral_47142a302de900bb01c8ae0ff1dc734ff4eef8ce501aaff55c4025f281594ec8", "typeString": "literal_string \"No voting power\""}, "value": "No voting power"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_47142a302de900bb01c8ae0ff1dc734ff4eef8ce501aaff55c4025f281594ec8", "typeString": "literal_string \"No voting power\""}], "id": 596, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [-18, -18], "referencedDeclaration": -18, "src": "3910:7:4", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 601, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3910:38:4", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 602, "nodeType": "ExpressionStatement", "src": "3910:38:4"}, {"expression": {"id": 610, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"baseExpression": {"baseExpression": {"id": 603, "name": "hasVoted", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 385, "src": "3967:8:4", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_uint256_$_t_mapping$_t_address_$_t_bool_$_$", "typeString": "mapping(uint256 => mapping(address => bool))"}}, "id": 607, "indexExpression": {"id": 604, "name": "proposalId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 547, "src": "3976:10:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "3967:20:4", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_bool_$", "typeString": "mapping(address => bool)"}}, "id": 608, "indexExpression": {"expression": {"id": 605, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "3988:3:4", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 606, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "3992:6:4", "memberName": "sender", "nodeType": "MemberAccess", "src": "3988:10:4", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "3967:32:4", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"hexValue": "74727565", "id": 609, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "4002:4:4", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "true"}, "src": "3967:39:4", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 611, "nodeType": "ExpressionStatement", "src": "3967:39:4"}, {"expression": {"id": 619, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"baseExpression": {"baseExpression": {"id": 612, "name": "voteChoice", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 391, "src": "4016:10:4", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_uint256_$_t_mapping$_t_address_$_t_bool_$_$", "typeString": "mapping(uint256 => mapping(address => bool))"}}, "id": 616, "indexExpression": {"id": 613, "name": "proposalId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 547, "src": "4027:10:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "4016:22:4", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_bool_$", "typeString": "mapping(address => bool)"}}, "id": 617, "indexExpression": {"expression": {"id": 614, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "4039:3:4", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 615, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "4043:6:4", "memberName": "sender", "nodeType": "MemberAccess", "src": "4039:10:4", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "4016:34:4", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 618, "name": "support", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 549, "src": "4053:7:4", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "src": "4016:44:4", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 620, "nodeType": "ExpressionStatement", "src": "4016:44:4"}, {"condition": {"id": 621, "name": "support", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 549, "src": "4083:7:4", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "falseBody": {"id": 635, "nodeType": "Block", "src": "4150:56:4", "statements": [{"expression": {"id": 633, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"expression": {"id": 629, "name": "proposal", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 554, "src": "4164:8:4", "typeDescriptions": {"typeIdentifier": "t_struct$_Proposal_$374_storage_ptr", "typeString": "struct SimpleDAO.Proposal storage pointer"}}, "id": 631, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "memberLocation": "4173:12:4", "memberName": "againstVotes", "nodeType": "MemberAccess", "referencedDeclaration": 364, "src": "4164:21:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "+=", "rightHandSide": {"id": 632, "name": "weight", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 589, "src": "4189:6:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "4164:31:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 634, "nodeType": "ExpressionStatement", "src": "4164:31:4"}]}, "id": 636, "nodeType": "IfStatement", "src": "4079:127:4", "trueBody": {"id": 628, "nodeType": "Block", "src": "4092:52:4", "statements": [{"expression": {"id": 626, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"expression": {"id": 622, "name": "proposal", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 554, "src": "4106:8:4", "typeDescriptions": {"typeIdentifier": "t_struct$_Proposal_$374_storage_ptr", "typeString": "struct SimpleDAO.Proposal storage pointer"}}, "id": 624, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "memberLocation": "4115:8:4", "memberName": "forVotes", "nodeType": "MemberAccess", "referencedDeclaration": 362, "src": "4106:17:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "+=", "rightHandSide": {"id": 625, "name": "weight", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 589, "src": "4127:6:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "4106:27:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 627, "nodeType": "ExpressionStatement", "src": "4106:27:4"}]}}, {"eventCall": {"arguments": [{"id": 638, "name": "proposalId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 547, "src": "4238:10:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"expression": {"id": 639, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "4250:3:4", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 640, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "4254:6:4", "memberName": "sender", "nodeType": "MemberAccess", "src": "4250:10:4", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 641, "name": "support", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 549, "src": "4262:7:4", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"id": 642, "name": "weight", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 589, "src": "4271:6:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 637, "name": "VoteCast", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 433, "src": "4229:8:4", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_uint256_$_t_address_$_t_bool_$_t_uint256_$returns$__$", "typeString": "function (uint256,address,bool,uint256)"}}, "id": 643, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4229:49:4", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 644, "nodeType": "EmitStatement", "src": "4224:54:4"}]}, "documentation": {"id": 545, "nodeType": "StructuredDocumentation", "src": "3450:30:4", "text": " @dev 投票"}, "functionSelector": "c9d27afe", "id": 646, "implemented": true, "kind": "function", "modifiers": [], "name": "vote", "nameLocation": "3494:4:4", "nodeType": "FunctionDefinition", "parameters": {"id": 550, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 547, "mutability": "mutable", "name": "proposalId", "nameLocation": "3507:10:4", "nodeType": "VariableDeclaration", "scope": 646, "src": "3499:18:4", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 546, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "3499:7:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 549, "mutability": "mutable", "name": "support", "nameLocation": "3524:7:4", "nodeType": "VariableDeclaration", "scope": 646, "src": "3519:12:4", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 548, "name": "bool", "nodeType": "ElementaryTypeName", "src": "3519:4:4", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "3498:34:4"}, "returnParameters": {"id": 551, "nodeType": "ParameterList", "parameters": [], "src": "3542:0:4"}, "scope": 1039, "src": "3485:800:4", "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}, {"body": {"id": 724, "nodeType": "Block", "src": "4398:673:4", "statements": [{"assignments": [654], "declarations": [{"constant": false, "id": 654, "mutability": "mutable", "name": "proposal", "nameLocation": "4425:8:4", "nodeType": "VariableDeclaration", "scope": 724, "src": "4408:25:4", "stateVariable": false, "storageLocation": "storage", "typeDescriptions": {"typeIdentifier": "t_struct$_Proposal_$374_storage_ptr", "typeString": "struct SimpleDAO.Proposal"}, "typeName": {"id": 653, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 652, "name": "Proposal", "nameLocations": ["4408:8:4"], "nodeType": "IdentifierPath", "referencedDeclaration": 374, "src": "4408:8:4"}, "referencedDeclaration": 374, "src": "4408:8:4", "typeDescriptions": {"typeIdentifier": "t_struct$_Proposal_$374_storage_ptr", "typeString": "struct SimpleDAO.Proposal"}}, "visibility": "internal"}], "id": 658, "initialValue": {"baseExpression": {"id": 655, "name": "proposals", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 379, "src": "4436:9:4", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_uint256_$_t_struct$_Proposal_$374_storage_$", "typeString": "mapping(uint256 => struct SimpleDAO.Proposal storage ref)"}}, "id": 657, "indexExpression": {"id": 656, "name": "proposalId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 649, "src": "4446:10:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "4436:21:4", "typeDescriptions": {"typeIdentifier": "t_struct$_Proposal_$374_storage", "typeString": "struct SimpleDAO.Proposal storage ref"}}, "nodeType": "VariableDeclarationStatement", "src": "4408:49:4"}, {"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_enum$_ProposalState_$343", "typeString": "enum SimpleDAO.ProposalState"}, "id": 664, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"expression": {"id": 660, "name": "proposal", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 654, "src": "4475:8:4", "typeDescriptions": {"typeIdentifier": "t_struct$_Proposal_$374_storage_ptr", "typeString": "struct SimpleDAO.Proposal storage pointer"}}, "id": 661, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "4484:5:4", "memberName": "state", "nodeType": "MemberAccess", "referencedDeclaration": 371, "src": "4475:14:4", "typeDescriptions": {"typeIdentifier": "t_enum$_ProposalState_$343", "typeString": "enum SimpleDAO.ProposalState"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"expression": {"id": 662, "name": "ProposalState", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 343, "src": "4493:13:4", "typeDescriptions": {"typeIdentifier": "t_type$_t_enum$_ProposalState_$343_$", "typeString": "type(enum SimpleDAO.ProposalState)"}}, "id": 663, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "4507:6:4", "memberName": "ACTIVE", "nodeType": "MemberAccess", "referencedDeclaration": 339, "src": "4493:20:4", "typeDescriptions": {"typeIdentifier": "t_enum$_ProposalState_$343", "typeString": "enum SimpleDAO.ProposalState"}}, "src": "4475:38:4", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"hexValue": "50726f706f73616c206e6f7420616374697665", "id": 665, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "4515:21:4", "typeDescriptions": {"typeIdentifier": "t_stringliteral_4952ffe616f3390d5bf18613b3e7012373772f13f0f0e4152890ed9947100a04", "typeString": "literal_string \"Proposal not active\""}, "value": "Proposal not active"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_4952ffe616f3390d5bf18613b3e7012373772f13f0f0e4152890ed9947100a04", "typeString": "literal_string \"Proposal not active\""}], "id": 659, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [-18, -18], "referencedDeclaration": -18, "src": "4467:7:4", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 666, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4467:70:4", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 667, "nodeType": "ExpressionStatement", "src": "4467:70:4"}, {"condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 672, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"expression": {"id": 668, "name": "block", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -4, "src": "4560:5:4", "typeDescriptions": {"typeIdentifier": "t_magic_block", "typeString": "block"}}, "id": 669, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "4566:9:4", "memberName": "timestamp", "nodeType": "MemberAccess", "src": "4560:15:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": ">", "rightExpression": {"expression": {"id": 670, "name": "proposal", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 654, "src": "4578:8:4", "typeDescriptions": {"typeIdentifier": "t_struct$_Proposal_$374_storage_ptr", "typeString": "struct SimpleDAO.Proposal storage pointer"}}, "id": 671, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "4587:7:4", "memberName": "endTime", "nodeType": "MemberAccess", "referencedDeclaration": 368, "src": "4578:16:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "4560:34:4", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 723, "nodeType": "IfStatement", "src": "4556:509:4", "trueBody": {"id": 722, "nodeType": "Block", "src": "4596:469:4", "statements": [{"assignments": [674], "declarations": [{"constant": false, "id": 674, "mutability": "mutable", "name": "totalVotes", "nameLocation": "4618:10:4", "nodeType": "VariableDeclaration", "scope": 722, "src": "4610:18:4", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 673, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "4610:7:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 680, "initialValue": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 679, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"expression": {"id": 675, "name": "proposal", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 654, "src": "4631:8:4", "typeDescriptions": {"typeIdentifier": "t_struct$_Proposal_$374_storage_ptr", "typeString": "struct SimpleDAO.Proposal storage pointer"}}, "id": 676, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "4640:8:4", "memberName": "forVotes", "nodeType": "MemberAccess", "referencedDeclaration": 362, "src": "4631:17:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "+", "rightExpression": {"expression": {"id": 677, "name": "proposal", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 654, "src": "4651:8:4", "typeDescriptions": {"typeIdentifier": "t_struct$_Proposal_$374_storage_ptr", "typeString": "struct SimpleDAO.Proposal storage pointer"}}, "id": 678, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "4660:12:4", "memberName": "againstVotes", "nodeType": "MemberAccess", "referencedDeclaration": 364, "src": "4651:21:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "4631:41:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "VariableDeclarationStatement", "src": "4610:62:4"}, {"assignments": [682], "declarations": [{"constant": false, "id": 682, "mutability": "mutable", "name": "totalSupply", "nameLocation": "4694:11:4", "nodeType": "VariableDeclaration", "scope": 722, "src": "4686:19:4", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 681, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "4686:7:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 686, "initialValue": {"arguments": [], "expression": {"argumentTypes": [], "expression": {"id": 683, "name": "governanceToken", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 337, "src": "4708:15:4", "typeDescriptions": {"typeIdentifier": "t_contract$_IERC20_$225", "typeString": "contract IERC20"}}, "id": 684, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "4724:11:4", "memberName": "totalSupply", "nodeType": "MemberAccess", "referencedDeclaration": 174, "src": "4708:27:4", "typeDescriptions": {"typeIdentifier": "t_function_external_view$__$returns$_t_uint256_$", "typeString": "function () view external returns (uint256)"}}, "id": 685, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4708:29:4", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "VariableDeclarationStatement", "src": "4686:51:4"}, {"assignments": [688], "declarations": [{"constant": false, "id": 688, "mutability": "mutable", "name": "quorum", "nameLocation": "4759:6:4", "nodeType": "VariableDeclaration", "scope": 722, "src": "4751:14:4", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 687, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "4751:7:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 695, "initialValue": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 694, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"components": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 691, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 689, "name": "totalSupply", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 682, "src": "4769:11:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "*", "rightExpression": {"id": 690, "name": "quorumPercentage", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 406, "src": "4783:16:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "4769:30:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "id": 692, "isConstant": false, "isInlineArray": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "TupleExpression", "src": "4768:32:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "/", "rightExpression": {"hexValue": "313030", "id": 693, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "4803:3:4", "typeDescriptions": {"typeIdentifier": "t_rational_100_by_1", "typeString": "int_const 100"}, "value": "100"}, "src": "4768:38:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "VariableDeclarationStatement", "src": "4751:55:4"}, {"condition": {"commonType": {"typeIdentifier": "t_bool", "typeString": "bool"}, "id": 704, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 698, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 696, "name": "totalVotes", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 674, "src": "4837:10:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": ">=", "rightExpression": {"id": 697, "name": "quorum", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 688, "src": "4851:6:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "4837:20:4", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "BinaryOperation", "operator": "&&", "rightExpression": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 703, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"expression": {"id": 699, "name": "proposal", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 654, "src": "4861:8:4", "typeDescriptions": {"typeIdentifier": "t_struct$_Proposal_$374_storage_ptr", "typeString": "struct SimpleDAO.Proposal storage pointer"}}, "id": 700, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "4870:8:4", "memberName": "forVotes", "nodeType": "MemberAccess", "referencedDeclaration": 362, "src": "4861:17:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": ">", "rightExpression": {"expression": {"id": 701, "name": "proposal", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 654, "src": "4881:8:4", "typeDescriptions": {"typeIdentifier": "t_struct$_Proposal_$374_storage_ptr", "typeString": "struct SimpleDAO.Proposal storage pointer"}}, "id": 702, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "4890:12:4", "memberName": "againstVotes", "nodeType": "MemberAccess", "referencedDeclaration": 364, "src": "4881:21:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "4861:41:4", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "src": "4837:65:4", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "falseBody": {"id": 720, "nodeType": "Block", "src": "4983:72:4", "statements": [{"expression": {"id": 718, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"expression": {"id": 713, "name": "proposal", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 654, "src": "5001:8:4", "typeDescriptions": {"typeIdentifier": "t_struct$_Proposal_$374_storage_ptr", "typeString": "struct SimpleDAO.Proposal storage pointer"}}, "id": 715, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "memberLocation": "5010:5:4", "memberName": "state", "nodeType": "MemberAccess", "referencedDeclaration": 371, "src": "5001:14:4", "typeDescriptions": {"typeIdentifier": "t_enum$_ProposalState_$343", "typeString": "enum SimpleDAO.ProposalState"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"expression": {"id": 716, "name": "ProposalState", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 343, "src": "5018:13:4", "typeDescriptions": {"typeIdentifier": "t_type$_t_enum$_ProposalState_$343_$", "typeString": "type(enum SimpleDAO.ProposalState)"}}, "id": 717, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "5032:8:4", "memberName": "DEFEATED", "nodeType": "MemberAccess", "referencedDeclaration": 341, "src": "5018:22:4", "typeDescriptions": {"typeIdentifier": "t_enum$_ProposalState_$343", "typeString": "enum SimpleDAO.ProposalState"}}, "src": "5001:39:4", "typeDescriptions": {"typeIdentifier": "t_enum$_ProposalState_$343", "typeString": "enum SimpleDAO.ProposalState"}}, "id": 719, "nodeType": "ExpressionStatement", "src": "5001:39:4"}]}, "id": 721, "nodeType": "IfStatement", "src": "4833:222:4", "trueBody": {"id": 712, "nodeType": "Block", "src": "4904:73:4", "statements": [{"expression": {"id": 710, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"expression": {"id": 705, "name": "proposal", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 654, "src": "4922:8:4", "typeDescriptions": {"typeIdentifier": "t_struct$_Proposal_$374_storage_ptr", "typeString": "struct SimpleDAO.Proposal storage pointer"}}, "id": 707, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "memberLocation": "4931:5:4", "memberName": "state", "nodeType": "MemberAccess", "referencedDeclaration": 371, "src": "4922:14:4", "typeDescriptions": {"typeIdentifier": "t_enum$_ProposalState_$343", "typeString": "enum SimpleDAO.ProposalState"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"expression": {"id": 708, "name": "ProposalState", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 343, "src": "4939:13:4", "typeDescriptions": {"typeIdentifier": "t_type$_t_enum$_ProposalState_$343_$", "typeString": "type(enum SimpleDAO.ProposalState)"}}, "id": 709, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "4953:9:4", "memberName": "SUCCEEDED", "nodeType": "MemberAccess", "referencedDeclaration": 340, "src": "4939:23:4", "typeDescriptions": {"typeIdentifier": "t_enum$_ProposalState_$343", "typeString": "enum SimpleDAO.ProposalState"}}, "src": "4922:40:4", "typeDescriptions": {"typeIdentifier": "t_enum$_ProposalState_$343", "typeString": "enum SimpleDAO.ProposalState"}}, "id": 711, "nodeType": "ExpressionStatement", "src": "4922:40:4"}]}}]}}]}, "documentation": {"id": 647, "nodeType": "StructuredDocumentation", "src": "4295:42:4", "text": " @dev 更新提案状态"}, "functionSelector": "6ac600c7", "id": 725, "implemented": true, "kind": "function", "modifiers": [], "name": "updateProposalState", "nameLocation": "4351:19:4", "nodeType": "FunctionDefinition", "parameters": {"id": 650, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 649, "mutability": "mutable", "name": "proposalId", "nameLocation": "4379:10:4", "nodeType": "VariableDeclaration", "scope": 725, "src": "4371:18:4", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 648, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "4371:7:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "4370:20:4"}, "returnParameters": {"id": 651, "nodeType": "ParameterList", "parameters": [], "src": "4398:0:4"}, "scope": 1039, "src": "4342:729:4", "stateMutability": "nonpayable", "virtual": false, "visibility": "public"}, {"body": {"id": 834, "nodeType": "Block", "src": "5189:999:4", "statements": [{"assignments": [735], "declarations": [{"constant": false, "id": 735, "mutability": "mutable", "name": "proposal", "nameLocation": "5216:8:4", "nodeType": "VariableDeclaration", "scope": 834, "src": "5199:25:4", "stateVariable": false, "storageLocation": "storage", "typeDescriptions": {"typeIdentifier": "t_struct$_Proposal_$374_storage_ptr", "typeString": "struct SimpleDAO.Proposal"}, "typeName": {"id": 734, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 733, "name": "Proposal", "nameLocations": ["5199:8:4"], "nodeType": "IdentifierPath", "referencedDeclaration": 374, "src": "5199:8:4"}, "referencedDeclaration": 374, "src": "5199:8:4", "typeDescriptions": {"typeIdentifier": "t_struct$_Proposal_$374_storage_ptr", "typeString": "struct SimpleDAO.Proposal"}}, "visibility": "internal"}], "id": 739, "initialValue": {"baseExpression": {"id": 736, "name": "proposals", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 379, "src": "5227:9:4", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_uint256_$_t_struct$_Proposal_$374_storage_$", "typeString": "mapping(uint256 => struct SimpleDAO.Proposal storage ref)"}}, "id": 738, "indexExpression": {"id": 737, "name": "proposalId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 728, "src": "5237:10:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "5227:21:4", "typeDescriptions": {"typeIdentifier": "t_struct$_Proposal_$374_storage", "typeString": "struct SimpleDAO.Proposal storage ref"}}, "nodeType": "VariableDeclarationStatement", "src": "5199:49:4"}, {"expression": {"arguments": [{"id": 741, "name": "proposalId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 728, "src": "5278:10:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 740, "name": "updateProposalState", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 725, "src": "5258:19:4", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_uint256_$returns$__$", "typeString": "function (uint256)"}}, "id": 742, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5258:31:4", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 743, "nodeType": "ExpressionStatement", "src": "5258:31:4"}, {"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_enum$_ProposalState_$343", "typeString": "enum SimpleDAO.ProposalState"}, "id": 749, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"expression": {"id": 745, "name": "proposal", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 735, "src": "5316:8:4", "typeDescriptions": {"typeIdentifier": "t_struct$_Proposal_$374_storage_ptr", "typeString": "struct SimpleDAO.Proposal storage pointer"}}, "id": 746, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "5325:5:4", "memberName": "state", "nodeType": "MemberAccess", "referencedDeclaration": 371, "src": "5316:14:4", "typeDescriptions": {"typeIdentifier": "t_enum$_ProposalState_$343", "typeString": "enum SimpleDAO.ProposalState"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"expression": {"id": 747, "name": "ProposalState", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 343, "src": "5334:13:4", "typeDescriptions": {"typeIdentifier": "t_type$_t_enum$_ProposalState_$343_$", "typeString": "type(enum SimpleDAO.ProposalState)"}}, "id": 748, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "5348:9:4", "memberName": "SUCCEEDED", "nodeType": "MemberAccess", "referencedDeclaration": 340, "src": "5334:23:4", "typeDescriptions": {"typeIdentifier": "t_enum$_ProposalState_$343", "typeString": "enum SimpleDAO.ProposalState"}}, "src": "5316:41:4", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"hexValue": "50726f706f73616c206e6f7420737563636565646564", "id": 750, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "5359:24:4", "typeDescriptions": {"typeIdentifier": "t_stringliteral_23814462f936441e6f2a8d302f86d63fff227edfc26d4486e70df7415b985a0c", "typeString": "literal_string \"Proposal not succeeded\""}, "value": "Proposal not succeeded"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_23814462f936441e6f2a8d302f86d63fff227edfc26d4486e70df7415b985a0c", "typeString": "literal_string \"Proposal not succeeded\""}], "id": 744, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [-18, -18], "referencedDeclaration": -18, "src": "5308:7:4", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 751, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5308:76:4", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 752, "nodeType": "ExpressionStatement", "src": "5308:76:4"}, {"expression": {"arguments": [{"id": 756, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "!", "prefix": true, "src": "5402:18:4", "subExpression": {"expression": {"id": 754, "name": "proposal", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 735, "src": "5403:8:4", "typeDescriptions": {"typeIdentifier": "t_struct$_Proposal_$374_storage_ptr", "typeString": "struct SimpleDAO.Proposal storage pointer"}}, "id": 755, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "5412:8:4", "memberName": "executed", "nodeType": "MemberAccess", "referencedDeclaration": 373, "src": "5403:17:4", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"hexValue": "50726f706f73616c20616c7265616479206578656375746564", "id": 757, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "5422:27:4", "typeDescriptions": {"typeIdentifier": "t_stringliteral_2707e21e9aae26cb9edbd76c2d262b3de919daea0bd16a10af4500ba819ed5fd", "typeString": "literal_string \"Proposal already executed\""}, "value": "Proposal already executed"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_2707e21e9aae26cb9edbd76c2d262b3de919daea0bd16a10af4500ba819ed5fd", "typeString": "literal_string \"Proposal already executed\""}], "id": 753, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [-18, -18], "referencedDeclaration": -18, "src": "5394:7:4", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 758, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5394:56:4", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 759, "nodeType": "ExpressionStatement", "src": "5394:56:4"}, {"expression": {"id": 764, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"expression": {"id": 760, "name": "proposal", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 735, "src": "5469:8:4", "typeDescriptions": {"typeIdentifier": "t_struct$_Proposal_$374_storage_ptr", "typeString": "struct SimpleDAO.Proposal storage pointer"}}, "id": 762, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "memberLocation": "5478:8:4", "memberName": "executed", "nodeType": "MemberAccess", "referencedDeclaration": 373, "src": "5469:17:4", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"hexValue": "74727565", "id": 763, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "5489:4:4", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "true"}, "src": "5469:24:4", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 765, "nodeType": "ExpressionStatement", "src": "5469:24:4"}, {"expression": {"id": 771, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"expression": {"id": 766, "name": "proposal", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 735, "src": "5503:8:4", "typeDescriptions": {"typeIdentifier": "t_struct$_Proposal_$374_storage_ptr", "typeString": "struct SimpleDAO.Proposal storage pointer"}}, "id": 768, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "memberLocation": "5512:5:4", "memberName": "state", "nodeType": "MemberAccess", "referencedDeclaration": 371, "src": "5503:14:4", "typeDescriptions": {"typeIdentifier": "t_enum$_ProposalState_$343", "typeString": "enum SimpleDAO.ProposalState"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"expression": {"id": 769, "name": "ProposalState", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 343, "src": "5520:13:4", "typeDescriptions": {"typeIdentifier": "t_type$_t_enum$_ProposalState_$343_$", "typeString": "type(enum SimpleDAO.ProposalState)"}}, "id": 770, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "5534:8:4", "memberName": "EXECUTED", "nodeType": "MemberAccess", "referencedDeclaration": 342, "src": "5520:22:4", "typeDescriptions": {"typeIdentifier": "t_enum$_ProposalState_$343", "typeString": "enum SimpleDAO.ProposalState"}}, "src": "5503:39:4", "typeDescriptions": {"typeIdentifier": "t_enum$_ProposalState_$343", "typeString": "enum SimpleDAO.ProposalState"}}, "id": 772, "nodeType": "ExpressionStatement", "src": "5503:39:4"}, {"condition": {"commonType": {"typeIdentifier": "t_enum$_ProposalType_$347", "typeString": "enum SimpleDAO.ProposalType"}, "id": 777, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"expression": {"id": 773, "name": "proposal", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 735, "src": "5565:8:4", "typeDescriptions": {"typeIdentifier": "t_struct$_Proposal_$374_storage_ptr", "typeString": "struct SimpleDAO.Proposal storage pointer"}}, "id": 774, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "5574:12:4", "memberName": "proposalType", "nodeType": "MemberAccess", "referencedDeclaration": 356, "src": "5565:21:4", "typeDescriptions": {"typeIdentifier": "t_enum$_ProposalType_$347", "typeString": "enum SimpleDAO.ProposalType"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"expression": {"id": 775, "name": "ProposalType", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 347, "src": "5590:12:4", "typeDescriptions": {"typeIdentifier": "t_type$_t_enum$_ProposalType_$347_$", "typeString": "type(enum SimpleDAO.ProposalType)"}}, "id": 776, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "5603:7:4", "memberName": "FUNDING", "nodeType": "MemberAccess", "referencedDeclaration": 345, "src": "5590:20:4", "typeDescriptions": {"typeIdentifier": "t_enum$_ProposalType_$347", "typeString": "enum SimpleDAO.ProposalType"}}, "src": "5565:45:4", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 829, "nodeType": "IfStatement", "src": "5561:569:4", "trueBody": {"id": 828, "nodeType": "Block", "src": "5612:518:4", "statements": [{"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 782, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"expression": {"id": 779, "name": "proposal", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 735, "src": "5634:8:4", "typeDescriptions": {"typeIdentifier": "t_struct$_Proposal_$374_storage_ptr", "typeString": "struct SimpleDAO.Proposal storage pointer"}}, "id": 780, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "5643:15:4", "memberName": "requestedAmount", "nodeType": "MemberAccess", "referencedDeclaration": 358, "src": "5634:24:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<=", "rightExpression": {"id": 781, "name": "treasuryBalance", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 408, "src": "5662:15:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "5634:43:4", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"hexValue": "496e73756666696369656e742074726561737572792066756e6473", "id": 783, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "5679:29:4", "typeDescriptions": {"typeIdentifier": "t_stringliteral_f232c0a8c3351530d23997d88dc4d375f139466a0ca2f00460395dfa071aeeaf", "typeString": "literal_string \"Insufficient treasury funds\""}, "value": "Insufficient treasury funds"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_f232c0a8c3351530d23997d88dc4d375f139466a0ca2f00460395dfa071aeeaf", "typeString": "literal_string \"Insufficient treasury funds\""}], "id": 778, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [-18, -18], "referencedDeclaration": -18, "src": "5626:7:4", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 784, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5626:83:4", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 785, "nodeType": "ExpressionStatement", "src": "5626:83:4"}, {"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_address", "typeString": "address"}, "id": 793, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"expression": {"id": 787, "name": "proposal", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 735, "src": "5731:8:4", "typeDescriptions": {"typeIdentifier": "t_struct$_Proposal_$374_storage_ptr", "typeString": "struct SimpleDAO.Proposal storage pointer"}}, "id": 788, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "5740:11:4", "memberName": "beneficiary", "nodeType": "MemberAccess", "referencedDeclaration": 360, "src": "5731:20:4", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "BinaryOperation", "operator": "!=", "rightExpression": {"arguments": [{"hexValue": "30", "id": 791, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "5763:1:4", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}], "id": 790, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "5755:7:4", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 789, "name": "address", "nodeType": "ElementaryTypeName", "src": "5755:7:4", "typeDescriptions": {}}}, "id": 792, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5755:10:4", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "src": "5731:34:4", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"hexValue": "496e76616c69642062656e6566696369617279", "id": 794, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "5767:21:4", "typeDescriptions": {"typeIdentifier": "t_stringliteral_86f5acfc12d2804bcf816c0b4c171086bf03352ff286fda75ac8ea27fcfb10a6", "typeString": "literal_string \"Invalid beneficiary\""}, "value": "Invalid beneficiary"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_86f5acfc12d2804bcf816c0b4c171086bf03352ff286fda75ac8ea27fcfb10a6", "typeString": "literal_string \"Invalid beneficiary\""}], "id": 786, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [-18, -18], "referencedDeclaration": -18, "src": "5723:7:4", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 795, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5723:66:4", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 796, "nodeType": "ExpressionStatement", "src": "5723:66:4"}, {"expression": {"id": 800, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 797, "name": "treasuryBalance", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 408, "src": "5816:15:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "-=", "rightHandSide": {"expression": {"id": 798, "name": "proposal", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 735, "src": "5835:8:4", "typeDescriptions": {"typeIdentifier": "t_struct$_Proposal_$374_storage_ptr", "typeString": "struct SimpleDAO.Proposal storage pointer"}}, "id": 799, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "5844:15:4", "memberName": "requestedAmount", "nodeType": "MemberAccess", "referencedDeclaration": 358, "src": "5835:24:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "5816:43:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 801, "nodeType": "ExpressionStatement", "src": "5816:43:4"}, {"assignments": [803, null], "declarations": [{"constant": false, "id": 803, "mutability": "mutable", "name": "success", "nameLocation": "5892:7:4", "nodeType": "VariableDeclaration", "scope": 828, "src": "5887:12:4", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 802, "name": "bool", "nodeType": "ElementaryTypeName", "src": "5887:4:4", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}, null], "id": 815, "initialValue": {"arguments": [{"hexValue": "", "id": 813, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "5973:2:4", "typeDescriptions": {"typeIdentifier": "t_stringliteral_c5d2460186f7233c927e7db2dcc703c0e500b653ca82273b7bfad8045d85a470", "typeString": "literal_string \"\""}, "value": ""}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_c5d2460186f7233c927e7db2dcc703c0e500b653ca82273b7bfad8045d85a470", "typeString": "literal_string \"\""}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_c5d2460186f7233c927e7db2dcc703c0e500b653ca82273b7bfad8045d85a470", "typeString": "literal_string \"\""}], "expression": {"arguments": [{"expression": {"id": 806, "name": "proposal", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 735, "src": "5913:8:4", "typeDescriptions": {"typeIdentifier": "t_struct$_Proposal_$374_storage_ptr", "typeString": "struct SimpleDAO.Proposal storage pointer"}}, "id": 807, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "5922:11:4", "memberName": "beneficiary", "nodeType": "MemberAccess", "referencedDeclaration": 360, "src": "5913:20:4", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 805, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "5905:8:4", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_payable_$", "typeString": "type(address payable)"}, "typeName": {"id": 804, "name": "address", "nodeType": "ElementaryTypeName", "src": "5905:8:4", "stateMutability": "payable", "typeDescriptions": {}}}, "id": 808, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5905:29:4", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address_payable", "typeString": "address payable"}}, "id": 809, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "5935:4:4", "memberName": "call", "nodeType": "MemberAccess", "src": "5905:34:4", "typeDescriptions": {"typeIdentifier": "t_function_barecall_payable$_t_bytes_memory_ptr_$returns$_t_bool_$_t_bytes_memory_ptr_$", "typeString": "function (bytes memory) payable returns (bool,bytes memory)"}}, "id": 812, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "names": ["value"], "nodeType": "FunctionCallOptions", "options": [{"expression": {"id": 810, "name": "proposal", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 735, "src": "5947:8:4", "typeDescriptions": {"typeIdentifier": "t_struct$_Proposal_$374_storage_ptr", "typeString": "struct SimpleDAO.Proposal storage pointer"}}, "id": 811, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "5956:15:4", "memberName": "requestedAmount", "nodeType": "MemberAccess", "referencedDeclaration": 358, "src": "5947:24:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "src": "5905:67:4", "typeDescriptions": {"typeIdentifier": "t_function_barecall_payable$_t_bytes_memory_ptr_$returns$_t_bool_$_t_bytes_memory_ptr_$value", "typeString": "function (bytes memory) payable returns (bool,bytes memory)"}}, "id": 814, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5905:71:4", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$_t_bool_$_t_bytes_memory_ptr_$", "typeString": "tuple(bool,bytes memory)"}}, "nodeType": "VariableDeclarationStatement", "src": "5886:90:4"}, {"expression": {"arguments": [{"id": 817, "name": "success", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 803, "src": "5998:7:4", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"hexValue": "5472616e73666572206661696c6564", "id": 818, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "6007:17:4", "typeDescriptions": {"typeIdentifier": "t_stringliteral_25adaa6d082ce15f901e0d8a3d393e7462ef9edf2e6bc8321fa14d1615b6fc51", "typeString": "literal_string \"Transfer failed\""}, "value": "Transfer failed"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_25adaa6d082ce15f901e0d8a3d393e7462ef9edf2e6bc8321fa14d1615b6fc51", "typeString": "literal_string \"Transfer failed\""}], "id": 816, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [-18, -18], "referencedDeclaration": -18, "src": "5990:7:4", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 819, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5990:35:4", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 820, "nodeType": "ExpressionStatement", "src": "5990:35:4"}, {"eventCall": {"arguments": [{"expression": {"id": 822, "name": "proposal", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 735, "src": "6072:8:4", "typeDescriptions": {"typeIdentifier": "t_struct$_Proposal_$374_storage_ptr", "typeString": "struct SimpleDAO.Proposal storage pointer"}}, "id": 823, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "6081:11:4", "memberName": "beneficiary", "nodeType": "MemberAccess", "referencedDeclaration": 360, "src": "6072:20:4", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"expression": {"id": 824, "name": "proposal", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 735, "src": "6094:8:4", "typeDescriptions": {"typeIdentifier": "t_struct$_Proposal_$374_storage_ptr", "typeString": "struct SimpleDAO.Proposal storage pointer"}}, "id": 825, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "6103:15:4", "memberName": "requestedAmount", "nodeType": "MemberAccess", "referencedDeclaration": 358, "src": "6094:24:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 821, "name": "FundsWithdrawn", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 449, "src": "6057:14:4", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_address_$_t_uint256_$returns$__$", "typeString": "function (address,uint256)"}}, "id": 826, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6057:62:4", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 827, "nodeType": "EmitStatement", "src": "6052:67:4"}]}}, {"eventCall": {"arguments": [{"id": 831, "name": "proposalId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 728, "src": "6170:10:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 830, "name": "ProposalExecuted", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 437, "src": "6153:16:4", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_uint256_$returns$__$", "typeString": "function (uint256)"}}, "id": 832, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6153:28:4", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 833, "nodeType": "EmitStatement", "src": "6148:33:4"}]}, "documentation": {"id": 726, "nodeType": "StructuredDocumentation", "src": "5081:36:4", "text": " @dev 执行提案"}, "functionSelector": "0d61b519", "id": 835, "implemented": true, "kind": "function", "modifiers": [{"id": 731, "kind": "modifierInvocation", "modifierName": {"id": 730, "name": "nonReentrant", "nameLocations": ["5176:12:4"], "nodeType": "IdentifierPath", "referencedDeclaration": 288, "src": "5176:12:4"}, "nodeType": "ModifierInvocation", "src": "5176:12:4"}], "name": "executeProposal", "nameLocation": "5131:15:4", "nodeType": "FunctionDefinition", "parameters": {"id": 729, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 728, "mutability": "mutable", "name": "proposalId", "nameLocation": "5155:10:4", "nodeType": "VariableDeclaration", "scope": 835, "src": "5147:18:4", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 727, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "5147:7:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "5146:20:4"}, "returnParameters": {"id": 732, "nodeType": "ParameterList", "parameters": [], "src": "5189:0:4"}, "scope": 1039, "src": "5122:1066:4", "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}, {"body": {"id": 869, "nodeType": "Block", "src": "6310:226:4", "statements": [{"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 845, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"expression": {"id": 842, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "6328:3:4", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 843, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "6332:5:4", "memberName": "value", "nodeType": "MemberAccess", "src": "6328:9:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": ">", "rightExpression": {"hexValue": "30", "id": 844, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "6340:1:4", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "src": "6328:13:4", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"hexValue": "4d757374206465706f73697420736f6d6520455448", "id": 846, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "6343:23:4", "typeDescriptions": {"typeIdentifier": "t_stringliteral_97f5c58601dae1421ae4b5d90a2b8902eec584d6e90c2d87f4b4e308cb18aaf7", "typeString": "literal_string \"Must deposit some ETH\""}, "value": "Must deposit some ETH"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_97f5c58601dae1421ae4b5d90a2b8902eec584d6e90c2d87f4b4e308cb18aaf7", "typeString": "literal_string \"Must deposit some ETH\""}], "id": 841, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [-18, -18], "referencedDeclaration": -18, "src": "6320:7:4", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 847, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6320:47:4", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 848, "nodeType": "ExpressionStatement", "src": "6320:47:4"}, {"expression": {"id": 852, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 849, "name": "treasuryBalance", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 408, "src": "6386:15:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "+=", "rightHandSide": {"expression": {"id": 850, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "6405:3:4", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 851, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "6409:5:4", "memberName": "value", "nodeType": "MemberAccess", "src": "6405:9:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "6386:28:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 853, "nodeType": "ExpressionStatement", "src": "6386:28:4"}, {"expression": {"id": 860, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"baseExpression": {"id": 854, "name": "memberContributions", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 412, "src": "6424:19:4", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 857, "indexExpression": {"expression": {"id": 855, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "6444:3:4", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 856, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "6448:6:4", "memberName": "sender", "nodeType": "MemberAccess", "src": "6444:10:4", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "6424:31:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "+=", "rightHandSide": {"expression": {"id": 858, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "6459:3:4", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 859, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "6463:5:4", "memberName": "value", "nodeType": "MemberAccess", "src": "6459:9:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "6424:44:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 861, "nodeType": "ExpressionStatement", "src": "6424:44:4"}, {"eventCall": {"arguments": [{"expression": {"id": 863, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "6507:3:4", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 864, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "6511:6:4", "memberName": "sender", "nodeType": "MemberAccess", "src": "6507:10:4", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"expression": {"id": 865, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "6519:3:4", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 866, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "6523:5:4", "memberName": "value", "nodeType": "MemberAccess", "src": "6519:9:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 862, "name": "FundsDeposited", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 443, "src": "6492:14:4", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_address_$_t_uint256_$returns$__$", "typeString": "function (address,uint256)"}}, "id": 867, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6492:37:4", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 868, "nodeType": "EmitStatement", "src": "6487:42:4"}]}, "documentation": {"id": 836, "nodeType": "StructuredDocumentation", "src": "6198:53:4", "text": " @dev 向 DAO 资金池存入资金"}, "functionSelector": "e2c41dbc", "id": 870, "implemented": true, "kind": "function", "modifiers": [{"id": 839, "kind": "modifierInvocation", "modifierName": {"id": 838, "name": "nonReentrant", "nameLocations": ["6297:12:4"], "nodeType": "IdentifierPath", "referencedDeclaration": 288, "src": "6297:12:4"}, "nodeType": "ModifierInvocation", "src": "6297:12:4"}], "name": "depositFunds", "nameLocation": "6265:12:4", "nodeType": "FunctionDefinition", "parameters": {"id": 837, "nodeType": "ParameterList", "parameters": [], "src": "6277:2:4"}, "returnParameters": {"id": 840, "nodeType": "ParameterList", "parameters": [], "src": "6310:0:4"}, "scope": 1039, "src": "6256:280:4", "stateMutability": "payable", "virtual": false, "visibility": "external"}, {"body": {"id": 889, "nodeType": "Block", "src": "6656:91:4", "statements": [{"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 881, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 879, "name": "newPeriod", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 873, "src": "6674:9:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": ">", "rightExpression": {"hexValue": "30", "id": 880, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "6686:1:4", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "src": "6674:13:4", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"hexValue": "496e76616c696420706572696f64", "id": 882, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "6689:16:4", "typeDescriptions": {"typeIdentifier": "t_stringliteral_aa554920682da6001173f4ad2526282c74ee4725c3e9435e042c1df8d004f00d", "typeString": "literal_string \"Invalid period\""}, "value": "Invalid period"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_aa554920682da6001173f4ad2526282c74ee4725c3e9435e042c1df8d004f00d", "typeString": "literal_string \"Invalid period\""}], "id": 878, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [-18, -18], "referencedDeclaration": -18, "src": "6666:7:4", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 883, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6666:40:4", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 884, "nodeType": "ExpressionStatement", "src": "6666:40:4"}, {"expression": {"id": 887, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 885, "name": "votingPeriod", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 396, "src": "6716:12:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 886, "name": "newPeriod", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 873, "src": "6731:9:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "6716:24:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 888, "nodeType": "ExpressionStatement", "src": "6716:24:4"}]}, "documentation": {"id": 871, "nodeType": "StructuredDocumentation", "src": "6546:42:4", "text": " @dev 设置投票期限"}, "functionSelector": "ea0217cf", "id": 890, "implemented": true, "kind": "function", "modifiers": [{"id": 876, "kind": "modifierInvocation", "modifierName": {"id": 875, "name": "only<PERSON><PERSON>er", "nameLocations": ["6646:9:4"], "nodeType": "IdentifierPath", "referencedDeclaration": 58, "src": "6646:9:4"}, "nodeType": "ModifierInvocation", "src": "6646:9:4"}], "name": "setVotingPeriod", "nameLocation": "6602:15:4", "nodeType": "FunctionDefinition", "parameters": {"id": 874, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 873, "mutability": "mutable", "name": "newPeriod", "nameLocation": "6626:9:4", "nodeType": "VariableDeclaration", "scope": 890, "src": "6618:17:4", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 872, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "6618:7:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "6617:19:4"}, "returnParameters": {"id": 877, "nodeType": "ParameterList", "parameters": [], "src": "6656:0:4"}, "scope": 1039, "src": "6593:154:4", "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}, {"body": {"id": 902, "nodeType": "Block", "src": "6875:49:4", "statements": [{"expression": {"id": 900, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 898, "name": "proposalThreshold", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 403, "src": "6885:17:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 899, "name": "newThreshold", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 893, "src": "6905:12:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "6885:32:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 901, "nodeType": "ExpressionStatement", "src": "6885:32:4"}]}, "documentation": {"id": 891, "nodeType": "StructuredDocumentation", "src": "6757:42:4", "text": " @dev 设置提案阈值"}, "functionSelector": "ece40cc1", "id": 903, "implemented": true, "kind": "function", "modifiers": [{"id": 896, "kind": "modifierInvocation", "modifierName": {"id": 895, "name": "only<PERSON><PERSON>er", "nameLocations": ["6865:9:4"], "nodeType": "IdentifierPath", "referencedDeclaration": 58, "src": "6865:9:4"}, "nodeType": "ModifierInvocation", "src": "6865:9:4"}], "name": "setProposalThreshold", "nameLocation": "6813:20:4", "nodeType": "FunctionDefinition", "parameters": {"id": 894, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 893, "mutability": "mutable", "name": "newThreshold", "nameLocation": "6842:12:4", "nodeType": "VariableDeclaration", "scope": 903, "src": "6834:20:4", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 892, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "6834:7:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "6833:22:4"}, "returnParameters": {"id": 897, "nodeType": "ParameterList", "parameters": [], "src": "6875:0:4"}, "scope": 1039, "src": "6804:120:4", "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}, {"body": {"id": 922, "nodeType": "Block", "src": "7061:110:4", "statements": [{"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 914, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 912, "name": "newPercentage", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 906, "src": "7079:13:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<=", "rightExpression": {"hexValue": "313030", "id": 913, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "7096:3:4", "typeDescriptions": {"typeIdentifier": "t_rational_100_by_1", "typeString": "int_const 100"}, "value": "100"}, "src": "7079:20:4", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"hexValue": "496e76616c69642070657263656e74616765", "id": 915, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "7101:20:4", "typeDescriptions": {"typeIdentifier": "t_stringliteral_3aa060f1dfc69ce7f57887a6e23d7fbceead8042b984953c572b9c8fa5af8f04", "typeString": "literal_string \"Invalid percentage\""}, "value": "Invalid percentage"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_3aa060f1dfc69ce7f57887a6e23d7fbceead8042b984953c572b9c8fa5af8f04", "typeString": "literal_string \"Invalid percentage\""}], "id": 911, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [-18, -18], "referencedDeclaration": -18, "src": "7071:7:4", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 916, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "7071:51:4", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 917, "nodeType": "ExpressionStatement", "src": "7071:51:4"}, {"expression": {"id": 920, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 918, "name": "quorumPercentage", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 406, "src": "7132:16:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 919, "name": "newPercentage", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 906, "src": "7151:13:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "7132:32:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 921, "nodeType": "ExpressionStatement", "src": "7132:32:4"}]}, "documentation": {"id": 904, "nodeType": "StructuredDocumentation", "src": "6934:51:4", "text": " @dev 设置法定人数百分比"}, "functionSelector": "32f6a1dc", "id": 923, "implemented": true, "kind": "function", "modifiers": [{"id": 909, "kind": "modifierInvocation", "modifierName": {"id": 908, "name": "only<PERSON><PERSON>er", "nameLocations": ["7051:9:4"], "nodeType": "IdentifierPath", "referencedDeclaration": 58, "src": "7051:9:4"}, "nodeType": "ModifierInvocation", "src": "7051:9:4"}], "name": "setQuorumPercentage", "nameLocation": "6999:19:4", "nodeType": "FunctionDefinition", "parameters": {"id": 907, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 906, "mutability": "mutable", "name": "newPercentage", "nameLocation": "7027:13:4", "nodeType": "VariableDeclaration", "scope": 923, "src": "7019:21:4", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 905, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "7019:7:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "7018:23:4"}, "returnParameters": {"id": 910, "nodeType": "ParameterList", "parameters": [], "src": "7061:0:4"}, "scope": 1039, "src": "6990:181:4", "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}, {"body": {"id": 988, "nodeType": "Block", "src": "7636:477:4", "statements": [{"assignments": [957], "declarations": [{"constant": false, "id": 957, "mutability": "mutable", "name": "proposal", "nameLocation": "7663:8:4", "nodeType": "VariableDeclaration", "scope": 988, "src": "7646:25:4", "stateVariable": false, "storageLocation": "storage", "typeDescriptions": {"typeIdentifier": "t_struct$_Proposal_$374_storage_ptr", "typeString": "struct SimpleDAO.Proposal"}, "typeName": {"id": 956, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 955, "name": "Proposal", "nameLocations": ["7646:8:4"], "nodeType": "IdentifierPath", "referencedDeclaration": 374, "src": "7646:8:4"}, "referencedDeclaration": 374, "src": "7646:8:4", "typeDescriptions": {"typeIdentifier": "t_struct$_Proposal_$374_storage_ptr", "typeString": "struct SimpleDAO.Proposal"}}, "visibility": "internal"}], "id": 961, "initialValue": {"baseExpression": {"id": 958, "name": "proposals", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 379, "src": "7674:9:4", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_uint256_$_t_struct$_Proposal_$374_storage_$", "typeString": "mapping(uint256 => struct SimpleDAO.Proposal storage ref)"}}, "id": 960, "indexExpression": {"id": 959, "name": "proposalId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 926, "src": "7684:10:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "7674:21:4", "typeDescriptions": {"typeIdentifier": "t_struct$_Proposal_$374_storage", "typeString": "struct SimpleDAO.Proposal storage ref"}}, "nodeType": "VariableDeclarationStatement", "src": "7646:49:4"}, {"expression": {"components": [{"expression": {"id": 962, "name": "proposal", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 957, "src": "7726:8:4", "typeDescriptions": {"typeIdentifier": "t_struct$_Proposal_$374_storage_ptr", "typeString": "struct SimpleDAO.Proposal storage pointer"}}, "id": 963, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "7735:2:4", "memberName": "id", "nodeType": "MemberAccess", "referencedDeclaration": 349, "src": "7726:11:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"expression": {"id": 964, "name": "proposal", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 957, "src": "7751:8:4", "typeDescriptions": {"typeIdentifier": "t_struct$_Proposal_$374_storage_ptr", "typeString": "struct SimpleDAO.Proposal storage pointer"}}, "id": 965, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "7760:8:4", "memberName": "proposer", "nodeType": "MemberAccess", "referencedDeclaration": 351, "src": "7751:17:4", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"expression": {"id": 966, "name": "proposal", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 957, "src": "7782:8:4", "typeDescriptions": {"typeIdentifier": "t_struct$_Proposal_$374_storage_ptr", "typeString": "struct SimpleDAO.Proposal storage pointer"}}, "id": 967, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "7791:11:4", "memberName": "description", "nodeType": "MemberAccess", "referencedDeclaration": 353, "src": "7782:20:4", "typeDescriptions": {"typeIdentifier": "t_string_storage", "typeString": "string storage ref"}}, {"expression": {"id": 968, "name": "proposal", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 957, "src": "7816:8:4", "typeDescriptions": {"typeIdentifier": "t_struct$_Proposal_$374_storage_ptr", "typeString": "struct SimpleDAO.Proposal storage pointer"}}, "id": 969, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "7825:12:4", "memberName": "proposalType", "nodeType": "MemberAccess", "referencedDeclaration": 356, "src": "7816:21:4", "typeDescriptions": {"typeIdentifier": "t_enum$_ProposalType_$347", "typeString": "enum SimpleDAO.ProposalType"}}, {"expression": {"id": 970, "name": "proposal", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 957, "src": "7851:8:4", "typeDescriptions": {"typeIdentifier": "t_struct$_Proposal_$374_storage_ptr", "typeString": "struct SimpleDAO.Proposal storage pointer"}}, "id": 971, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "7860:15:4", "memberName": "requestedAmount", "nodeType": "MemberAccess", "referencedDeclaration": 358, "src": "7851:24:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"expression": {"id": 972, "name": "proposal", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 957, "src": "7889:8:4", "typeDescriptions": {"typeIdentifier": "t_struct$_Proposal_$374_storage_ptr", "typeString": "struct SimpleDAO.Proposal storage pointer"}}, "id": 973, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "7898:11:4", "memberName": "beneficiary", "nodeType": "MemberAccess", "referencedDeclaration": 360, "src": "7889:20:4", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"expression": {"id": 974, "name": "proposal", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 957, "src": "7923:8:4", "typeDescriptions": {"typeIdentifier": "t_struct$_Proposal_$374_storage_ptr", "typeString": "struct SimpleDAO.Proposal storage pointer"}}, "id": 975, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "7932:8:4", "memberName": "forVotes", "nodeType": "MemberAccess", "referencedDeclaration": 362, "src": "7923:17:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"expression": {"id": 976, "name": "proposal", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 957, "src": "7954:8:4", "typeDescriptions": {"typeIdentifier": "t_struct$_Proposal_$374_storage_ptr", "typeString": "struct SimpleDAO.Proposal storage pointer"}}, "id": 977, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "7963:12:4", "memberName": "againstVotes", "nodeType": "MemberAccess", "referencedDeclaration": 364, "src": "7954:21:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"expression": {"id": 978, "name": "proposal", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 957, "src": "7989:8:4", "typeDescriptions": {"typeIdentifier": "t_struct$_Proposal_$374_storage_ptr", "typeString": "struct SimpleDAO.Proposal storage pointer"}}, "id": 979, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "7998:9:4", "memberName": "startTime", "nodeType": "MemberAccess", "referencedDeclaration": 366, "src": "7989:18:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"expression": {"id": 980, "name": "proposal", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 957, "src": "8021:8:4", "typeDescriptions": {"typeIdentifier": "t_struct$_Proposal_$374_storage_ptr", "typeString": "struct SimpleDAO.Proposal storage pointer"}}, "id": 981, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "8030:7:4", "memberName": "endTime", "nodeType": "MemberAccess", "referencedDeclaration": 368, "src": "8021:16:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"expression": {"id": 982, "name": "proposal", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 957, "src": "8051:8:4", "typeDescriptions": {"typeIdentifier": "t_struct$_Proposal_$374_storage_ptr", "typeString": "struct SimpleDAO.Proposal storage pointer"}}, "id": 983, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "8060:5:4", "memberName": "state", "nodeType": "MemberAccess", "referencedDeclaration": 371, "src": "8051:14:4", "typeDescriptions": {"typeIdentifier": "t_enum$_ProposalState_$343", "typeString": "enum SimpleDAO.ProposalState"}}, {"expression": {"id": 984, "name": "proposal", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 957, "src": "8079:8:4", "typeDescriptions": {"typeIdentifier": "t_struct$_Proposal_$374_storage_ptr", "typeString": "struct SimpleDAO.Proposal storage pointer"}}, "id": 985, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "8088:8:4", "memberName": "executed", "nodeType": "MemberAccess", "referencedDeclaration": 373, "src": "8079:17:4", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}], "id": 986, "isConstant": false, "isInlineArray": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "TupleExpression", "src": "7712:394:4", "typeDescriptions": {"typeIdentifier": "t_tuple$_t_uint256_$_t_address_$_t_string_storage_$_t_enum$_ProposalType_$347_$_t_uint256_$_t_address_$_t_uint256_$_t_uint256_$_t_uint256_$_t_uint256_$_t_enum$_ProposalState_$343_$_t_bool_$", "typeString": "tuple(uint256,address,string storage ref,enum SimpleDAO.ProposalType,uint256,address,uint256,uint256,uint256,uint256,enum SimpleDAO.ProposalState,bool)"}}, "functionReturnParameters": 954, "id": 987, "nodeType": "Return", "src": "7705:401:4"}]}, "documentation": {"id": 924, "nodeType": "StructuredDocumentation", "src": "7181:42:4", "text": " @dev 获取提案信息"}, "functionSelector": "c7f758a8", "id": 989, "implemented": true, "kind": "function", "modifiers": [], "name": "getProposal", "nameLocation": "7237:11:4", "nodeType": "FunctionDefinition", "parameters": {"id": 927, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 926, "mutability": "mutable", "name": "proposalId", "nameLocation": "7257:10:4", "nodeType": "VariableDeclaration", "scope": 989, "src": "7249:18:4", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 925, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "7249:7:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "7248:20:4"}, "returnParameters": {"id": 954, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 929, "mutability": "mutable", "name": "id", "nameLocation": "7309:2:4", "nodeType": "VariableDeclaration", "scope": 989, "src": "7301:10:4", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 928, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "7301:7:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 931, "mutability": "mutable", "name": "proposer", "nameLocation": "7329:8:4", "nodeType": "VariableDeclaration", "scope": 989, "src": "7321:16:4", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 930, "name": "address", "nodeType": "ElementaryTypeName", "src": "7321:7:4", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 933, "mutability": "mutable", "name": "description", "nameLocation": "7361:11:4", "nodeType": "VariableDeclaration", "scope": 989, "src": "7347:25:4", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 932, "name": "string", "nodeType": "ElementaryTypeName", "src": "7347:6:4", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 936, "mutability": "mutable", "name": "proposalType", "nameLocation": "7395:12:4", "nodeType": "VariableDeclaration", "scope": 989, "src": "7382:25:4", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_enum$_ProposalType_$347", "typeString": "enum SimpleDAO.ProposalType"}, "typeName": {"id": 935, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 934, "name": "ProposalType", "nameLocations": ["7382:12:4"], "nodeType": "IdentifierPath", "referencedDeclaration": 347, "src": "7382:12:4"}, "referencedDeclaration": 347, "src": "7382:12:4", "typeDescriptions": {"typeIdentifier": "t_enum$_ProposalType_$347", "typeString": "enum SimpleDAO.ProposalType"}}, "visibility": "internal"}, {"constant": false, "id": 938, "mutability": "mutable", "name": "requestedAmount", "nameLocation": "7425:15:4", "nodeType": "VariableDeclaration", "scope": 989, "src": "7417:23:4", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 937, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "7417:7:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 940, "mutability": "mutable", "name": "beneficiary", "nameLocation": "7458:11:4", "nodeType": "VariableDeclaration", "scope": 989, "src": "7450:19:4", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 939, "name": "address", "nodeType": "ElementaryTypeName", "src": "7450:7:4", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 942, "mutability": "mutable", "name": "forVotes", "nameLocation": "7487:8:4", "nodeType": "VariableDeclaration", "scope": 989, "src": "7479:16:4", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 941, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "7479:7:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 944, "mutability": "mutable", "name": "againstVotes", "nameLocation": "7513:12:4", "nodeType": "VariableDeclaration", "scope": 989, "src": "7505:20:4", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 943, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "7505:7:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 946, "mutability": "mutable", "name": "startTime", "nameLocation": "7543:9:4", "nodeType": "VariableDeclaration", "scope": 989, "src": "7535:17:4", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 945, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "7535:7:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 948, "mutability": "mutable", "name": "endTime", "nameLocation": "7570:7:4", "nodeType": "VariableDeclaration", "scope": 989, "src": "7562:15:4", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 947, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "7562:7:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 951, "mutability": "mutable", "name": "state", "nameLocation": "7601:5:4", "nodeType": "VariableDeclaration", "scope": 989, "src": "7587:19:4", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_enum$_ProposalState_$343", "typeString": "enum SimpleDAO.ProposalState"}, "typeName": {"id": 950, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 949, "name": "ProposalState", "nameLocations": ["7587:13:4"], "nodeType": "IdentifierPath", "referencedDeclaration": 343, "src": "7587:13:4"}, "referencedDeclaration": 343, "src": "7587:13:4", "typeDescriptions": {"typeIdentifier": "t_enum$_ProposalState_$343", "typeString": "enum SimpleDAO.ProposalState"}}, "visibility": "internal"}, {"constant": false, "id": 953, "mutability": "mutable", "name": "executed", "nameLocation": "7621:8:4", "nodeType": "VariableDeclaration", "scope": 989, "src": "7616:13:4", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 952, "name": "bool", "nodeType": "ElementaryTypeName", "src": "7616:4:4", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "7291:344:4"}, "scope": 1039, "src": "7228:885:4", "stateMutability": "view", "virtual": false, "visibility": "external"}, {"body": {"id": 1013, "nodeType": "Block", "src": "8279:82:4", "statements": [{"expression": {"components": [{"baseExpression": {"baseExpression": {"id": 1001, "name": "hasVoted", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 385, "src": "8297:8:4", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_uint256_$_t_mapping$_t_address_$_t_bool_$_$", "typeString": "mapping(uint256 => mapping(address => bool))"}}, "id": 1003, "indexExpression": {"id": 1002, "name": "proposalId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 992, "src": "8306:10:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "8297:20:4", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_bool_$", "typeString": "mapping(address => bool)"}}, "id": 1005, "indexExpression": {"id": 1004, "name": "user", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 994, "src": "8318:4:4", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "8297:26:4", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"baseExpression": {"baseExpression": {"id": 1006, "name": "voteChoice", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 391, "src": "8325:10:4", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_uint256_$_t_mapping$_t_address_$_t_bool_$_$", "typeString": "mapping(uint256 => mapping(address => bool))"}}, "id": 1008, "indexExpression": {"id": 1007, "name": "proposalId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 992, "src": "8336:10:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "8325:22:4", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_bool_$", "typeString": "mapping(address => bool)"}}, "id": 1010, "indexExpression": {"id": 1009, "name": "user", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 994, "src": "8348:4:4", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "8325:28:4", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}], "id": 1011, "isConstant": false, "isInlineArray": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "TupleExpression", "src": "8296:58:4", "typeDescriptions": {"typeIdentifier": "t_tuple$_t_bool_$_t_bool_$", "typeString": "tuple(bool,bool)"}}, "functionReturnParameters": 1000, "id": 1012, "nodeType": "Return", "src": "8289:65:4"}]}, "documentation": {"id": 990, "nodeType": "StructuredDocumentation", "src": "8123:48:4", "text": " @dev 获取用户投票信息"}, "functionSelector": "03c7881a", "id": 1014, "implemented": true, "kind": "function", "modifiers": [], "name": "getUserVote", "nameLocation": "8185:11:4", "nodeType": "FunctionDefinition", "parameters": {"id": 995, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 992, "mutability": "mutable", "name": "proposalId", "nameLocation": "8205:10:4", "nodeType": "VariableDeclaration", "scope": 1014, "src": "8197:18:4", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 991, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "8197:7:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 994, "mutability": "mutable", "name": "user", "nameLocation": "8225:4:4", "nodeType": "VariableDeclaration", "scope": 1014, "src": "8217:12:4", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 993, "name": "address", "nodeType": "ElementaryTypeName", "src": "8217:7:4", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "8196:34:4"}, "returnParameters": {"id": 1000, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 997, "mutability": "mutable", "name": "voted", "nameLocation": "8259:5:4", "nodeType": "VariableDeclaration", "scope": 1014, "src": "8254:10:4", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 996, "name": "bool", "nodeType": "ElementaryTypeName", "src": "8254:4:4", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}, {"constant": false, "id": 999, "mutability": "mutable", "name": "choice", "nameLocation": "8271:6:4", "nodeType": "VariableDeclaration", "scope": 1014, "src": "8266:11:4", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 998, "name": "bool", "nodeType": "ElementaryTypeName", "src": "8266:4:4", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "8253:25:4"}, "scope": 1039, "src": "8176:185:4", "stateMutability": "view", "virtual": false, "visibility": "external"}, {"body": {"id": 1037, "nodeType": "Block", "src": "8416:151:4", "statements": [{"expression": {"id": 1020, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 1017, "name": "treasuryBalance", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 408, "src": "8426:15:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "+=", "rightHandSide": {"expression": {"id": 1018, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "8445:3:4", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 1019, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "8449:5:4", "memberName": "value", "nodeType": "MemberAccess", "src": "8445:9:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "8426:28:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 1021, "nodeType": "ExpressionStatement", "src": "8426:28:4"}, {"expression": {"id": 1028, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"baseExpression": {"id": 1022, "name": "memberContributions", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 412, "src": "8464:19:4", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 1025, "indexExpression": {"expression": {"id": 1023, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "8484:3:4", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 1024, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "8488:6:4", "memberName": "sender", "nodeType": "MemberAccess", "src": "8484:10:4", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "8464:31:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "+=", "rightHandSide": {"expression": {"id": 1026, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "8499:3:4", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 1027, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "8503:5:4", "memberName": "value", "nodeType": "MemberAccess", "src": "8499:9:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "8464:44:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 1029, "nodeType": "ExpressionStatement", "src": "8464:44:4"}, {"eventCall": {"arguments": [{"expression": {"id": 1031, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "8538:3:4", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 1032, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "8542:6:4", "memberName": "sender", "nodeType": "MemberAccess", "src": "8538:10:4", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"expression": {"id": 1033, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "8550:3:4", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 1034, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "8554:5:4", "memberName": "value", "nodeType": "MemberAccess", "src": "8550:9:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 1030, "name": "FundsDeposited", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 443, "src": "8523:14:4", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_address_$_t_uint256_$returns$__$", "typeString": "function (address,uint256)"}}, "id": 1035, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "8523:37:4", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 1036, "nodeType": "EmitStatement", "src": "8518:42:4"}]}, "id": 1038, "implemented": true, "kind": "receive", "modifiers": [], "name": "", "nameLocation": "-1:-1:-1", "nodeType": "FunctionDefinition", "parameters": {"id": 1015, "nodeType": "ParameterList", "parameters": [], "src": "8396:2:4"}, "returnParameters": {"id": 1016, "nodeType": "ParameterList", "parameters": [], "src": "8416:0:4"}, "scope": 1039, "src": "8389:178:4", "stateMutability": "payable", "virtual": false, "visibility": "external"}], "scope": 1040, "src": "411:8158:4", "usedErrors": [13, 18, 269], "usedEvents": [24, 423, 433, 437, 443, 449]}], "src": "32:8538:4"}, "id": 4}}, "contracts": {"@openzeppelin/contracts/access/Ownable.sol": {"Ownable": {"abi": [{"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "OwnableInvalidOwner", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "OwnableUnauthorizedAccount", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "evm": {"bytecode": {"functionDebugData": {}, "generatedSources": [], "linkReferences": {}, "object": "", "opcodes": "", "sourceMap": ""}, "deployedBytecode": {"functionDebugData": {}, "generatedSources": [], "immutableReferences": {}, "linkReferences": {}, "object": "", "opcodes": "", "sourceMap": ""}, "methodIdentifiers": {"owner()": "8da5cb5b", "renounceOwnership()": "715018a6", "transferOwnership(address)": "f2fde38b"}}, "metadata": "{\"compiler\":{\"version\":\"0.8.24+commit.e11b9ed9\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"name\":\"OwnableInvalidOwner\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"OwnableUnauthorizedAccount\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"previousOwner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"OwnershipTransferred\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"owner\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"renounceOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"transferOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"details\":\"Contract module which provides a basic access control mechanism, where there is an account (an owner) that can be granted exclusive access to specific functions. The initial owner is set to the address provided by the deployer. This can later be changed with {transferOwnership}. This module is used through inheritance. It will make available the modifier `onlyOwner`, which can be applied to your functions to restrict their use to the owner.\",\"errors\":{\"OwnableInvalidOwner(address)\":[{\"details\":\"The owner is not a valid owner account. (eg. `address(0)`)\"}],\"OwnableUnauthorizedAccount(address)\":[{\"details\":\"The caller account is not authorized to perform an operation.\"}]},\"kind\":\"dev\",\"methods\":{\"constructor\":{\"details\":\"Initializes the contract setting the address provided by the deployer as the initial owner.\"},\"owner()\":{\"details\":\"Returns the address of the current owner.\"},\"renounceOwnership()\":{\"details\":\"Leaves the contract without owner. It will not be possible to call `onlyOwner` functions. Can only be called by the current owner. NOTE: Renouncing ownership will leave the contract without an owner, thereby disabling any functionality that is only available to the owner.\"},\"transferOwnership(address)\":{\"details\":\"Transfers ownership of the contract to a new account (`newOwner`). Can only be called by the current owner.\"}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"@openzeppelin/contracts/access/Ownable.sol\":\"Ownable\"},\"evmVersion\":\"paris\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[],\"viaIR\":true},\"sources\":{\"@openzeppelin/contracts/access/Ownable.sol\":{\"keccak256\":\"0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8ed324d3920bb545059d66ab97d43e43ee85fd3bd52e03e401f020afb0b120f6\",\"dweb:/ipfs/QmfEckWLmZkDDcoWrkEvMWhms66xwTLff9DDhegYpvHo1a\"]},\"@openzeppelin/contracts/utils/Context.sol\":{\"keccak256\":\"0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12\",\"dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF\"]}},\"version\":1}"}}, "@openzeppelin/contracts/token/ERC20/IERC20.sol": {"IERC20": {"abi": [{"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "spender", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Approval", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Transfer", "type": "event"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}], "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}], "evm": {"bytecode": {"functionDebugData": {}, "generatedSources": [], "linkReferences": {}, "object": "", "opcodes": "", "sourceMap": ""}, "deployedBytecode": {"functionDebugData": {}, "generatedSources": [], "immutableReferences": {}, "linkReferences": {}, "object": "", "opcodes": "", "sourceMap": ""}, "methodIdentifiers": {"allowance(address,address)": "dd62ed3e", "approve(address,uint256)": "095ea7b3", "balanceOf(address)": "70a08231", "totalSupply()": "18160ddd", "transfer(address,uint256)": "a9059cbb", "transferFrom(address,address,uint256)": "23b872dd"}}, "metadata": "{\"compiler\":{\"version\":\"0.8.24+commit.e11b9ed9\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"Approval\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"Transfer\",\"type\":\"event\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"}],\"name\":\"allowance\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"approve\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"balanceOf\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"totalSupply\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"transfer\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"transferFrom\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"details\":\"Interface of the ERC-20 standard as defined in the ERC.\",\"events\":{\"Approval(address,address,uint256)\":{\"details\":\"Emitted when the allowance of a `spender` for an `owner` is set by a call to {approve}. `value` is the new allowance.\"},\"Transfer(address,address,uint256)\":{\"details\":\"Emitted when `value` tokens are moved from one account (`from`) to another (`to`). Note that `value` may be zero.\"}},\"kind\":\"dev\",\"methods\":{\"allowance(address,address)\":{\"details\":\"Returns the remaining number of tokens that `spender` will be allowed to spend on behalf of `owner` through {transferFrom}. This is zero by default. This value changes when {approve} or {transferFrom} are called.\"},\"approve(address,uint256)\":{\"details\":\"Sets a `value` amount of tokens as the allowance of `spender` over the caller's tokens. Returns a boolean value indicating whether the operation succeeded. IMPORTANT: Beware that changing an allowance with this method brings the risk that someone may use both the old and the new allowance by unfortunate transaction ordering. One possible solution to mitigate this race condition is to first reduce the spender's allowance to 0 and set the desired value afterwards: https://github.com/ethereum/EIPs/issues/20#issuecomment-********* Emits an {Approval} event.\"},\"balanceOf(address)\":{\"details\":\"Returns the value of tokens owned by `account`.\"},\"totalSupply()\":{\"details\":\"Returns the value of tokens in existence.\"},\"transfer(address,uint256)\":{\"details\":\"Moves a `value` amount of tokens from the caller's account to `to`. Returns a boolean value indicating whether the operation succeeded. Emits a {Transfer} event.\"},\"transferFrom(address,address,uint256)\":{\"details\":\"Moves a `value` amount of tokens from `from` to `to` using the allowance mechanism. `value` is then deducted from the caller's allowance. Returns a boolean value indicating whether the operation succeeded. Emits a {Transfer} event.\"}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"@openzeppelin/contracts/token/ERC20/IERC20.sol\":\"IERC20\"},\"evmVersion\":\"paris\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[],\"viaIR\":true},\"sources\":{\"@openzeppelin/contracts/token/ERC20/IERC20.sol\":{\"keccak256\":\"0x74ed01eb66b923d0d0cfe3be84604ac04b76482a55f9dd655e1ef4d367f95bc2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5282825a626cfe924e504274b864a652b0023591fa66f06a067b25b51ba9b303\",\"dweb:/ipfs/QmeCfPykghhMc81VJTrHTC7sF6CRvaA1FXVq2pJhwYp1dV\"]}},\"version\":1}"}}, "@openzeppelin/contracts/utils/Context.sol": {"Context": {"abi": [], "evm": {"bytecode": {"functionDebugData": {}, "generatedSources": [], "linkReferences": {}, "object": "", "opcodes": "", "sourceMap": ""}, "deployedBytecode": {"functionDebugData": {}, "generatedSources": [], "immutableReferences": {}, "linkReferences": {}, "object": "", "opcodes": "", "sourceMap": ""}, "methodIdentifiers": {}}, "metadata": "{\"compiler\":{\"version\":\"0.8.24+commit.e11b9ed9\"},\"language\":\"Solidity\",\"output\":{\"abi\":[],\"devdoc\":{\"details\":\"Provides information about the current execution context, including the sender of the transaction and its data. While these are generally available via msg.sender and msg.data, they should not be accessed in such a direct manner, since when dealing with meta-transactions the account sending and paying for execution may not be the actual sender (as far as an application is concerned). This contract is only required for intermediate, library-like contracts.\",\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"@openzeppelin/contracts/utils/Context.sol\":\"Context\"},\"evmVersion\":\"paris\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[],\"viaIR\":true},\"sources\":{\"@openzeppelin/contracts/utils/Context.sol\":{\"keccak256\":\"0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12\",\"dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF\"]}},\"version\":1}"}}, "@openzeppelin/contracts/utils/ReentrancyGuard.sol": {"ReentrancyGuard": {"abi": [{"inputs": [], "name": "ReentrancyGuardReentrantCall", "type": "error"}], "evm": {"bytecode": {"functionDebugData": {}, "generatedSources": [], "linkReferences": {}, "object": "", "opcodes": "", "sourceMap": ""}, "deployedBytecode": {"functionDebugData": {}, "generatedSources": [], "immutableReferences": {}, "linkReferences": {}, "object": "", "opcodes": "", "sourceMap": ""}, "methodIdentifiers": {}}, "metadata": "{\"compiler\":{\"version\":\"0.8.24+commit.e11b9ed9\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"ReentrancyGuardReentrantCall\",\"type\":\"error\"}],\"devdoc\":{\"details\":\"Contract module that helps prevent reentrant calls to a function. Inheriting from `ReentrancyGuard` will make the {nonReentrant} modifier available, which can be applied to functions to make sure there are no nested (reentrant) calls to them. Note that because there is a single `nonReentrant` guard, functions marked as `nonReentrant` may not call one another. This can be worked around by making those functions `private`, and then adding `external` `nonReentrant` entry points to them. TIP: If EIP-1153 (transient storage) is available on the chain you're deploying at, consider using {ReentrancyGuardTransient} instead. TIP: If you would like to learn more about reentrancy and alternative ways to protect against it, check out our blog post https://blog.openzeppelin.com/reentrancy-after-istanbul/[Reentrancy After Istanbul].\",\"errors\":{\"ReentrancyGuardReentrantCall()\":[{\"details\":\"Unauthorized reentrant call.\"}]},\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"@openzeppelin/contracts/utils/ReentrancyGuard.sol\":\"ReentrancyGuard\"},\"evmVersion\":\"paris\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[],\"viaIR\":true},\"sources\":{\"@openzeppelin/contracts/utils/ReentrancyGuard.sol\":{\"keccak256\":\"0x11a5a79827df29e915a12740caf62fe21ebe27c08c9ae3e09abe9ee3ba3866d3\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3cf0c69ab827e3251db9ee6a50647d62c90ba580a4d7bbff21f2bea39e7b2f4a\",\"dweb:/ipfs/QmZiKwtKU1SBX4RGfQtY7PZfiapbbu6SZ9vizGQD9UHjRA\"]}},\"version\":1}"}}, "contracts/dao/SimpleDAO.sol": {"SimpleDAO": {"abi": [{"inputs": [{"internalType": "address", "name": "_governanceToken", "type": "address"}, {"internalType": "address", "name": "_owner", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "OwnableInvalidOwner", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "OwnableUnauthorizedAccount", "type": "error"}, {"inputs": [], "name": "ReentrancyGuardReentrantCall", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "depositor", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "FundsDeposited", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "recipient", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "FundsWithdrawn", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "proposalId", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "proposer", "type": "address"}, {"indexed": false, "internalType": "string", "name": "description", "type": "string"}, {"indexed": false, "internalType": "enum SimpleDAO.ProposalType", "name": "proposalType", "type": "uint8"}], "name": "ProposalCreated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "proposalId", "type": "uint256"}], "name": "ProposalExecuted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "proposalId", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "voter", "type": "address"}, {"indexed": false, "internalType": "bool", "name": "support", "type": "bool"}, {"indexed": false, "internalType": "uint256", "name": "weight", "type": "uint256"}], "name": "VoteCast", "type": "event"}, {"inputs": [{"internalType": "string", "name": "description", "type": "string"}, {"internalType": "enum SimpleDAO.ProposalType", "name": "proposalType", "type": "uint8"}, {"internalType": "uint256", "name": "requestedAmount", "type": "uint256"}, {"internalType": "address", "name": "beneficiary", "type": "address"}], "name": "createProposal", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "depositFunds", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "proposalId", "type": "uint256"}], "name": "executeProposal", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "proposalId", "type": "uint256"}], "name": "getProposal", "outputs": [{"internalType": "uint256", "name": "id", "type": "uint256"}, {"internalType": "address", "name": "proposer", "type": "address"}, {"internalType": "string", "name": "description", "type": "string"}, {"internalType": "enum SimpleDAO.ProposalType", "name": "proposalType", "type": "uint8"}, {"internalType": "uint256", "name": "requestedAmount", "type": "uint256"}, {"internalType": "address", "name": "beneficiary", "type": "address"}, {"internalType": "uint256", "name": "forVotes", "type": "uint256"}, {"internalType": "uint256", "name": "againstVotes", "type": "uint256"}, {"internalType": "uint256", "name": "startTime", "type": "uint256"}, {"internalType": "uint256", "name": "endTime", "type": "uint256"}, {"internalType": "enum SimpleDAO.ProposalState", "name": "state", "type": "uint8"}, {"internalType": "bool", "name": "executed", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "proposalId", "type": "uint256"}, {"internalType": "address", "name": "user", "type": "address"}], "name": "getUserVote", "outputs": [{"internalType": "bool", "name": "voted", "type": "bool"}, {"internalType": "bool", "name": "choice", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "governanceToken", "outputs": [{"internalType": "contract IERC20", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "address", "name": "", "type": "address"}], "name": "hasVoted", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "memberContributions", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "proposalCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "proposalThreshold", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "proposals", "outputs": [{"internalType": "uint256", "name": "id", "type": "uint256"}, {"internalType": "address", "name": "proposer", "type": "address"}, {"internalType": "string", "name": "description", "type": "string"}, {"internalType": "enum SimpleDAO.ProposalType", "name": "proposalType", "type": "uint8"}, {"internalType": "uint256", "name": "requestedAmount", "type": "uint256"}, {"internalType": "address", "name": "beneficiary", "type": "address"}, {"internalType": "uint256", "name": "forVotes", "type": "uint256"}, {"internalType": "uint256", "name": "againstVotes", "type": "uint256"}, {"internalType": "uint256", "name": "startTime", "type": "uint256"}, {"internalType": "uint256", "name": "endTime", "type": "uint256"}, {"internalType": "enum SimpleDAO.ProposalState", "name": "state", "type": "uint8"}, {"internalType": "bool", "name": "executed", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "quorumPercentage", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "newThreshold", "type": "uint256"}], "name": "setProposalThreshold", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "newPercentage", "type": "uint256"}], "name": "setQuorumPercentage", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "newPeriod", "type": "uint256"}], "name": "setVotingPeriod", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "treasuryBalance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "proposalId", "type": "uint256"}], "name": "updateProposalState", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "proposalId", "type": "uint256"}, {"internalType": "bool", "name": "support", "type": "bool"}], "name": "vote", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "address", "name": "", "type": "address"}], "name": "voteChoice", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "votingPeriod", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"stateMutability": "payable", "type": "receive"}], "evm": {"bytecode": {"functionDebugData": {"abi_decode_address_fromMemory": {"entryPoint": 269, "id": null, "parameterSlots": 1, "returnSlots": 1}}, "generatedSources": [], "linkReferences": {}, "object": "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", "opcodes": "PUSH1 0x80 CALLVALUE PUSH2 0xF2 JUMPI PUSH1 0x1F PUSH2 0x1675 CODESIZE DUP2 SWAP1 SUB SWAP2 DUP3 ADD PUSH1 0x1F NOT AND DUP4 ADD SWAP2 PUSH1 0x1 PUSH1 0x1 PUSH1 0x40 SHL SUB DUP4 GT DUP5 DUP5 LT OR PUSH2 0xF7 JUMPI DUP1 DUP5 SWAP3 PUSH1 0x40 SWAP5 DUP6 MSTORE DUP4 CODECOPY DUP2 ADD SUB SLT PUSH2 0xF2 JUMPI PUSH1 0x20 PUSH2 0x48 DUP3 PUSH2 0x10D JUMP JUMPDEST SWAP2 PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB SWAP2 DUP3 SWAP2 PUSH2 0x5E SWAP2 ADD PUSH2 0x10D JUMP JUMPDEST AND SWAP1 DUP2 ISZERO PUSH2 0xD9 JUMPI PUSH1 0x0 SLOAD SWAP3 PUSH1 0x1 DUP1 PUSH1 0xA0 SHL SUB NOT SWAP3 DUP1 DUP5 DUP7 AND OR PUSH1 0x0 SSTORE DUP3 PUSH1 0x40 MLOAD SWAP6 AND PUSH32 0x8BE0079C531659141344CD1FD0A4F28419497F9722A3DAAFE3B4186F6B6457E0 PUSH1 0x0 DUP1 LOG3 PUSH1 0x1 DUP1 SSTORE PUSH3 0x3F480 PUSH1 0x7 SSTORE PUSH9 0x3635C9ADC5DEA00000 PUSH1 0x8 SSTORE PUSH1 0x4 PUSH1 0x9 SSTORE AND SWAP1 PUSH1 0x2 SLOAD AND OR PUSH1 0x2 SSTORE PUSH2 0x1553 SWAP1 DUP2 PUSH2 0x122 DUP3 CODECOPY RETURN JUMPDEST PUSH1 0x40 MLOAD PUSH4 0x1E4FBDF7 PUSH1 0xE0 SHL DUP2 MSTORE PUSH1 0x0 PUSH1 0x4 DUP3 ADD MSTORE PUSH1 0x24 SWAP1 REVERT JUMPDEST PUSH1 0x0 DUP1 REVERT JUMPDEST PUSH4 0x4E487B71 PUSH1 0xE0 SHL PUSH1 0x0 MSTORE PUSH1 0x41 PUSH1 0x4 MSTORE PUSH1 0x24 PUSH1 0x0 REVERT JUMPDEST MLOAD SWAP1 PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB DUP3 AND DUP3 SUB PUSH2 0xF2 JUMPI JUMP INVALID PUSH1 0x80 PUSH1 0x40 MSTORE PUSH1 0x4 CALLDATASIZE LT ISZERO PUSH2 0x71 JUMPI JUMPDEST CALLDATASIZE ISZERO PUSH2 0x19 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST PUSH2 0x25 CALLVALUE PUSH1 0xA SLOAD PUSH2 0x132C JUMP JUMPDEST PUSH1 0xA SSTORE CALLER PUSH1 0x0 MSTORE PUSH1 0xB PUSH1 0x20 MSTORE PUSH1 0x40 PUSH1 0x0 KECCAK256 PUSH2 0x41 CALLVALUE DUP3 SLOAD PUSH2 0x132C JUMP JUMPDEST SWAP1 SSTORE PUSH1 0x40 MLOAD CALLVALUE DUP2 MSTORE PUSH32 0x543BA50A5EEC5E6178218E364B1D0F396157B3C8FA278522C2CB7FD99407D474 PUSH1 0x20 CALLER SWAP3 LOG2 STOP JUMPDEST PUSH1 0x0 CALLDATALOAD PUSH1 0xE0 SHR DUP1 PUSH4 0x13CF08B EQ PUSH2 0x108D JUMPI DUP1 PUSH4 0x2A251A3 EQ PUSH2 0x106F JUMPI DUP1 PUSH4 0x3C7881A EQ PUSH2 0xFFF JUMPI DUP1 PUSH4 0xD61B519 EQ PUSH2 0xD6D JUMPI DUP1 PUSH4 0x1A216BBD EQ PUSH2 0x891 JUMPI DUP1 PUSH4 0x313DAB20 EQ PUSH2 0x873 JUMPI DUP1 PUSH4 0x32F6A1DC EQ PUSH2 0x810 JUMPI DUP1 PUSH4 0x43859632 EQ PUSH2 0x7C3 JUMPI DUP1 PUSH4 0x4FA76EC9 EQ PUSH2 0x7A5 JUMPI DUP1 PUSH4 0x6AC600C7 EQ PUSH2 0x787 JUMPI DUP1 PUSH4 0x715018A6 EQ PUSH2 0x72E JUMPI DUP1 PUSH4 0x8DA5CB5B EQ PUSH2 0x705 JUMPI DUP1 PUSH4 0xA93271AF EQ PUSH2 0x6CB JUMPI DUP1 PUSH4 0xB58131B0 EQ PUSH2 0x6AD JUMPI DUP1 PUSH4 0xC7F758A8 EQ PUSH2 0x61E JUMPI DUP1 PUSH4 0xC9D27AFE EQ PUSH2 0x3C6 JUMPI DUP1 PUSH4 0xDA35C664 EQ PUSH2 0x3A8 JUMPI DUP1 PUSH4 0xE2C41DBC EQ PUSH2 0x2F5 JUMPI DUP1 PUSH4 0xEA0217CF EQ PUSH2 0x298 JUMPI DUP1 PUSH4 0xECE40CC1 EQ PUSH2 0x277 JUMPI DUP1 PUSH4 0xF2FDE38B EQ PUSH2 0x1EE JUMPI DUP1 PUSH4 0xF96DAE0A EQ PUSH2 0x1C5 JUMPI PUSH4 0xFB468855 SUB PUSH2 0xE JUMPI CALLVALUE PUSH2 0x1C0 JUMPI PUSH1 0x40 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x1C0 JUMPI PUSH2 0x18C PUSH2 0x12E4 JUMP JUMPDEST PUSH1 0x4 CALLDATALOAD PUSH1 0x0 MSTORE PUSH1 0x5 PUSH1 0x20 MSTORE PUSH1 0x40 PUSH1 0x0 KECCAK256 SWAP1 PUSH1 0x1 DUP1 PUSH1 0xA0 SHL SUB AND PUSH1 0x0 MSTORE PUSH1 0x20 MSTORE PUSH1 0x20 PUSH1 0xFF PUSH1 0x40 PUSH1 0x0 KECCAK256 SLOAD AND PUSH1 0x40 MLOAD SWAP1 ISZERO ISZERO DUP2 MSTORE RETURN JUMPDEST PUSH1 0x0 DUP1 REVERT JUMPDEST CALLVALUE PUSH2 0x1C0 JUMPI PUSH1 0x0 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x1C0 JUMPI PUSH1 0x2 SLOAD PUSH1 0x40 MLOAD PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB SWAP1 SWAP2 AND DUP2 MSTORE PUSH1 0x20 SWAP1 RETURN JUMPDEST CALLVALUE PUSH2 0x1C0 JUMPI PUSH1 0x20 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x1C0 JUMPI PUSH2 0x207 PUSH2 0x12FA JUMP JUMPDEST PUSH2 0x20F PUSH2 0x14F1 JUMP JUMPDEST PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB SWAP1 DUP2 AND SWAP1 DUP2 ISZERO PUSH2 0x25E JUMPI PUSH1 0x0 SLOAD DUP3 PUSH1 0x1 PUSH1 0x1 PUSH1 0x60 SHL SUB PUSH1 0xA0 SHL DUP3 AND OR PUSH1 0x0 SSTORE AND PUSH32 0x8BE0079C531659141344CD1FD0A4F28419497F9722A3DAAFE3B4186F6B6457E0 PUSH1 0x0 DUP1 LOG3 STOP JUMPDEST PUSH1 0x40 MLOAD PUSH4 0x1E4FBDF7 PUSH1 0xE0 SHL DUP2 MSTORE PUSH1 0x0 PUSH1 0x4 DUP3 ADD MSTORE PUSH1 0x24 SWAP1 REVERT JUMPDEST CALLVALUE PUSH2 0x1C0 JUMPI PUSH1 0x20 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x1C0 JUMPI PUSH2 0x290 PUSH2 0x14F1 JUMP JUMPDEST PUSH1 0x4 CALLDATALOAD PUSH1 0x8 SSTORE STOP JUMPDEST CALLVALUE PUSH2 0x1C0 JUMPI PUSH1 0x20 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x1C0 JUMPI PUSH1 0x4 CALLDATALOAD PUSH2 0x2B4 PUSH2 0x14F1 JUMP JUMPDEST DUP1 ISZERO PUSH2 0x2BF JUMPI PUSH1 0x7 SSTORE STOP JUMPDEST PUSH1 0x40 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE PUSH1 0x20 PUSH1 0x4 DUP3 ADD MSTORE PUSH1 0xE PUSH1 0x24 DUP3 ADD MSTORE PUSH14 0x125B9D985B1A59081C195C9A5BD9 PUSH1 0x92 SHL PUSH1 0x44 DUP3 ADD MSTORE PUSH1 0x64 SWAP1 REVERT JUMPDEST PUSH1 0x0 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x1C0 JUMPI PUSH2 0x309 PUSH2 0x14CE JUMP JUMPDEST CALLVALUE ISZERO PUSH2 0x36B JUMPI PUSH2 0x31B CALLVALUE PUSH1 0xA SLOAD PUSH2 0x132C JUMP JUMPDEST PUSH1 0xA SSTORE CALLER PUSH1 0x0 MSTORE PUSH1 0xB PUSH1 0x20 MSTORE PUSH1 0x40 PUSH1 0x0 KECCAK256 PUSH2 0x337 CALLVALUE DUP3 SLOAD PUSH2 0x132C JUMP JUMPDEST SWAP1 SSTORE PUSH1 0x40 MLOAD CALLVALUE DUP2 MSTORE PUSH32 0x543BA50A5EEC5E6178218E364B1D0F396157B3C8FA278522C2CB7FD99407D474 PUSH1 0x20 CALLER SWAP3 LOG2 PUSH1 0x1 DUP1 SSTORE STOP JUMPDEST PUSH1 0x40 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE PUSH1 0x20 PUSH1 0x4 DUP3 ADD MSTORE PUSH1 0x15 PUSH1 0x24 DUP3 ADD MSTORE PUSH21 0x9AEAE6E840C8CAE0DEE6D2E840E6DEDACA408AA89 PUSH1 0x5B SHL PUSH1 0x44 DUP3 ADD MSTORE PUSH1 0x64 SWAP1 REVERT JUMPDEST CALLVALUE PUSH2 0x1C0 JUMPI PUSH1 0x0 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x1C0 JUMPI PUSH1 0x20 PUSH1 0x6 SLOAD PUSH1 0x40 MLOAD SWAP1 DUP2 MSTORE RETURN JUMPDEST CALLVALUE PUSH2 0x1C0 JUMPI PUSH1 0x40 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x1C0 JUMPI PUSH1 0x24 DUP1 CALLDATALOAD DUP1 ISZERO ISZERO SWAP2 PUSH1 0x4 CALLDATALOAD SWAP2 DUP4 DUP2 SUB PUSH2 0x1C0 JUMPI DUP3 PUSH1 0x0 MSTORE PUSH1 0x20 SWAP1 PUSH1 0x3 DUP3 MSTORE PUSH1 0x40 PUSH1 0x0 KECCAK256 SWAP1 PUSH1 0xFF PUSH1 0xA DUP4 ADD SLOAD AND PUSH1 0x5 DUP2 LT ISZERO PUSH2 0x609 JUMPI PUSH1 0x1 PUSH2 0x418 SWAP2 EQ PUSH2 0x1339 JUMP JUMPDEST PUSH1 0x9 DUP3 ADD SLOAD TIMESTAMP GT PUSH2 0x5CF JUMPI DUP5 PUSH1 0x0 MSTORE PUSH1 0x4 DUP4 MSTORE PUSH1 0x40 PUSH1 0x0 KECCAK256 CALLER PUSH1 0x0 MSTORE DUP4 MSTORE PUSH1 0xFF PUSH1 0x40 PUSH1 0x0 KECCAK256 SLOAD AND PUSH2 0x59B JUMPI PUSH1 0x2 SLOAD PUSH1 0x40 MLOAD PUSH4 0x70A08231 PUSH1 0xE0 SHL DUP2 MSTORE CALLER PUSH1 0x4 DUP3 ADD MSTORE SWAP5 SWAP1 DUP5 SWAP1 DUP7 SWAP1 DUP4 SWAP1 DUP3 SWAP1 PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB AND GAS STATICCALL SWAP5 DUP6 ISZERO PUSH2 0x58F JUMPI PUSH1 0x0 SWAP6 PUSH2 0x560 JUMPI JUMPDEST POP DUP5 ISZERO PUSH2 0x52B JUMPI POP DUP5 PUSH1 0x0 MSTORE PUSH1 0x4 DUP4 MSTORE PUSH1 0x40 PUSH1 0x0 KECCAK256 CALLER PUSH1 0x0 MSTORE DUP4 MSTORE PUSH1 0x40 PUSH1 0x0 KECCAK256 PUSH1 0xFF NOT SWAP1 PUSH1 0x1 DUP3 DUP3 SLOAD AND OR SWAP1 SSTORE DUP6 PUSH1 0x0 MSTORE PUSH1 0x5 DUP5 MSTORE PUSH1 0x40 PUSH1 0x0 KECCAK256 CALLER PUSH1 0x0 MSTORE DUP5 MSTORE PUSH1 0x40 PUSH1 0x0 KECCAK256 SWAP1 DUP2 SLOAD AND PUSH1 0xFF DUP9 AND OR SWAP1 SSTORE PUSH1 0x0 EQ PUSH2 0x516 JUMPI PUSH1 0x6 ADD PUSH2 0x4E2 DUP4 DUP3 SLOAD PUSH2 0x132C JUMP JUMPDEST SWAP1 SSTORE JUMPDEST PUSH1 0x40 MLOAD SWAP4 DUP5 MSTORE DUP4 ADD MSTORE PUSH32 0xCBDF6214089CBA887ECBF35A0B6A734589959C9763342C756BB2A80CA2BC9F6E PUSH1 0x40 CALLER SWAP4 LOG3 STOP JUMPDEST PUSH1 0x7 ADD PUSH2 0x524 DUP4 DUP3 SLOAD PUSH2 0x132C JUMP JUMPDEST SWAP1 SSTORE PUSH2 0x4E5 JUMP JUMPDEST DUP4 PUSH1 0xF PUSH1 0x64 SWAP3 PUSH1 0x40 MLOAD SWAP3 PUSH3 0x461BCD PUSH1 0xE5 SHL DUP5 MSTORE PUSH1 0x4 DUP5 ADD MSTORE DUP3 ADD MSTORE PUSH15 0x2737903B37BA34B733903837BBB2B9 PUSH1 0x89 SHL PUSH1 0x44 DUP3 ADD MSTORE REVERT JUMPDEST SWAP1 SWAP5 POP DUP4 DUP2 DUP2 RETURNDATASIZE DUP4 GT PUSH2 0x588 JUMPI JUMPDEST PUSH2 0x578 DUP2 DUP4 PUSH2 0x1151 JUMP JUMPDEST DUP2 ADD SUB SLT PUSH2 0x1C0 JUMPI MLOAD SWAP4 DUP8 PUSH2 0x47D JUMP JUMPDEST POP RETURNDATASIZE PUSH2 0x56E JUMP JUMPDEST PUSH1 0x40 MLOAD RETURNDATASIZE PUSH1 0x0 DUP3 RETURNDATACOPY RETURNDATASIZE SWAP1 REVERT JUMPDEST PUSH1 0x40 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE PUSH1 0x4 DUP2 ADD DUP5 SWAP1 MSTORE PUSH1 0xD DUP2 DUP7 ADD MSTORE PUSH13 0x105B1C9958591E481D9BDD1959 PUSH1 0x9A SHL PUSH1 0x44 DUP3 ADD MSTORE PUSH1 0x64 SWAP1 REVERT JUMPDEST PUSH1 0x40 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE PUSH1 0x4 DUP2 ADD DUP5 SWAP1 MSTORE PUSH1 0x13 DUP2 DUP7 ADD MSTORE PUSH19 0x159BDD1A5B99C81C195C9A5BD908195B991959 PUSH1 0x6A SHL PUSH1 0x44 DUP3 ADD MSTORE PUSH1 0x64 SWAP1 REVERT JUMPDEST DUP5 PUSH4 0x4E487B71 PUSH1 0xE0 SHL PUSH1 0x0 MSTORE PUSH1 0x21 PUSH1 0x4 MSTORE PUSH1 0x0 REVERT JUMPDEST CALLVALUE PUSH2 0x1C0 JUMPI PUSH1 0x20 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x1C0 JUMPI PUSH1 0x4 CALLDATALOAD PUSH1 0x0 MSTORE PUSH1 0x3 PUSH1 0x20 MSTORE PUSH1 0x40 PUSH1 0x0 KECCAK256 DUP1 SLOAD PUSH2 0x6A9 PUSH1 0x1 DUP1 PUSH1 0xA0 SHL SUB SWAP3 DUP4 PUSH1 0x1 DUP3 ADD SLOAD AND SWAP3 PUSH1 0xFF PUSH1 0x3 DUP4 ADD SLOAD AND SWAP5 PUSH1 0x4 DUP4 ADD SLOAD SWAP1 PUSH1 0x5 DUP5 ADD SLOAD AND PUSH1 0x6 DUP5 ADD SLOAD PUSH1 0x7 DUP6 ADD SLOAD SWAP1 PUSH1 0x8 DUP7 ADD SLOAD SWAP3 PUSH1 0x9 DUP8 ADD SLOAD SWAP5 PUSH2 0x691 PUSH1 0x2 PUSH1 0xA DUP11 ADD SLOAD SWAP10 ADD PUSH2 0x1173 JUMP JUMPDEST SWAP11 PUSH1 0x40 MLOAD SWAP12 DUP13 SWAP12 PUSH1 0xFF DUP1 DUP13 PUSH1 0x8 SHR AND SWAP12 AND SWAP10 DUP14 PUSH2 0x1265 JUMP JUMPDEST SUB SWAP1 RETURN JUMPDEST CALLVALUE PUSH2 0x1C0 JUMPI PUSH1 0x0 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x1C0 JUMPI PUSH1 0x20 PUSH1 0x8 SLOAD PUSH1 0x40 MLOAD SWAP1 DUP2 MSTORE RETURN JUMPDEST CALLVALUE PUSH2 0x1C0 JUMPI PUSH1 0x20 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x1C0 JUMPI PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB PUSH2 0x6EC PUSH2 0x12FA JUMP JUMPDEST AND PUSH1 0x0 MSTORE PUSH1 0xB PUSH1 0x20 MSTORE PUSH1 0x20 PUSH1 0x40 PUSH1 0x0 KECCAK256 SLOAD PUSH1 0x40 MLOAD SWAP1 DUP2 MSTORE RETURN JUMPDEST CALLVALUE PUSH2 0x1C0 JUMPI PUSH1 0x0 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x1C0 JUMPI PUSH1 0x0 SLOAD PUSH1 0x40 MLOAD PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB SWAP1 SWAP2 AND DUP2 MSTORE PUSH1 0x20 SWAP1 RETURN JUMPDEST CALLVALUE PUSH2 0x1C0 JUMPI PUSH1 0x0 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x1C0 JUMPI PUSH2 0x747 PUSH2 0x14F1 JUMP JUMPDEST PUSH1 0x0 DUP1 SLOAD PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB NOT DUP2 AND DUP3 SSTORE PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB AND PUSH32 0x8BE0079C531659141344CD1FD0A4F28419497F9722A3DAAFE3B4186F6B6457E0 DUP3 DUP1 LOG3 STOP JUMPDEST CALLVALUE PUSH2 0x1C0 JUMPI PUSH1 0x20 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x1C0 JUMPI PUSH2 0x7A3 PUSH1 0x4 CALLDATALOAD PUSH2 0x137B JUMP JUMPDEST STOP JUMPDEST CALLVALUE PUSH2 0x1C0 JUMPI PUSH1 0x0 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x1C0 JUMPI PUSH1 0x20 PUSH1 0x9 SLOAD PUSH1 0x40 MLOAD SWAP1 DUP2 MSTORE RETURN JUMPDEST CALLVALUE PUSH2 0x1C0 JUMPI PUSH1 0x40 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x1C0 JUMPI PUSH2 0x7DC PUSH2 0x12E4 JUMP JUMPDEST PUSH1 0x4 CALLDATALOAD PUSH1 0x0 MSTORE PUSH1 0x4 PUSH1 0x20 MSTORE PUSH1 0x40 PUSH1 0x0 KECCAK256 SWAP1 PUSH1 0x1 DUP1 PUSH1 0xA0 SHL SUB AND PUSH1 0x0 MSTORE PUSH1 0x20 MSTORE PUSH1 0x20 PUSH1 0xFF PUSH1 0x40 PUSH1 0x0 KECCAK256 SLOAD AND PUSH1 0x40 MLOAD SWAP1 ISZERO ISZERO DUP2 MSTORE RETURN JUMPDEST CALLVALUE PUSH2 0x1C0 JUMPI PUSH1 0x20 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x1C0 JUMPI PUSH1 0x4 CALLDATALOAD PUSH2 0x82C PUSH2 0x14F1 JUMP JUMPDEST PUSH1 0x64 DUP2 GT PUSH2 0x839 JUMPI PUSH1 0x9 SSTORE STOP JUMPDEST PUSH1 0x40 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE PUSH1 0x20 PUSH1 0x4 DUP3 ADD MSTORE PUSH1 0x12 PUSH1 0x24 DUP3 ADD MSTORE PUSH18 0x496E76616C69642070657263656E74616765 PUSH1 0x70 SHL PUSH1 0x44 DUP3 ADD MSTORE PUSH1 0x64 SWAP1 REVERT JUMPDEST CALLVALUE PUSH2 0x1C0 JUMPI PUSH1 0x0 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x1C0 JUMPI PUSH1 0x20 PUSH1 0xA SLOAD PUSH1 0x40 MLOAD SWAP1 DUP2 MSTORE RETURN JUMPDEST CALLVALUE PUSH2 0x1C0 JUMPI PUSH1 0x80 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x1C0 JUMPI PUSH1 0x4 CALLDATALOAD PUSH8 0xFFFFFFFFFFFFFFFF DUP2 GT PUSH2 0x1C0 JUMPI CALLDATASIZE PUSH1 0x23 DUP3 ADD SLT ISZERO PUSH2 0x1C0 JUMPI DUP1 PUSH1 0x4 ADD CALLDATALOAD PUSH2 0x8CD DUP2 PUSH2 0x1310 JUMP JUMPDEST SWAP2 PUSH2 0x8DB PUSH1 0x40 MLOAD SWAP4 DUP5 PUSH2 0x1151 JUMP JUMPDEST DUP2 DUP4 MSTORE CALLDATASIZE PUSH1 0x24 DUP4 DUP4 ADD ADD GT PUSH2 0x1C0 JUMPI DUP2 PUSH1 0x0 SWAP3 PUSH1 0x24 PUSH1 0x20 SWAP4 ADD DUP4 DUP7 ADD CALLDATACOPY DUP4 ADD ADD MSTORE PUSH1 0x3 PUSH1 0x24 CALLDATALOAD LT ISZERO PUSH2 0x1C0 JUMPI PUSH1 0x64 CALLDATALOAD SWAP1 PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB DUP3 AND DUP3 SUB PUSH2 0x1C0 JUMPI DUP1 MLOAD ISZERO PUSH2 0xD28 JUMPI PUSH1 0x2 SLOAD PUSH1 0x40 MLOAD PUSH4 0x70A08231 PUSH1 0xE0 SHL DUP2 MSTORE CALLER PUSH1 0x4 DUP3 ADD MSTORE SWAP1 PUSH1 0x20 SWAP1 DUP3 SWAP1 PUSH1 0x24 SWAP1 DUP3 SWAP1 PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB AND GAS STATICCALL SWAP1 DUP2 ISZERO PUSH2 0x58F JUMPI PUSH1 0x0 SWAP2 PUSH2 0xCF6 JUMPI JUMPDEST POP PUSH1 0x8 SLOAD GT PUSH2 0xCA2 JUMPI PUSH1 0x6 SLOAD SWAP1 PUSH1 0x0 NOT DUP3 EQ PUSH2 0xC8C JUMPI PUSH1 0x1 DUP3 ADD PUSH1 0x6 SSTORE PUSH2 0x986 PUSH1 0x7 SLOAD TIMESTAMP PUSH2 0x132C JUMP JUMPDEST PUSH1 0x40 MLOAD SWAP4 DUP5 PUSH2 0x180 DUP2 ADD LT PUSH8 0xFFFFFFFFFFFFFFFF PUSH2 0x180 DUP8 ADD GT OR PUSH2 0xC76 JUMPI PUSH2 0x180 DUP6 ADD PUSH1 0x40 MSTORE DUP4 DUP6 MSTORE CALLER PUSH1 0x20 DUP7 ADD MSTORE DUP3 PUSH1 0x40 DUP7 ADD MSTORE PUSH1 0x24 CALLDATALOAD PUSH1 0x60 DUP7 ADD MSTORE PUSH1 0x44 CALLDATALOAD PUSH1 0x80 DUP7 ADD MSTORE PUSH1 0x1 DUP1 PUSH1 0xA0 SHL SUB AND PUSH1 0xA0 DUP6 ADD MSTORE PUSH1 0x0 PUSH1 0xC0 DUP6 ADD MSTORE PUSH1 0x0 PUSH1 0xE0 DUP6 ADD MSTORE TIMESTAMP PUSH2 0x100 DUP6 ADD MSTORE PUSH2 0x120 DUP5 ADD MSTORE PUSH1 0x1 PUSH2 0x140 DUP5 ADD MSTORE PUSH1 0x0 PUSH2 0x160 DUP5 ADD MSTORE DUP2 PUSH1 0x0 MSTORE PUSH1 0x3 PUSH1 0x20 MSTORE PUSH1 0x40 PUSH1 0x0 KECCAK256 DUP4 MLOAD DUP2 SSTORE PUSH1 0x1 DUP2 ADD PUSH1 0x1 DUP1 PUSH1 0xA0 SHL SUB PUSH1 0x20 DUP7 ADD MLOAD AND PUSH1 0x1 PUSH1 0x1 PUSH1 0x60 SHL SUB PUSH1 0xA0 SHL DUP3 SLOAD AND OR SWAP1 SSTORE PUSH1 0x40 DUP5 ADD MLOAD DUP1 MLOAD SWAP1 PUSH8 0xFFFFFFFFFFFFFFFF DUP3 GT PUSH2 0xC76 JUMPI DUP2 SWAP1 PUSH2 0xA5E PUSH1 0x2 DUP6 ADD SLOAD PUSH2 0x1117 JUMP JUMPDEST PUSH1 0x1F DUP2 GT PUSH2 0xC23 JUMPI JUMPDEST POP PUSH1 0x20 SWAP1 PUSH1 0x1F DUP4 GT PUSH1 0x1 EQ PUSH2 0xBB6 JUMPI PUSH1 0x0 SWAP3 PUSH2 0xBAB JUMPI JUMPDEST POP POP DUP2 PUSH1 0x1 SHL SWAP2 PUSH1 0x0 NOT SWAP1 PUSH1 0x3 SHL SHR NOT AND OR PUSH1 0x2 DUP3 ADD SSTORE JUMPDEST PUSH1 0x3 DUP2 ADD SWAP1 PUSH1 0x60 DUP6 ADD MLOAD SWAP2 PUSH1 0x3 DUP4 LT ISZERO PUSH2 0xB95 JUMPI PUSH1 0xA SWAP3 PUSH1 0xFF DUP1 NOT DUP4 SLOAD AND SWAP2 AND OR SWAP1 SSTORE PUSH1 0x80 DUP6 ADD MLOAD PUSH1 0x4 DUP3 ADD SSTORE PUSH1 0x5 DUP2 ADD PUSH1 0x1 DUP1 PUSH1 0xA0 SHL SUB PUSH1 0xA0 DUP8 ADD MLOAD AND PUSH1 0x1 PUSH1 0x1 PUSH1 0x60 SHL SUB PUSH1 0xA0 SHL DUP3 SLOAD AND OR SWAP1 SSTORE PUSH1 0xC0 DUP6 ADD MLOAD PUSH1 0x6 DUP3 ADD SSTORE PUSH1 0xE0 DUP6 ADD MLOAD PUSH1 0x7 DUP3 ADD SSTORE PUSH2 0x100 DUP6 ADD MLOAD PUSH1 0x8 DUP3 ADD SSTORE PUSH2 0x120 DUP6 ADD MLOAD PUSH1 0x9 DUP3 ADD SSTORE ADD PUSH2 0x140 DUP5 ADD MLOAD PUSH1 0x5 DUP2 LT ISZERO PUSH2 0xB95 JUMPI PUSH1 0x20 SWAP5 PUSH1 0xFF PUSH2 0xFF00 PUSH2 0x160 DUP6 SLOAD SWAP4 ADD MLOAD ISZERO ISZERO PUSH1 0x8 SHL AND SWAP3 AND SWAP1 PUSH2 0xFFFF NOT AND OR OR SWAP1 SSTORE DUP2 PUSH32 0xE0269F7953E70365E80E614E4EFE7D4BEDE59B7F5AD80982EA4064C97EE6F4EF PUSH2 0xB78 PUSH1 0x40 MLOAD SWAP4 PUSH1 0x40 DUP6 MSTORE PUSH1 0x40 DUP6 ADD SWAP1 PUSH2 0x1218 JUMP JUMPDEST SWAP3 PUSH2 0xB87 DUP7 DUP3 ADD PUSH1 0x24 CALLDATALOAD PUSH2 0x1258 JUMP JUMPDEST DUP1 CALLER SWAP5 SUB SWAP1 LOG3 PUSH1 0x40 MLOAD SWAP1 DUP2 MSTORE RETURN JUMPDEST PUSH4 0x4E487B71 PUSH1 0xE0 SHL PUSH1 0x0 MSTORE PUSH1 0x21 PUSH1 0x4 MSTORE PUSH1 0x24 PUSH1 0x0 REVERT JUMPDEST ADD MLOAD SWAP1 POP DUP7 DUP1 PUSH2 0xA7E JUMP JUMPDEST PUSH1 0x2 DUP6 ADD PUSH1 0x0 SWAP1 DUP2 MSTORE PUSH1 0x20 DUP2 KECCAK256 SWAP4 POP PUSH1 0x1F NOT DUP6 AND SWAP1 JUMPDEST DUP2 DUP2 LT PUSH2 0xC0B JUMPI POP SWAP1 DUP5 PUSH1 0x1 SWAP6 SWAP5 SWAP4 SWAP3 LT PUSH2 0xBF2 JUMPI JUMPDEST POP POP POP DUP2 SHL ADD PUSH1 0x2 DUP3 ADD SSTORE PUSH2 0xA96 JUMP JUMPDEST ADD MLOAD PUSH1 0x0 NOT PUSH1 0xF8 DUP5 PUSH1 0x3 SHL AND SHR NOT AND SWAP1 SSTORE DUP7 DUP1 DUP1 PUSH2 0xBE2 JUMP JUMPDEST SWAP3 SWAP4 PUSH1 0x20 PUSH1 0x1 DUP2 SWAP3 DUP8 DUP7 ADD MLOAD DUP2 SSTORE ADD SWAP6 ADD SWAP4 ADD PUSH2 0xBCC JUMP JUMPDEST SWAP1 SWAP2 POP PUSH1 0x2 DUP5 ADD PUSH1 0x0 MSTORE PUSH1 0x20 PUSH1 0x0 KECCAK256 PUSH1 0x1F DUP5 ADD PUSH1 0x5 SHR DUP2 ADD PUSH1 0x20 DUP6 LT PUSH2 0xC6F JUMPI JUMPDEST SWAP1 DUP5 SWAP4 SWAP3 SWAP2 JUMPDEST PUSH1 0x1F DUP4 ADD PUSH1 0x5 SHR DUP3 ADD DUP2 LT PUSH2 0xC60 JUMPI POP POP PUSH2 0xA67 JUMP JUMPDEST PUSH1 0x0 DUP2 SSTORE DUP6 SWAP5 POP PUSH1 0x1 ADD PUSH2 0xC4A JUMP JUMPDEST POP DUP1 PUSH2 0xC44 JUMP JUMPDEST PUSH4 0x4E487B71 PUSH1 0xE0 SHL PUSH1 0x0 MSTORE PUSH1 0x41 PUSH1 0x4 MSTORE PUSH1 0x24 PUSH1 0x0 REVERT JUMPDEST PUSH4 0x4E487B71 PUSH1 0xE0 SHL PUSH1 0x0 MSTORE PUSH1 0x11 PUSH1 0x4 MSTORE PUSH1 0x24 PUSH1 0x0 REVERT JUMPDEST PUSH1 0x40 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE PUSH1 0x20 PUSH1 0x4 DUP3 ADD MSTORE PUSH1 0x26 PUSH1 0x24 DUP3 ADD MSTORE PUSH32 0x496E73756666696369656E7420746F6B656E7320746F20637265617465207072 PUSH1 0x44 DUP3 ADD MSTORE PUSH6 0x1BDC1BDCD85B PUSH1 0xD2 SHL PUSH1 0x64 DUP3 ADD MSTORE PUSH1 0x84 SWAP1 REVERT JUMPDEST SWAP1 POP PUSH1 0x20 DUP2 RETURNDATASIZE PUSH1 0x20 GT PUSH2 0xD20 JUMPI JUMPDEST DUP2 PUSH2 0xD11 PUSH1 0x20 SWAP4 DUP4 PUSH2 0x1151 JUMP JUMPDEST DUP2 ADD SUB SLT PUSH2 0x1C0 JUMPI MLOAD DUP4 PUSH2 0x95D JUMP JUMPDEST RETURNDATASIZE SWAP2 POP PUSH2 0xD04 JUMP JUMPDEST PUSH1 0x40 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE PUSH1 0x20 PUSH1 0x4 DUP3 ADD MSTORE PUSH1 0x1B PUSH1 0x24 DUP3 ADD MSTORE PUSH32 0x4465736372697074696F6E2063616E6E6F7420626520656D7074790000000000 PUSH1 0x44 DUP3 ADD MSTORE PUSH1 0x64 SWAP1 REVERT JUMPDEST CALLVALUE PUSH2 0x1C0 JUMPI PUSH1 0x20 DUP1 PUSH1 0x3 NOT CALLDATASIZE ADD SLT PUSH2 0x1C0 JUMPI PUSH1 0x4 CALLDATALOAD SWAP1 PUSH2 0xD8B PUSH2 0x14CE JUMP JUMPDEST DUP2 PUSH1 0x0 MSTORE PUSH1 0x3 DUP2 MSTORE PUSH1 0x40 PUSH1 0x0 KECCAK256 SWAP1 PUSH2 0xDA2 DUP4 PUSH2 0x137B JUMP JUMPDEST PUSH1 0xA DUP3 ADD DUP1 SLOAD PUSH1 0xFF DUP2 AND PUSH1 0x5 DUP2 LT ISZERO PUSH2 0xB95 JUMPI PUSH1 0x2 SUB PUSH2 0xFC1 JUMPI PUSH1 0xFF DUP2 PUSH1 0x8 SHR AND PUSH2 0xF7C JUMPI PUSH2 0xFFFF NOT AND PUSH2 0x104 OR SWAP1 SSTORE PUSH1 0x3 DUP3 DUP2 ADD SLOAD PUSH1 0xFF AND SWAP1 DUP2 LT ISZERO PUSH2 0xB95 JUMPI PUSH1 0x1 EQ PUSH2 0xE17 JUMPI JUMPDEST DUP3 PUSH32 0x712AE1383F79AC853F8D882153778E0260EF8F03B504E2866E0593E04D2B291F PUSH1 0x0 DUP1 LOG2 PUSH1 0x1 DUP1 SSTORE STOP JUMPDEST PUSH1 0x4 DUP3 ADD SWAP2 DUP3 SLOAD SWAP1 PUSH1 0xA SLOAD SWAP1 DUP2 DUP4 GT PUSH2 0xF37 JUMPI PUSH1 0x5 ADD DUP1 SLOAD PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB SWAP4 SWAP2 SWAP3 SWAP2 SWAP1 DUP5 AND ISZERO PUSH2 0xEFC JUMPI DUP2 SUB SWAP1 DUP2 GT PUSH2 0xC8C JUMPI PUSH1 0xA SSTORE PUSH1 0x0 DUP1 DUP1 DUP1 DUP6 DUP6 SLOAD AND DUP9 SLOAD SWAP1 GAS CALL RETURNDATASIZE ISZERO PUSH2 0xEF7 JUMPI RETURNDATASIZE PUSH2 0xE6D DUP2 PUSH2 0x1310 JUMP JUMPDEST SWAP1 PUSH2 0xE7B PUSH1 0x40 MLOAD SWAP3 DUP4 PUSH2 0x1151 JUMP JUMPDEST DUP2 MSTORE PUSH1 0x0 DUP6 RETURNDATASIZE SWAP3 ADD RETURNDATACOPY JUMPDEST ISZERO PUSH2 0xEC0 JUMPI SWAP1 PUSH32 0xEAFF4B37086828766AD3268786972C0CD24259D4C87A80F9D3963A3C3D999B0D SWAP3 SWAP2 SLOAD AND SWAP3 SLOAD PUSH1 0x40 MLOAD SWAP1 DUP2 MSTORE LOG2 DUP2 DUP1 PUSH2 0xDEB JUMP JUMPDEST PUSH1 0x40 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE PUSH1 0x4 DUP2 ADD DUP5 SWAP1 MSTORE PUSH1 0xF PUSH1 0x24 DUP3 ADD MSTORE PUSH15 0x151C985B9CD9995C8819985A5B1959 PUSH1 0x8A SHL PUSH1 0x44 DUP3 ADD MSTORE PUSH1 0x64 SWAP1 REVERT JUMPDEST PUSH2 0xE85 JUMP JUMPDEST PUSH1 0x40 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE PUSH1 0x4 DUP2 ADD DUP7 SWAP1 MSTORE PUSH1 0x13 PUSH1 0x24 DUP3 ADD MSTORE PUSH19 0x496E76616C69642062656E6566696369617279 PUSH1 0x68 SHL PUSH1 0x44 DUP3 ADD MSTORE PUSH1 0x64 SWAP1 REVERT JUMPDEST PUSH1 0x40 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE PUSH1 0x4 DUP2 ADD DUP6 SWAP1 MSTORE PUSH1 0x1B PUSH1 0x24 DUP3 ADD MSTORE PUSH32 0x496E73756666696369656E742074726561737572792066756E64730000000000 PUSH1 0x44 DUP3 ADD MSTORE PUSH1 0x64 SWAP1 REVERT JUMPDEST PUSH1 0x40 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE PUSH1 0x4 DUP2 ADD DUP5 SWAP1 MSTORE PUSH1 0x19 PUSH1 0x24 DUP3 ADD MSTORE PUSH32 0x50726F706F73616C20616C726561647920657865637574656400000000000000 PUSH1 0x44 DUP3 ADD MSTORE PUSH1 0x64 SWAP1 REVERT JUMPDEST PUSH1 0x40 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE PUSH1 0x4 DUP2 ADD DUP5 SWAP1 MSTORE PUSH1 0x16 PUSH1 0x24 DUP3 ADD MSTORE PUSH22 0x141C9BDC1BDCD85B081B9BDD081CDD58D8D959591959 PUSH1 0x52 SHL PUSH1 0x44 DUP3 ADD MSTORE PUSH1 0x64 SWAP1 REVERT JUMPDEST CALLVALUE PUSH2 0x1C0 JUMPI PUSH1 0x40 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x1C0 JUMPI PUSH1 0x40 PUSH1 0x4 CALLDATALOAD PUSH2 0x101D PUSH2 0x12E4 JUMP JUMPDEST DUP2 PUSH1 0x0 MSTORE PUSH1 0x4 PUSH1 0x20 MSTORE DUP3 PUSH1 0x0 KECCAK256 SWAP1 PUSH1 0x1 DUP1 PUSH1 0xA0 SHL SUB AND SWAP1 DUP2 PUSH1 0x0 MSTORE PUSH1 0x20 MSTORE PUSH1 0xFF DUP4 PUSH1 0x0 KECCAK256 SLOAD AND SWAP2 PUSH1 0x0 MSTORE PUSH1 0x5 PUSH1 0x20 MSTORE DUP3 PUSH1 0x0 KECCAK256 SWAP1 PUSH1 0x0 MSTORE PUSH1 0x20 MSTORE PUSH1 0xFF DUP3 PUSH1 0x0 KECCAK256 SLOAD AND DUP3 MLOAD SWAP2 ISZERO ISZERO DUP3 MSTORE ISZERO ISZERO PUSH1 0x20 DUP3 ADD MSTORE RETURN JUMPDEST CALLVALUE PUSH2 0x1C0 JUMPI PUSH1 0x0 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x1C0 JUMPI PUSH1 0x20 PUSH1 0x7 SLOAD PUSH1 0x40 MLOAD SWAP1 DUP2 MSTORE RETURN JUMPDEST CALLVALUE PUSH2 0x1C0 JUMPI PUSH1 0x20 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x1C0 JUMPI PUSH1 0x4 CALLDATALOAD PUSH1 0x0 MSTORE PUSH1 0x3 PUSH1 0x20 MSTORE PUSH1 0x40 PUSH1 0x0 KECCAK256 DUP1 SLOAD PUSH2 0x6A9 PUSH1 0x1 DUP1 PUSH1 0xA0 SHL SUB DUP1 PUSH1 0x1 DUP6 ADD SLOAD AND SWAP3 PUSH2 0x10CE PUSH1 0x2 DUP7 ADD PUSH2 0x1173 JUMP JUMPDEST SWAP5 PUSH1 0xFF PUSH1 0x3 DUP3 ADD SLOAD AND SWAP3 PUSH1 0x4 DUP3 ADD SLOAD SWAP1 PUSH1 0x5 DUP4 ADD SLOAD AND PUSH1 0x6 DUP4 ADD SLOAD PUSH1 0x7 DUP5 ADD SLOAD SWAP2 PUSH1 0x8 DUP6 ADD SLOAD SWAP4 PUSH1 0xA PUSH1 0x9 DUP8 ADD SLOAD SWAP7 ADD SLOAD SWAP8 PUSH1 0x40 MLOAD SWAP12 DUP13 SWAP12 PUSH1 0xFF DUP1 DUP13 PUSH1 0x8 SHR AND SWAP12 AND SWAP10 DUP14 PUSH2 0x1265 JUMP JUMPDEST SWAP1 PUSH1 0x1 DUP3 DUP2 SHR SWAP3 AND DUP1 ISZERO PUSH2 0x1147 JUMPI JUMPDEST PUSH1 0x20 DUP4 LT EQ PUSH2 0x1131 JUMPI JUMP JUMPDEST PUSH4 0x4E487B71 PUSH1 0xE0 SHL PUSH1 0x0 MSTORE PUSH1 0x22 PUSH1 0x4 MSTORE PUSH1 0x24 PUSH1 0x0 REVERT JUMPDEST SWAP2 PUSH1 0x7F AND SWAP2 PUSH2 0x1126 JUMP JUMPDEST SWAP1 PUSH1 0x1F DUP1 NOT SWAP2 ADD AND DUP2 ADD SWAP1 DUP2 LT PUSH8 0xFFFFFFFFFFFFFFFF DUP3 GT OR PUSH2 0xC76 JUMPI PUSH1 0x40 MSTORE JUMP JUMPDEST SWAP1 PUSH1 0x40 MLOAD SWAP2 DUP3 PUSH1 0x0 DUP3 SLOAD PUSH2 0x1186 DUP2 PUSH2 0x1117 JUMP JUMPDEST SWAP1 DUP2 DUP5 MSTORE PUSH1 0x20 SWAP5 PUSH1 0x1 SWAP2 PUSH1 0x1 DUP2 AND SWAP1 DUP2 PUSH1 0x0 EQ PUSH2 0x11F6 JUMPI POP PUSH1 0x1 EQ PUSH2 0x11B7 JUMPI JUMPDEST POP POP POP PUSH2 0x11B5 SWAP3 POP SUB DUP4 PUSH2 0x1151 JUMP JUMPDEST JUMP JUMPDEST PUSH1 0x0 SWAP1 DUP2 MSTORE DUP6 DUP2 KECCAK256 SWAP6 SWAP4 POP SWAP2 SWAP1 JUMPDEST DUP2 DUP4 LT PUSH2 0x11DE JUMPI POP POP PUSH2 0x11B5 SWAP4 POP DUP3 ADD ADD CODESIZE DUP1 DUP1 PUSH2 0x11A6 JUMP JUMPDEST DUP6 SLOAD DUP9 DUP5 ADD DUP6 ADD MSTORE SWAP5 DUP6 ADD SWAP5 DUP8 SWAP5 POP SWAP2 DUP4 ADD SWAP2 PUSH2 0x11C5 JUMP JUMPDEST SWAP3 POP POP POP PUSH2 0x11B5 SWAP5 SWAP3 POP PUSH1 0xFF NOT AND DUP3 DUP5 ADD MSTORE ISZERO ISZERO PUSH1 0x5 SHL DUP3 ADD ADD CODESIZE DUP1 DUP1 PUSH2 0x11A6 JUMP JUMPDEST SWAP2 SWAP1 DUP3 MLOAD SWAP3 DUP4 DUP3 MSTORE PUSH1 0x0 JUMPDEST DUP5 DUP2 LT PUSH2 0x1244 JUMPI POP POP DUP3 PUSH1 0x0 PUSH1 0x20 DUP1 SWAP5 SWAP6 DUP5 ADD ADD MSTORE PUSH1 0x1F DUP1 NOT SWAP2 ADD AND ADD ADD SWAP1 JUMP JUMPDEST PUSH1 0x20 DUP2 DUP4 ADD DUP2 ADD MLOAD DUP5 DUP4 ADD DUP3 ADD MSTORE ADD PUSH2 0x1223 JUMP JUMPDEST SWAP1 PUSH1 0x3 DUP3 LT ISZERO PUSH2 0xB95 JUMPI MSTORE JUMP JUMPDEST SWAP11 SWAP10 SWAP8 SWAP6 SWAP3 PUSH2 0x129E SWAP1 DUP13 SWAP6 SWAP15 SWAP14 SWAP11 SWAP9 SWAP7 SWAP4 SWAP6 PUSH2 0x12A9 SWAP4 PUSH1 0x40 PUSH2 0x180 SWAP3 DUP4 SWAP3 DUP2 MSTORE PUSH1 0x1 DUP1 PUSH1 0xA0 SHL SUB DUP1 SWAP11 AND PUSH1 0x20 DUP3 ADD MSTORE ADD MSTORE DUP14 ADD SWAP1 PUSH2 0x1218 JUMP JUMPDEST SWAP13 PUSH1 0x60 DUP13 ADD SWAP1 PUSH2 0x1258 JUMP JUMPDEST PUSH1 0x80 DUP11 ADD MSTORE AND PUSH1 0xA0 DUP9 ADD MSTORE PUSH1 0xC0 DUP8 ADD MSTORE PUSH1 0xE0 DUP7 ADD MSTORE PUSH2 0x100 DUP6 ADD MSTORE PUSH2 0x120 DUP5 ADD MSTORE PUSH1 0x5 DUP3 LT ISZERO PUSH2 0xB95 JUMPI PUSH2 0x160 SWAP2 PUSH2 0x140 DUP5 ADD MSTORE ISZERO ISZERO SWAP2 ADD MSTORE JUMP JUMPDEST PUSH1 0x24 CALLDATALOAD SWAP1 PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB DUP3 AND DUP3 SUB PUSH2 0x1C0 JUMPI JUMP JUMPDEST PUSH1 0x4 CALLDATALOAD SWAP1 PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB DUP3 AND DUP3 SUB PUSH2 0x1C0 JUMPI JUMP JUMPDEST PUSH8 0xFFFFFFFFFFFFFFFF DUP2 GT PUSH2 0xC76 JUMPI PUSH1 0x1F ADD PUSH1 0x1F NOT AND PUSH1 0x20 ADD SWAP1 JUMP JUMPDEST SWAP2 SWAP1 DUP3 ADD DUP1 SWAP3 GT PUSH2 0xC8C JUMPI JUMP JUMPDEST ISZERO PUSH2 0x1340 JUMPI JUMP JUMPDEST PUSH1 0x40 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE PUSH1 0x20 PUSH1 0x4 DUP3 ADD MSTORE PUSH1 0x13 PUSH1 0x24 DUP3 ADD MSTORE PUSH19 0x50726F706F73616C206E6F7420616374697665 PUSH1 0x68 SHL PUSH1 0x44 DUP3 ADD MSTORE PUSH1 0x64 SWAP1 REVERT JUMPDEST PUSH1 0x0 SWAP1 DUP2 MSTORE PUSH1 0x3 PUSH1 0x20 MSTORE PUSH1 0x40 DUP2 KECCAK256 SWAP1 PUSH1 0xA DUP3 ADD SWAP2 DUP3 SLOAD SWAP2 PUSH1 0xFF DUP4 AND PUSH1 0x5 DUP2 LT ISZERO PUSH2 0x14BA JUMPI PUSH1 0x1 PUSH2 0x13AB SWAP2 EQ PUSH2 0x1339 JUMP JUMPDEST PUSH1 0x9 DUP3 ADD SLOAD TIMESTAMP GT PUSH2 0x13BD JUMPI JUMPDEST POP POP POP POP JUMP JUMPDEST PUSH1 0x7 PUSH1 0x6 DUP4 ADD SLOAD SWAP3 ADD SLOAD SWAP1 PUSH1 0x4 PUSH2 0x13D4 DUP4 DUP6 PUSH2 0x132C JUMP JUMPDEST PUSH1 0x2 SLOAD PUSH1 0x40 MLOAD PUSH4 0x18160DDD PUSH1 0xE0 SHL DUP2 MSTORE SWAP2 SWAP4 SWAP2 SWAP3 PUSH1 0x20 SWAP2 DUP5 SWAP2 SWAP1 DUP3 SWAP1 PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB AND GAS STATICCALL SWAP2 DUP3 ISZERO PUSH2 0x14AD JUMPI DUP2 SWAP3 PUSH2 0x1475 JUMPI JUMPDEST POP PUSH1 0x9 SLOAD SWAP2 DUP3 DUP2 MUL SWAP3 DUP2 DUP5 DIV EQ SWAP1 ISZERO OR ISZERO PUSH2 0x1461 JUMPI POP PUSH1 0x64 SWAP1 DIV GT ISZERO SWAP2 DUP3 PUSH2 0x1457 JUMPI JUMPDEST POP POP ISZERO PUSH2 0x1449 JUMPI POP DUP1 SLOAD PUSH1 0xFF NOT AND PUSH1 0x2 OR SWAP1 SSTORE JUMPDEST CODESIZE DUP1 DUP1 DUP1 PUSH2 0x13B7 JUMP JUMPDEST PUSH1 0xFF NOT AND PUSH1 0x3 OR SWAP1 SSTORE PUSH2 0x1440 JUMP JUMPDEST GT SWAP1 POP CODESIZE DUP1 PUSH2 0x142C JUMP JUMPDEST PUSH4 0x4E487B71 PUSH1 0xE0 SHL DUP2 MSTORE PUSH1 0x11 PUSH1 0x4 MSTORE PUSH1 0x24 SWAP1 REVERT JUMPDEST SWAP1 SWAP2 POP PUSH1 0x20 DUP2 RETURNDATASIZE PUSH1 0x20 GT PUSH2 0x14A5 JUMPI JUMPDEST DUP2 PUSH2 0x1491 PUSH1 0x20 SWAP4 DUP4 PUSH2 0x1151 JUMP JUMPDEST DUP2 ADD SUB SLT PUSH2 0x14A1 JUMPI MLOAD SWAP1 CODESIZE PUSH2 0x1409 JUMP JUMPDEST POP DUP1 REVERT JUMPDEST RETURNDATASIZE SWAP2 POP PUSH2 0x1484 JUMP JUMPDEST POP PUSH1 0x40 MLOAD SWAP1 RETURNDATASIZE SWAP1 DUP3 RETURNDATACOPY RETURNDATASIZE SWAP1 REVERT JUMPDEST PUSH4 0x4E487B71 PUSH1 0xE0 SHL DUP3 MSTORE PUSH1 0x21 PUSH1 0x4 MSTORE PUSH1 0x24 DUP3 REVERT JUMPDEST PUSH1 0x2 PUSH1 0x1 SLOAD EQ PUSH2 0x14DF JUMPI PUSH1 0x2 PUSH1 0x1 SSTORE JUMP JUMPDEST PUSH1 0x40 MLOAD PUSH4 0x3EE5AEB5 PUSH1 0xE0 SHL DUP2 MSTORE PUSH1 0x4 SWAP1 REVERT JUMPDEST PUSH1 0x0 SLOAD PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB AND CALLER SUB PUSH2 0x1505 JUMPI JUMP JUMPDEST PUSH1 0x40 MLOAD PUSH4 0x118CDAA7 PUSH1 0xE0 SHL DUP2 MSTORE CALLER PUSH1 0x4 DUP3 ADD MSTORE PUSH1 0x24 SWAP1 REVERT INVALID LOG2 PUSH5 0x6970667358 0x22 SLT KECCAK256 0xA8 PUSH3 0xF00043 SWAP10 SWAP6 0xE CREATE2 DUP5 0x23 0xA8 SWAP7 KECCAK256 CALLDATACOPY OR 0xA9 LOG0 SWAP10 0x27 GT 0xCD 0xED 0x24 0xC0 0xDA 0xB9 KECCAK256 MOD NUMBER STATICCALL 0xE2 PUSH5 0x736F6C6343 STOP ADDMOD XOR STOP CALLER ", "sourceMap": "411:8158:4:-:0;;;;;;;;;;;;;-1:-1:-1;;411:8158:4;;;;-1:-1:-1;;;;;411:8158:4;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;-1:-1:-1;;;;;411:8158:4;;;;;;;:::i;:::-;;1273:26:0;;;1269:95;;-1:-1:-1;411:8158:4;;;;;;;;;;;;;;-1:-1:-1;411:8158:4;;;;;;3052:40:0;-1:-1:-1;3052:40:0;;1857:1:3;411:8158:4;;1352:6;;411:8158;1399:13;;411:8158;1467:1;;411:8158;;;2227:42;411:8158;;;2227:42;411:8158;;;;;;;;1269:95:0;411:8158:4;;-1:-1:-1;;;1322:31:0;;-1:-1:-1;1322:31:0;;;411:8158:4;;;1322:31:0;411:8158:4;-1:-1:-1;411:8158:4;;;;;;-1:-1:-1;411:8158:4;;;;;-1:-1:-1;411:8158:4;;;;-1:-1:-1;;;;;411:8158:4;;;;;;:::o"}, "deployedBytecode": {"functionDebugData": {"abi_decode_address": {"entryPoint": 4858, "id": null, "parameterSlots": 0, "returnSlots": 1}, "abi_decode_address_17500": {"entryPoint": 4836, "id": null, "parameterSlots": 0, "returnSlots": 1}, "abi_encode_enum_ProposalType": {"entryPoint": 4696, "id": null, "parameterSlots": 2, "returnSlots": 0}, "abi_encode_string": {"entryPoint": 4632, "id": null, "parameterSlots": 2, "returnSlots": 1}, "abi_encode_uint256_address_string_enum_ProposalType_uint256_address_uint256_uint256_uint256_uint256_enum_ProposalState_bool": {"entryPoint": 4709, "id": null, "parameterSlots": 13, "returnSlots": 1}, "array_allocation_size_string": {"entryPoint": 4880, "id": null, "parameterSlots": 1, "returnSlots": 1}, "checked_add_uint256": {"entryPoint": 4908, "id": null, "parameterSlots": 2, "returnSlots": 1}, "copy_array_from_storage_to_memory_string": {"entryPoint": 4467, "id": null, "parameterSlots": 1, "returnSlots": 1}, "extract_byte_array_length": {"entryPoint": 4375, "id": null, "parameterSlots": 1, "returnSlots": 1}, "finalize_allocation": {"entryPoint": 4433, "id": null, "parameterSlots": 2, "returnSlots": 0}, "fun_checkOwner": {"entryPoint": 5361, "id": 84, "parameterSlots": 0, "returnSlots": 0}, "fun_nonReentrantBefore": {"entryPoint": 5326, "id": 304, "parameterSlots": 0, "returnSlots": 0}, "fun_updateProposalState": {"entryPoint": 4987, "id": 725, "parameterSlots": 1, "returnSlots": 0}, "require_helper_stringliteral": {"entryPoint": 4921, "id": null, "parameterSlots": 1, "returnSlots": 0}}, "generatedSources": [], "immutableReferences": {}, "linkReferences": {}, "object": "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", "opcodes": "PUSH1 0x80 PUSH1 0x40 MSTORE PUSH1 0x4 CALLDATASIZE LT ISZERO PUSH2 0x71 JUMPI JUMPDEST CALLDATASIZE ISZERO PUSH2 0x19 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST PUSH2 0x25 CALLVALUE PUSH1 0xA SLOAD PUSH2 0x132C JUMP JUMPDEST PUSH1 0xA SSTORE CALLER PUSH1 0x0 MSTORE PUSH1 0xB PUSH1 0x20 MSTORE PUSH1 0x40 PUSH1 0x0 KECCAK256 PUSH2 0x41 CALLVALUE DUP3 SLOAD PUSH2 0x132C JUMP JUMPDEST SWAP1 SSTORE PUSH1 0x40 MLOAD CALLVALUE DUP2 MSTORE PUSH32 0x543BA50A5EEC5E6178218E364B1D0F396157B3C8FA278522C2CB7FD99407D474 PUSH1 0x20 CALLER SWAP3 LOG2 STOP JUMPDEST PUSH1 0x0 CALLDATALOAD PUSH1 0xE0 SHR DUP1 PUSH4 0x13CF08B EQ PUSH2 0x108D JUMPI DUP1 PUSH4 0x2A251A3 EQ PUSH2 0x106F JUMPI DUP1 PUSH4 0x3C7881A EQ PUSH2 0xFFF JUMPI DUP1 PUSH4 0xD61B519 EQ PUSH2 0xD6D JUMPI DUP1 PUSH4 0x1A216BBD EQ PUSH2 0x891 JUMPI DUP1 PUSH4 0x313DAB20 EQ PUSH2 0x873 JUMPI DUP1 PUSH4 0x32F6A1DC EQ PUSH2 0x810 JUMPI DUP1 PUSH4 0x43859632 EQ PUSH2 0x7C3 JUMPI DUP1 PUSH4 0x4FA76EC9 EQ PUSH2 0x7A5 JUMPI DUP1 PUSH4 0x6AC600C7 EQ PUSH2 0x787 JUMPI DUP1 PUSH4 0x715018A6 EQ PUSH2 0x72E JUMPI DUP1 PUSH4 0x8DA5CB5B EQ PUSH2 0x705 JUMPI DUP1 PUSH4 0xA93271AF EQ PUSH2 0x6CB JUMPI DUP1 PUSH4 0xB58131B0 EQ PUSH2 0x6AD JUMPI DUP1 PUSH4 0xC7F758A8 EQ PUSH2 0x61E JUMPI DUP1 PUSH4 0xC9D27AFE EQ PUSH2 0x3C6 JUMPI DUP1 PUSH4 0xDA35C664 EQ PUSH2 0x3A8 JUMPI DUP1 PUSH4 0xE2C41DBC EQ PUSH2 0x2F5 JUMPI DUP1 PUSH4 0xEA0217CF EQ PUSH2 0x298 JUMPI DUP1 PUSH4 0xECE40CC1 EQ PUSH2 0x277 JUMPI DUP1 PUSH4 0xF2FDE38B EQ PUSH2 0x1EE JUMPI DUP1 PUSH4 0xF96DAE0A EQ PUSH2 0x1C5 JUMPI PUSH4 0xFB468855 SUB PUSH2 0xE JUMPI CALLVALUE PUSH2 0x1C0 JUMPI PUSH1 0x40 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x1C0 JUMPI PUSH2 0x18C PUSH2 0x12E4 JUMP JUMPDEST PUSH1 0x4 CALLDATALOAD PUSH1 0x0 MSTORE PUSH1 0x5 PUSH1 0x20 MSTORE PUSH1 0x40 PUSH1 0x0 KECCAK256 SWAP1 PUSH1 0x1 DUP1 PUSH1 0xA0 SHL SUB AND PUSH1 0x0 MSTORE PUSH1 0x20 MSTORE PUSH1 0x20 PUSH1 0xFF PUSH1 0x40 PUSH1 0x0 KECCAK256 SLOAD AND PUSH1 0x40 MLOAD SWAP1 ISZERO ISZERO DUP2 MSTORE RETURN JUMPDEST PUSH1 0x0 DUP1 REVERT JUMPDEST CALLVALUE PUSH2 0x1C0 JUMPI PUSH1 0x0 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x1C0 JUMPI PUSH1 0x2 SLOAD PUSH1 0x40 MLOAD PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB SWAP1 SWAP2 AND DUP2 MSTORE PUSH1 0x20 SWAP1 RETURN JUMPDEST CALLVALUE PUSH2 0x1C0 JUMPI PUSH1 0x20 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x1C0 JUMPI PUSH2 0x207 PUSH2 0x12FA JUMP JUMPDEST PUSH2 0x20F PUSH2 0x14F1 JUMP JUMPDEST PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB SWAP1 DUP2 AND SWAP1 DUP2 ISZERO PUSH2 0x25E JUMPI PUSH1 0x0 SLOAD DUP3 PUSH1 0x1 PUSH1 0x1 PUSH1 0x60 SHL SUB PUSH1 0xA0 SHL DUP3 AND OR PUSH1 0x0 SSTORE AND PUSH32 0x8BE0079C531659141344CD1FD0A4F28419497F9722A3DAAFE3B4186F6B6457E0 PUSH1 0x0 DUP1 LOG3 STOP JUMPDEST PUSH1 0x40 MLOAD PUSH4 0x1E4FBDF7 PUSH1 0xE0 SHL DUP2 MSTORE PUSH1 0x0 PUSH1 0x4 DUP3 ADD MSTORE PUSH1 0x24 SWAP1 REVERT JUMPDEST CALLVALUE PUSH2 0x1C0 JUMPI PUSH1 0x20 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x1C0 JUMPI PUSH2 0x290 PUSH2 0x14F1 JUMP JUMPDEST PUSH1 0x4 CALLDATALOAD PUSH1 0x8 SSTORE STOP JUMPDEST CALLVALUE PUSH2 0x1C0 JUMPI PUSH1 0x20 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x1C0 JUMPI PUSH1 0x4 CALLDATALOAD PUSH2 0x2B4 PUSH2 0x14F1 JUMP JUMPDEST DUP1 ISZERO PUSH2 0x2BF JUMPI PUSH1 0x7 SSTORE STOP JUMPDEST PUSH1 0x40 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE PUSH1 0x20 PUSH1 0x4 DUP3 ADD MSTORE PUSH1 0xE PUSH1 0x24 DUP3 ADD MSTORE PUSH14 0x125B9D985B1A59081C195C9A5BD9 PUSH1 0x92 SHL PUSH1 0x44 DUP3 ADD MSTORE PUSH1 0x64 SWAP1 REVERT JUMPDEST PUSH1 0x0 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x1C0 JUMPI PUSH2 0x309 PUSH2 0x14CE JUMP JUMPDEST CALLVALUE ISZERO PUSH2 0x36B JUMPI PUSH2 0x31B CALLVALUE PUSH1 0xA SLOAD PUSH2 0x132C JUMP JUMPDEST PUSH1 0xA SSTORE CALLER PUSH1 0x0 MSTORE PUSH1 0xB PUSH1 0x20 MSTORE PUSH1 0x40 PUSH1 0x0 KECCAK256 PUSH2 0x337 CALLVALUE DUP3 SLOAD PUSH2 0x132C JUMP JUMPDEST SWAP1 SSTORE PUSH1 0x40 MLOAD CALLVALUE DUP2 MSTORE PUSH32 0x543BA50A5EEC5E6178218E364B1D0F396157B3C8FA278522C2CB7FD99407D474 PUSH1 0x20 CALLER SWAP3 LOG2 PUSH1 0x1 DUP1 SSTORE STOP JUMPDEST PUSH1 0x40 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE PUSH1 0x20 PUSH1 0x4 DUP3 ADD MSTORE PUSH1 0x15 PUSH1 0x24 DUP3 ADD MSTORE PUSH21 0x9AEAE6E840C8CAE0DEE6D2E840E6DEDACA408AA89 PUSH1 0x5B SHL PUSH1 0x44 DUP3 ADD MSTORE PUSH1 0x64 SWAP1 REVERT JUMPDEST CALLVALUE PUSH2 0x1C0 JUMPI PUSH1 0x0 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x1C0 JUMPI PUSH1 0x20 PUSH1 0x6 SLOAD PUSH1 0x40 MLOAD SWAP1 DUP2 MSTORE RETURN JUMPDEST CALLVALUE PUSH2 0x1C0 JUMPI PUSH1 0x40 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x1C0 JUMPI PUSH1 0x24 DUP1 CALLDATALOAD DUP1 ISZERO ISZERO SWAP2 PUSH1 0x4 CALLDATALOAD SWAP2 DUP4 DUP2 SUB PUSH2 0x1C0 JUMPI DUP3 PUSH1 0x0 MSTORE PUSH1 0x20 SWAP1 PUSH1 0x3 DUP3 MSTORE PUSH1 0x40 PUSH1 0x0 KECCAK256 SWAP1 PUSH1 0xFF PUSH1 0xA DUP4 ADD SLOAD AND PUSH1 0x5 DUP2 LT ISZERO PUSH2 0x609 JUMPI PUSH1 0x1 PUSH2 0x418 SWAP2 EQ PUSH2 0x1339 JUMP JUMPDEST PUSH1 0x9 DUP3 ADD SLOAD TIMESTAMP GT PUSH2 0x5CF JUMPI DUP5 PUSH1 0x0 MSTORE PUSH1 0x4 DUP4 MSTORE PUSH1 0x40 PUSH1 0x0 KECCAK256 CALLER PUSH1 0x0 MSTORE DUP4 MSTORE PUSH1 0xFF PUSH1 0x40 PUSH1 0x0 KECCAK256 SLOAD AND PUSH2 0x59B JUMPI PUSH1 0x2 SLOAD PUSH1 0x40 MLOAD PUSH4 0x70A08231 PUSH1 0xE0 SHL DUP2 MSTORE CALLER PUSH1 0x4 DUP3 ADD MSTORE SWAP5 SWAP1 DUP5 SWAP1 DUP7 SWAP1 DUP4 SWAP1 DUP3 SWAP1 PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB AND GAS STATICCALL SWAP5 DUP6 ISZERO PUSH2 0x58F JUMPI PUSH1 0x0 SWAP6 PUSH2 0x560 JUMPI JUMPDEST POP DUP5 ISZERO PUSH2 0x52B JUMPI POP DUP5 PUSH1 0x0 MSTORE PUSH1 0x4 DUP4 MSTORE PUSH1 0x40 PUSH1 0x0 KECCAK256 CALLER PUSH1 0x0 MSTORE DUP4 MSTORE PUSH1 0x40 PUSH1 0x0 KECCAK256 PUSH1 0xFF NOT SWAP1 PUSH1 0x1 DUP3 DUP3 SLOAD AND OR SWAP1 SSTORE DUP6 PUSH1 0x0 MSTORE PUSH1 0x5 DUP5 MSTORE PUSH1 0x40 PUSH1 0x0 KECCAK256 CALLER PUSH1 0x0 MSTORE DUP5 MSTORE PUSH1 0x40 PUSH1 0x0 KECCAK256 SWAP1 DUP2 SLOAD AND PUSH1 0xFF DUP9 AND OR SWAP1 SSTORE PUSH1 0x0 EQ PUSH2 0x516 JUMPI PUSH1 0x6 ADD PUSH2 0x4E2 DUP4 DUP3 SLOAD PUSH2 0x132C JUMP JUMPDEST SWAP1 SSTORE JUMPDEST PUSH1 0x40 MLOAD SWAP4 DUP5 MSTORE DUP4 ADD MSTORE PUSH32 0xCBDF6214089CBA887ECBF35A0B6A734589959C9763342C756BB2A80CA2BC9F6E PUSH1 0x40 CALLER SWAP4 LOG3 STOP JUMPDEST PUSH1 0x7 ADD PUSH2 0x524 DUP4 DUP3 SLOAD PUSH2 0x132C JUMP JUMPDEST SWAP1 SSTORE PUSH2 0x4E5 JUMP JUMPDEST DUP4 PUSH1 0xF PUSH1 0x64 SWAP3 PUSH1 0x40 MLOAD SWAP3 PUSH3 0x461BCD PUSH1 0xE5 SHL DUP5 MSTORE PUSH1 0x4 DUP5 ADD MSTORE DUP3 ADD MSTORE PUSH15 0x2737903B37BA34B733903837BBB2B9 PUSH1 0x89 SHL PUSH1 0x44 DUP3 ADD MSTORE REVERT JUMPDEST SWAP1 SWAP5 POP DUP4 DUP2 DUP2 RETURNDATASIZE DUP4 GT PUSH2 0x588 JUMPI JUMPDEST PUSH2 0x578 DUP2 DUP4 PUSH2 0x1151 JUMP JUMPDEST DUP2 ADD SUB SLT PUSH2 0x1C0 JUMPI MLOAD SWAP4 DUP8 PUSH2 0x47D JUMP JUMPDEST POP RETURNDATASIZE PUSH2 0x56E JUMP JUMPDEST PUSH1 0x40 MLOAD RETURNDATASIZE PUSH1 0x0 DUP3 RETURNDATACOPY RETURNDATASIZE SWAP1 REVERT JUMPDEST PUSH1 0x40 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE PUSH1 0x4 DUP2 ADD DUP5 SWAP1 MSTORE PUSH1 0xD DUP2 DUP7 ADD MSTORE PUSH13 0x105B1C9958591E481D9BDD1959 PUSH1 0x9A SHL PUSH1 0x44 DUP3 ADD MSTORE PUSH1 0x64 SWAP1 REVERT JUMPDEST PUSH1 0x40 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE PUSH1 0x4 DUP2 ADD DUP5 SWAP1 MSTORE PUSH1 0x13 DUP2 DUP7 ADD MSTORE PUSH19 0x159BDD1A5B99C81C195C9A5BD908195B991959 PUSH1 0x6A SHL PUSH1 0x44 DUP3 ADD MSTORE PUSH1 0x64 SWAP1 REVERT JUMPDEST DUP5 PUSH4 0x4E487B71 PUSH1 0xE0 SHL PUSH1 0x0 MSTORE PUSH1 0x21 PUSH1 0x4 MSTORE PUSH1 0x0 REVERT JUMPDEST CALLVALUE PUSH2 0x1C0 JUMPI PUSH1 0x20 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x1C0 JUMPI PUSH1 0x4 CALLDATALOAD PUSH1 0x0 MSTORE PUSH1 0x3 PUSH1 0x20 MSTORE PUSH1 0x40 PUSH1 0x0 KECCAK256 DUP1 SLOAD PUSH2 0x6A9 PUSH1 0x1 DUP1 PUSH1 0xA0 SHL SUB SWAP3 DUP4 PUSH1 0x1 DUP3 ADD SLOAD AND SWAP3 PUSH1 0xFF PUSH1 0x3 DUP4 ADD SLOAD AND SWAP5 PUSH1 0x4 DUP4 ADD SLOAD SWAP1 PUSH1 0x5 DUP5 ADD SLOAD AND PUSH1 0x6 DUP5 ADD SLOAD PUSH1 0x7 DUP6 ADD SLOAD SWAP1 PUSH1 0x8 DUP7 ADD SLOAD SWAP3 PUSH1 0x9 DUP8 ADD SLOAD SWAP5 PUSH2 0x691 PUSH1 0x2 PUSH1 0xA DUP11 ADD SLOAD SWAP10 ADD PUSH2 0x1173 JUMP JUMPDEST SWAP11 PUSH1 0x40 MLOAD SWAP12 DUP13 SWAP12 PUSH1 0xFF DUP1 DUP13 PUSH1 0x8 SHR AND SWAP12 AND SWAP10 DUP14 PUSH2 0x1265 JUMP JUMPDEST SUB SWAP1 RETURN JUMPDEST CALLVALUE PUSH2 0x1C0 JUMPI PUSH1 0x0 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x1C0 JUMPI PUSH1 0x20 PUSH1 0x8 SLOAD PUSH1 0x40 MLOAD SWAP1 DUP2 MSTORE RETURN JUMPDEST CALLVALUE PUSH2 0x1C0 JUMPI PUSH1 0x20 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x1C0 JUMPI PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB PUSH2 0x6EC PUSH2 0x12FA JUMP JUMPDEST AND PUSH1 0x0 MSTORE PUSH1 0xB PUSH1 0x20 MSTORE PUSH1 0x20 PUSH1 0x40 PUSH1 0x0 KECCAK256 SLOAD PUSH1 0x40 MLOAD SWAP1 DUP2 MSTORE RETURN JUMPDEST CALLVALUE PUSH2 0x1C0 JUMPI PUSH1 0x0 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x1C0 JUMPI PUSH1 0x0 SLOAD PUSH1 0x40 MLOAD PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB SWAP1 SWAP2 AND DUP2 MSTORE PUSH1 0x20 SWAP1 RETURN JUMPDEST CALLVALUE PUSH2 0x1C0 JUMPI PUSH1 0x0 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x1C0 JUMPI PUSH2 0x747 PUSH2 0x14F1 JUMP JUMPDEST PUSH1 0x0 DUP1 SLOAD PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB NOT DUP2 AND DUP3 SSTORE PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB AND PUSH32 0x8BE0079C531659141344CD1FD0A4F28419497F9722A3DAAFE3B4186F6B6457E0 DUP3 DUP1 LOG3 STOP JUMPDEST CALLVALUE PUSH2 0x1C0 JUMPI PUSH1 0x20 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x1C0 JUMPI PUSH2 0x7A3 PUSH1 0x4 CALLDATALOAD PUSH2 0x137B JUMP JUMPDEST STOP JUMPDEST CALLVALUE PUSH2 0x1C0 JUMPI PUSH1 0x0 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x1C0 JUMPI PUSH1 0x20 PUSH1 0x9 SLOAD PUSH1 0x40 MLOAD SWAP1 DUP2 MSTORE RETURN JUMPDEST CALLVALUE PUSH2 0x1C0 JUMPI PUSH1 0x40 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x1C0 JUMPI PUSH2 0x7DC PUSH2 0x12E4 JUMP JUMPDEST PUSH1 0x4 CALLDATALOAD PUSH1 0x0 MSTORE PUSH1 0x4 PUSH1 0x20 MSTORE PUSH1 0x40 PUSH1 0x0 KECCAK256 SWAP1 PUSH1 0x1 DUP1 PUSH1 0xA0 SHL SUB AND PUSH1 0x0 MSTORE PUSH1 0x20 MSTORE PUSH1 0x20 PUSH1 0xFF PUSH1 0x40 PUSH1 0x0 KECCAK256 SLOAD AND PUSH1 0x40 MLOAD SWAP1 ISZERO ISZERO DUP2 MSTORE RETURN JUMPDEST CALLVALUE PUSH2 0x1C0 JUMPI PUSH1 0x20 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x1C0 JUMPI PUSH1 0x4 CALLDATALOAD PUSH2 0x82C PUSH2 0x14F1 JUMP JUMPDEST PUSH1 0x64 DUP2 GT PUSH2 0x839 JUMPI PUSH1 0x9 SSTORE STOP JUMPDEST PUSH1 0x40 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE PUSH1 0x20 PUSH1 0x4 DUP3 ADD MSTORE PUSH1 0x12 PUSH1 0x24 DUP3 ADD MSTORE PUSH18 0x496E76616C69642070657263656E74616765 PUSH1 0x70 SHL PUSH1 0x44 DUP3 ADD MSTORE PUSH1 0x64 SWAP1 REVERT JUMPDEST CALLVALUE PUSH2 0x1C0 JUMPI PUSH1 0x0 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x1C0 JUMPI PUSH1 0x20 PUSH1 0xA SLOAD PUSH1 0x40 MLOAD SWAP1 DUP2 MSTORE RETURN JUMPDEST CALLVALUE PUSH2 0x1C0 JUMPI PUSH1 0x80 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x1C0 JUMPI PUSH1 0x4 CALLDATALOAD PUSH8 0xFFFFFFFFFFFFFFFF DUP2 GT PUSH2 0x1C0 JUMPI CALLDATASIZE PUSH1 0x23 DUP3 ADD SLT ISZERO PUSH2 0x1C0 JUMPI DUP1 PUSH1 0x4 ADD CALLDATALOAD PUSH2 0x8CD DUP2 PUSH2 0x1310 JUMP JUMPDEST SWAP2 PUSH2 0x8DB PUSH1 0x40 MLOAD SWAP4 DUP5 PUSH2 0x1151 JUMP JUMPDEST DUP2 DUP4 MSTORE CALLDATASIZE PUSH1 0x24 DUP4 DUP4 ADD ADD GT PUSH2 0x1C0 JUMPI DUP2 PUSH1 0x0 SWAP3 PUSH1 0x24 PUSH1 0x20 SWAP4 ADD DUP4 DUP7 ADD CALLDATACOPY DUP4 ADD ADD MSTORE PUSH1 0x3 PUSH1 0x24 CALLDATALOAD LT ISZERO PUSH2 0x1C0 JUMPI PUSH1 0x64 CALLDATALOAD SWAP1 PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB DUP3 AND DUP3 SUB PUSH2 0x1C0 JUMPI DUP1 MLOAD ISZERO PUSH2 0xD28 JUMPI PUSH1 0x2 SLOAD PUSH1 0x40 MLOAD PUSH4 0x70A08231 PUSH1 0xE0 SHL DUP2 MSTORE CALLER PUSH1 0x4 DUP3 ADD MSTORE SWAP1 PUSH1 0x20 SWAP1 DUP3 SWAP1 PUSH1 0x24 SWAP1 DUP3 SWAP1 PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB AND GAS STATICCALL SWAP1 DUP2 ISZERO PUSH2 0x58F JUMPI PUSH1 0x0 SWAP2 PUSH2 0xCF6 JUMPI JUMPDEST POP PUSH1 0x8 SLOAD GT PUSH2 0xCA2 JUMPI PUSH1 0x6 SLOAD SWAP1 PUSH1 0x0 NOT DUP3 EQ PUSH2 0xC8C JUMPI PUSH1 0x1 DUP3 ADD PUSH1 0x6 SSTORE PUSH2 0x986 PUSH1 0x7 SLOAD TIMESTAMP PUSH2 0x132C JUMP JUMPDEST PUSH1 0x40 MLOAD SWAP4 DUP5 PUSH2 0x180 DUP2 ADD LT PUSH8 0xFFFFFFFFFFFFFFFF PUSH2 0x180 DUP8 ADD GT OR PUSH2 0xC76 JUMPI PUSH2 0x180 DUP6 ADD PUSH1 0x40 MSTORE DUP4 DUP6 MSTORE CALLER PUSH1 0x20 DUP7 ADD MSTORE DUP3 PUSH1 0x40 DUP7 ADD MSTORE PUSH1 0x24 CALLDATALOAD PUSH1 0x60 DUP7 ADD MSTORE PUSH1 0x44 CALLDATALOAD PUSH1 0x80 DUP7 ADD MSTORE PUSH1 0x1 DUP1 PUSH1 0xA0 SHL SUB AND PUSH1 0xA0 DUP6 ADD MSTORE PUSH1 0x0 PUSH1 0xC0 DUP6 ADD MSTORE PUSH1 0x0 PUSH1 0xE0 DUP6 ADD MSTORE TIMESTAMP PUSH2 0x100 DUP6 ADD MSTORE PUSH2 0x120 DUP5 ADD MSTORE PUSH1 0x1 PUSH2 0x140 DUP5 ADD MSTORE PUSH1 0x0 PUSH2 0x160 DUP5 ADD MSTORE DUP2 PUSH1 0x0 MSTORE PUSH1 0x3 PUSH1 0x20 MSTORE PUSH1 0x40 PUSH1 0x0 KECCAK256 DUP4 MLOAD DUP2 SSTORE PUSH1 0x1 DUP2 ADD PUSH1 0x1 DUP1 PUSH1 0xA0 SHL SUB PUSH1 0x20 DUP7 ADD MLOAD AND PUSH1 0x1 PUSH1 0x1 PUSH1 0x60 SHL SUB PUSH1 0xA0 SHL DUP3 SLOAD AND OR SWAP1 SSTORE PUSH1 0x40 DUP5 ADD MLOAD DUP1 MLOAD SWAP1 PUSH8 0xFFFFFFFFFFFFFFFF DUP3 GT PUSH2 0xC76 JUMPI DUP2 SWAP1 PUSH2 0xA5E PUSH1 0x2 DUP6 ADD SLOAD PUSH2 0x1117 JUMP JUMPDEST PUSH1 0x1F DUP2 GT PUSH2 0xC23 JUMPI JUMPDEST POP PUSH1 0x20 SWAP1 PUSH1 0x1F DUP4 GT PUSH1 0x1 EQ PUSH2 0xBB6 JUMPI PUSH1 0x0 SWAP3 PUSH2 0xBAB JUMPI JUMPDEST POP POP DUP2 PUSH1 0x1 SHL SWAP2 PUSH1 0x0 NOT SWAP1 PUSH1 0x3 SHL SHR NOT AND OR PUSH1 0x2 DUP3 ADD SSTORE JUMPDEST PUSH1 0x3 DUP2 ADD SWAP1 PUSH1 0x60 DUP6 ADD MLOAD SWAP2 PUSH1 0x3 DUP4 LT ISZERO PUSH2 0xB95 JUMPI PUSH1 0xA SWAP3 PUSH1 0xFF DUP1 NOT DUP4 SLOAD AND SWAP2 AND OR SWAP1 SSTORE PUSH1 0x80 DUP6 ADD MLOAD PUSH1 0x4 DUP3 ADD SSTORE PUSH1 0x5 DUP2 ADD PUSH1 0x1 DUP1 PUSH1 0xA0 SHL SUB PUSH1 0xA0 DUP8 ADD MLOAD AND PUSH1 0x1 PUSH1 0x1 PUSH1 0x60 SHL SUB PUSH1 0xA0 SHL DUP3 SLOAD AND OR SWAP1 SSTORE PUSH1 0xC0 DUP6 ADD MLOAD PUSH1 0x6 DUP3 ADD SSTORE PUSH1 0xE0 DUP6 ADD MLOAD PUSH1 0x7 DUP3 ADD SSTORE PUSH2 0x100 DUP6 ADD MLOAD PUSH1 0x8 DUP3 ADD SSTORE PUSH2 0x120 DUP6 ADD MLOAD PUSH1 0x9 DUP3 ADD SSTORE ADD PUSH2 0x140 DUP5 ADD MLOAD PUSH1 0x5 DUP2 LT ISZERO PUSH2 0xB95 JUMPI PUSH1 0x20 SWAP5 PUSH1 0xFF PUSH2 0xFF00 PUSH2 0x160 DUP6 SLOAD SWAP4 ADD MLOAD ISZERO ISZERO PUSH1 0x8 SHL AND SWAP3 AND SWAP1 PUSH2 0xFFFF NOT AND OR OR SWAP1 SSTORE DUP2 PUSH32 0xE0269F7953E70365E80E614E4EFE7D4BEDE59B7F5AD80982EA4064C97EE6F4EF PUSH2 0xB78 PUSH1 0x40 MLOAD SWAP4 PUSH1 0x40 DUP6 MSTORE PUSH1 0x40 DUP6 ADD SWAP1 PUSH2 0x1218 JUMP JUMPDEST SWAP3 PUSH2 0xB87 DUP7 DUP3 ADD PUSH1 0x24 CALLDATALOAD PUSH2 0x1258 JUMP JUMPDEST DUP1 CALLER SWAP5 SUB SWAP1 LOG3 PUSH1 0x40 MLOAD SWAP1 DUP2 MSTORE RETURN JUMPDEST PUSH4 0x4E487B71 PUSH1 0xE0 SHL PUSH1 0x0 MSTORE PUSH1 0x21 PUSH1 0x4 MSTORE PUSH1 0x24 PUSH1 0x0 REVERT JUMPDEST ADD MLOAD SWAP1 POP DUP7 DUP1 PUSH2 0xA7E JUMP JUMPDEST PUSH1 0x2 DUP6 ADD PUSH1 0x0 SWAP1 DUP2 MSTORE PUSH1 0x20 DUP2 KECCAK256 SWAP4 POP PUSH1 0x1F NOT DUP6 AND SWAP1 JUMPDEST DUP2 DUP2 LT PUSH2 0xC0B JUMPI POP SWAP1 DUP5 PUSH1 0x1 SWAP6 SWAP5 SWAP4 SWAP3 LT PUSH2 0xBF2 JUMPI JUMPDEST POP POP POP DUP2 SHL ADD PUSH1 0x2 DUP3 ADD SSTORE PUSH2 0xA96 JUMP JUMPDEST ADD MLOAD PUSH1 0x0 NOT PUSH1 0xF8 DUP5 PUSH1 0x3 SHL AND SHR NOT AND SWAP1 SSTORE DUP7 DUP1 DUP1 PUSH2 0xBE2 JUMP JUMPDEST SWAP3 SWAP4 PUSH1 0x20 PUSH1 0x1 DUP2 SWAP3 DUP8 DUP7 ADD MLOAD DUP2 SSTORE ADD SWAP6 ADD SWAP4 ADD PUSH2 0xBCC JUMP JUMPDEST SWAP1 SWAP2 POP PUSH1 0x2 DUP5 ADD PUSH1 0x0 MSTORE PUSH1 0x20 PUSH1 0x0 KECCAK256 PUSH1 0x1F DUP5 ADD PUSH1 0x5 SHR DUP2 ADD PUSH1 0x20 DUP6 LT PUSH2 0xC6F JUMPI JUMPDEST SWAP1 DUP5 SWAP4 SWAP3 SWAP2 JUMPDEST PUSH1 0x1F DUP4 ADD PUSH1 0x5 SHR DUP3 ADD DUP2 LT PUSH2 0xC60 JUMPI POP POP PUSH2 0xA67 JUMP JUMPDEST PUSH1 0x0 DUP2 SSTORE DUP6 SWAP5 POP PUSH1 0x1 ADD PUSH2 0xC4A JUMP JUMPDEST POP DUP1 PUSH2 0xC44 JUMP JUMPDEST PUSH4 0x4E487B71 PUSH1 0xE0 SHL PUSH1 0x0 MSTORE PUSH1 0x41 PUSH1 0x4 MSTORE PUSH1 0x24 PUSH1 0x0 REVERT JUMPDEST PUSH4 0x4E487B71 PUSH1 0xE0 SHL PUSH1 0x0 MSTORE PUSH1 0x11 PUSH1 0x4 MSTORE PUSH1 0x24 PUSH1 0x0 REVERT JUMPDEST PUSH1 0x40 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE PUSH1 0x20 PUSH1 0x4 DUP3 ADD MSTORE PUSH1 0x26 PUSH1 0x24 DUP3 ADD MSTORE PUSH32 0x496E73756666696369656E7420746F6B656E7320746F20637265617465207072 PUSH1 0x44 DUP3 ADD MSTORE PUSH6 0x1BDC1BDCD85B PUSH1 0xD2 SHL PUSH1 0x64 DUP3 ADD MSTORE PUSH1 0x84 SWAP1 REVERT JUMPDEST SWAP1 POP PUSH1 0x20 DUP2 RETURNDATASIZE PUSH1 0x20 GT PUSH2 0xD20 JUMPI JUMPDEST DUP2 PUSH2 0xD11 PUSH1 0x20 SWAP4 DUP4 PUSH2 0x1151 JUMP JUMPDEST DUP2 ADD SUB SLT PUSH2 0x1C0 JUMPI MLOAD DUP4 PUSH2 0x95D JUMP JUMPDEST RETURNDATASIZE SWAP2 POP PUSH2 0xD04 JUMP JUMPDEST PUSH1 0x40 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE PUSH1 0x20 PUSH1 0x4 DUP3 ADD MSTORE PUSH1 0x1B PUSH1 0x24 DUP3 ADD MSTORE PUSH32 0x4465736372697074696F6E2063616E6E6F7420626520656D7074790000000000 PUSH1 0x44 DUP3 ADD MSTORE PUSH1 0x64 SWAP1 REVERT JUMPDEST CALLVALUE PUSH2 0x1C0 JUMPI PUSH1 0x20 DUP1 PUSH1 0x3 NOT CALLDATASIZE ADD SLT PUSH2 0x1C0 JUMPI PUSH1 0x4 CALLDATALOAD SWAP1 PUSH2 0xD8B PUSH2 0x14CE JUMP JUMPDEST DUP2 PUSH1 0x0 MSTORE PUSH1 0x3 DUP2 MSTORE PUSH1 0x40 PUSH1 0x0 KECCAK256 SWAP1 PUSH2 0xDA2 DUP4 PUSH2 0x137B JUMP JUMPDEST PUSH1 0xA DUP3 ADD DUP1 SLOAD PUSH1 0xFF DUP2 AND PUSH1 0x5 DUP2 LT ISZERO PUSH2 0xB95 JUMPI PUSH1 0x2 SUB PUSH2 0xFC1 JUMPI PUSH1 0xFF DUP2 PUSH1 0x8 SHR AND PUSH2 0xF7C JUMPI PUSH2 0xFFFF NOT AND PUSH2 0x104 OR SWAP1 SSTORE PUSH1 0x3 DUP3 DUP2 ADD SLOAD PUSH1 0xFF AND SWAP1 DUP2 LT ISZERO PUSH2 0xB95 JUMPI PUSH1 0x1 EQ PUSH2 0xE17 JUMPI JUMPDEST DUP3 PUSH32 0x712AE1383F79AC853F8D882153778E0260EF8F03B504E2866E0593E04D2B291F PUSH1 0x0 DUP1 LOG2 PUSH1 0x1 DUP1 SSTORE STOP JUMPDEST PUSH1 0x4 DUP3 ADD SWAP2 DUP3 SLOAD SWAP1 PUSH1 0xA SLOAD SWAP1 DUP2 DUP4 GT PUSH2 0xF37 JUMPI PUSH1 0x5 ADD DUP1 SLOAD PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB SWAP4 SWAP2 SWAP3 SWAP2 SWAP1 DUP5 AND ISZERO PUSH2 0xEFC JUMPI DUP2 SUB SWAP1 DUP2 GT PUSH2 0xC8C JUMPI PUSH1 0xA SSTORE PUSH1 0x0 DUP1 DUP1 DUP1 DUP6 DUP6 SLOAD AND DUP9 SLOAD SWAP1 GAS CALL RETURNDATASIZE ISZERO PUSH2 0xEF7 JUMPI RETURNDATASIZE PUSH2 0xE6D DUP2 PUSH2 0x1310 JUMP JUMPDEST SWAP1 PUSH2 0xE7B PUSH1 0x40 MLOAD SWAP3 DUP4 PUSH2 0x1151 JUMP JUMPDEST DUP2 MSTORE PUSH1 0x0 DUP6 RETURNDATASIZE SWAP3 ADD RETURNDATACOPY JUMPDEST ISZERO PUSH2 0xEC0 JUMPI SWAP1 PUSH32 0xEAFF4B37086828766AD3268786972C0CD24259D4C87A80F9D3963A3C3D999B0D SWAP3 SWAP2 SLOAD AND SWAP3 SLOAD PUSH1 0x40 MLOAD SWAP1 DUP2 MSTORE LOG2 DUP2 DUP1 PUSH2 0xDEB JUMP JUMPDEST PUSH1 0x40 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE PUSH1 0x4 DUP2 ADD DUP5 SWAP1 MSTORE PUSH1 0xF PUSH1 0x24 DUP3 ADD MSTORE PUSH15 0x151C985B9CD9995C8819985A5B1959 PUSH1 0x8A SHL PUSH1 0x44 DUP3 ADD MSTORE PUSH1 0x64 SWAP1 REVERT JUMPDEST PUSH2 0xE85 JUMP JUMPDEST PUSH1 0x40 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE PUSH1 0x4 DUP2 ADD DUP7 SWAP1 MSTORE PUSH1 0x13 PUSH1 0x24 DUP3 ADD MSTORE PUSH19 0x496E76616C69642062656E6566696369617279 PUSH1 0x68 SHL PUSH1 0x44 DUP3 ADD MSTORE PUSH1 0x64 SWAP1 REVERT JUMPDEST PUSH1 0x40 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE PUSH1 0x4 DUP2 ADD DUP6 SWAP1 MSTORE PUSH1 0x1B PUSH1 0x24 DUP3 ADD MSTORE PUSH32 0x496E73756666696369656E742074726561737572792066756E64730000000000 PUSH1 0x44 DUP3 ADD MSTORE PUSH1 0x64 SWAP1 REVERT JUMPDEST PUSH1 0x40 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE PUSH1 0x4 DUP2 ADD DUP5 SWAP1 MSTORE PUSH1 0x19 PUSH1 0x24 DUP3 ADD MSTORE PUSH32 0x50726F706F73616C20616C726561647920657865637574656400000000000000 PUSH1 0x44 DUP3 ADD MSTORE PUSH1 0x64 SWAP1 REVERT JUMPDEST PUSH1 0x40 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE PUSH1 0x4 DUP2 ADD DUP5 SWAP1 MSTORE PUSH1 0x16 PUSH1 0x24 DUP3 ADD MSTORE PUSH22 0x141C9BDC1BDCD85B081B9BDD081CDD58D8D959591959 PUSH1 0x52 SHL PUSH1 0x44 DUP3 ADD MSTORE PUSH1 0x64 SWAP1 REVERT JUMPDEST CALLVALUE PUSH2 0x1C0 JUMPI PUSH1 0x40 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x1C0 JUMPI PUSH1 0x40 PUSH1 0x4 CALLDATALOAD PUSH2 0x101D PUSH2 0x12E4 JUMP JUMPDEST DUP2 PUSH1 0x0 MSTORE PUSH1 0x4 PUSH1 0x20 MSTORE DUP3 PUSH1 0x0 KECCAK256 SWAP1 PUSH1 0x1 DUP1 PUSH1 0xA0 SHL SUB AND SWAP1 DUP2 PUSH1 0x0 MSTORE PUSH1 0x20 MSTORE PUSH1 0xFF DUP4 PUSH1 0x0 KECCAK256 SLOAD AND SWAP2 PUSH1 0x0 MSTORE PUSH1 0x5 PUSH1 0x20 MSTORE DUP3 PUSH1 0x0 KECCAK256 SWAP1 PUSH1 0x0 MSTORE PUSH1 0x20 MSTORE PUSH1 0xFF DUP3 PUSH1 0x0 KECCAK256 SLOAD AND DUP3 MLOAD SWAP2 ISZERO ISZERO DUP3 MSTORE ISZERO ISZERO PUSH1 0x20 DUP3 ADD MSTORE RETURN JUMPDEST CALLVALUE PUSH2 0x1C0 JUMPI PUSH1 0x0 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x1C0 JUMPI PUSH1 0x20 PUSH1 0x7 SLOAD PUSH1 0x40 MLOAD SWAP1 DUP2 MSTORE RETURN JUMPDEST CALLVALUE PUSH2 0x1C0 JUMPI PUSH1 0x20 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x1C0 JUMPI PUSH1 0x4 CALLDATALOAD PUSH1 0x0 MSTORE PUSH1 0x3 PUSH1 0x20 MSTORE PUSH1 0x40 PUSH1 0x0 KECCAK256 DUP1 SLOAD PUSH2 0x6A9 PUSH1 0x1 DUP1 PUSH1 0xA0 SHL SUB DUP1 PUSH1 0x1 DUP6 ADD SLOAD AND SWAP3 PUSH2 0x10CE PUSH1 0x2 DUP7 ADD PUSH2 0x1173 JUMP JUMPDEST SWAP5 PUSH1 0xFF PUSH1 0x3 DUP3 ADD SLOAD AND SWAP3 PUSH1 0x4 DUP3 ADD SLOAD SWAP1 PUSH1 0x5 DUP4 ADD SLOAD AND PUSH1 0x6 DUP4 ADD SLOAD PUSH1 0x7 DUP5 ADD SLOAD SWAP2 PUSH1 0x8 DUP6 ADD SLOAD SWAP4 PUSH1 0xA PUSH1 0x9 DUP8 ADD SLOAD SWAP7 ADD SLOAD SWAP8 PUSH1 0x40 MLOAD SWAP12 DUP13 SWAP12 PUSH1 0xFF DUP1 DUP13 PUSH1 0x8 SHR AND SWAP12 AND SWAP10 DUP14 PUSH2 0x1265 JUMP JUMPDEST SWAP1 PUSH1 0x1 DUP3 DUP2 SHR SWAP3 AND DUP1 ISZERO PUSH2 0x1147 JUMPI JUMPDEST PUSH1 0x20 DUP4 LT EQ PUSH2 0x1131 JUMPI JUMP JUMPDEST PUSH4 0x4E487B71 PUSH1 0xE0 SHL PUSH1 0x0 MSTORE PUSH1 0x22 PUSH1 0x4 MSTORE PUSH1 0x24 PUSH1 0x0 REVERT JUMPDEST SWAP2 PUSH1 0x7F AND SWAP2 PUSH2 0x1126 JUMP JUMPDEST SWAP1 PUSH1 0x1F DUP1 NOT SWAP2 ADD AND DUP2 ADD SWAP1 DUP2 LT PUSH8 0xFFFFFFFFFFFFFFFF DUP3 GT OR PUSH2 0xC76 JUMPI PUSH1 0x40 MSTORE JUMP JUMPDEST SWAP1 PUSH1 0x40 MLOAD SWAP2 DUP3 PUSH1 0x0 DUP3 SLOAD PUSH2 0x1186 DUP2 PUSH2 0x1117 JUMP JUMPDEST SWAP1 DUP2 DUP5 MSTORE PUSH1 0x20 SWAP5 PUSH1 0x1 SWAP2 PUSH1 0x1 DUP2 AND SWAP1 DUP2 PUSH1 0x0 EQ PUSH2 0x11F6 JUMPI POP PUSH1 0x1 EQ PUSH2 0x11B7 JUMPI JUMPDEST POP POP POP PUSH2 0x11B5 SWAP3 POP SUB DUP4 PUSH2 0x1151 JUMP JUMPDEST JUMP JUMPDEST PUSH1 0x0 SWAP1 DUP2 MSTORE DUP6 DUP2 KECCAK256 SWAP6 SWAP4 POP SWAP2 SWAP1 JUMPDEST DUP2 DUP4 LT PUSH2 0x11DE JUMPI POP POP PUSH2 0x11B5 SWAP4 POP DUP3 ADD ADD CODESIZE DUP1 DUP1 PUSH2 0x11A6 JUMP JUMPDEST DUP6 SLOAD DUP9 DUP5 ADD DUP6 ADD MSTORE SWAP5 DUP6 ADD SWAP5 DUP8 SWAP5 POP SWAP2 DUP4 ADD SWAP2 PUSH2 0x11C5 JUMP JUMPDEST SWAP3 POP POP POP PUSH2 0x11B5 SWAP5 SWAP3 POP PUSH1 0xFF NOT AND DUP3 DUP5 ADD MSTORE ISZERO ISZERO PUSH1 0x5 SHL DUP3 ADD ADD CODESIZE DUP1 DUP1 PUSH2 0x11A6 JUMP JUMPDEST SWAP2 SWAP1 DUP3 MLOAD SWAP3 DUP4 DUP3 MSTORE PUSH1 0x0 JUMPDEST DUP5 DUP2 LT PUSH2 0x1244 JUMPI POP POP DUP3 PUSH1 0x0 PUSH1 0x20 DUP1 SWAP5 SWAP6 DUP5 ADD ADD MSTORE PUSH1 0x1F DUP1 NOT SWAP2 ADD AND ADD ADD SWAP1 JUMP JUMPDEST PUSH1 0x20 DUP2 DUP4 ADD DUP2 ADD MLOAD DUP5 DUP4 ADD DUP3 ADD MSTORE ADD PUSH2 0x1223 JUMP JUMPDEST SWAP1 PUSH1 0x3 DUP3 LT ISZERO PUSH2 0xB95 JUMPI MSTORE JUMP JUMPDEST SWAP11 SWAP10 SWAP8 SWAP6 SWAP3 PUSH2 0x129E SWAP1 DUP13 SWAP6 SWAP15 SWAP14 SWAP11 SWAP9 SWAP7 SWAP4 SWAP6 PUSH2 0x12A9 SWAP4 PUSH1 0x40 PUSH2 0x180 SWAP3 DUP4 SWAP3 DUP2 MSTORE PUSH1 0x1 DUP1 PUSH1 0xA0 SHL SUB DUP1 SWAP11 AND PUSH1 0x20 DUP3 ADD MSTORE ADD MSTORE DUP14 ADD SWAP1 PUSH2 0x1218 JUMP JUMPDEST SWAP13 PUSH1 0x60 DUP13 ADD SWAP1 PUSH2 0x1258 JUMP JUMPDEST PUSH1 0x80 DUP11 ADD MSTORE AND PUSH1 0xA0 DUP9 ADD MSTORE PUSH1 0xC0 DUP8 ADD MSTORE PUSH1 0xE0 DUP7 ADD MSTORE PUSH2 0x100 DUP6 ADD MSTORE PUSH2 0x120 DUP5 ADD MSTORE PUSH1 0x5 DUP3 LT ISZERO PUSH2 0xB95 JUMPI PUSH2 0x160 SWAP2 PUSH2 0x140 DUP5 ADD MSTORE ISZERO ISZERO SWAP2 ADD MSTORE JUMP JUMPDEST PUSH1 0x24 CALLDATALOAD SWAP1 PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB DUP3 AND DUP3 SUB PUSH2 0x1C0 JUMPI JUMP JUMPDEST PUSH1 0x4 CALLDATALOAD SWAP1 PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB DUP3 AND DUP3 SUB PUSH2 0x1C0 JUMPI JUMP JUMPDEST PUSH8 0xFFFFFFFFFFFFFFFF DUP2 GT PUSH2 0xC76 JUMPI PUSH1 0x1F ADD PUSH1 0x1F NOT AND PUSH1 0x20 ADD SWAP1 JUMP JUMPDEST SWAP2 SWAP1 DUP3 ADD DUP1 SWAP3 GT PUSH2 0xC8C JUMPI JUMP JUMPDEST ISZERO PUSH2 0x1340 JUMPI JUMP JUMPDEST PUSH1 0x40 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE PUSH1 0x20 PUSH1 0x4 DUP3 ADD MSTORE PUSH1 0x13 PUSH1 0x24 DUP3 ADD MSTORE PUSH19 0x50726F706F73616C206E6F7420616374697665 PUSH1 0x68 SHL PUSH1 0x44 DUP3 ADD MSTORE PUSH1 0x64 SWAP1 REVERT JUMPDEST PUSH1 0x0 SWAP1 DUP2 MSTORE PUSH1 0x3 PUSH1 0x20 MSTORE PUSH1 0x40 DUP2 KECCAK256 SWAP1 PUSH1 0xA DUP3 ADD SWAP2 DUP3 SLOAD SWAP2 PUSH1 0xFF DUP4 AND PUSH1 0x5 DUP2 LT ISZERO PUSH2 0x14BA JUMPI PUSH1 0x1 PUSH2 0x13AB SWAP2 EQ PUSH2 0x1339 JUMP JUMPDEST PUSH1 0x9 DUP3 ADD SLOAD TIMESTAMP GT PUSH2 0x13BD JUMPI JUMPDEST POP POP POP POP JUMP JUMPDEST PUSH1 0x7 PUSH1 0x6 DUP4 ADD SLOAD SWAP3 ADD SLOAD SWAP1 PUSH1 0x4 PUSH2 0x13D4 DUP4 DUP6 PUSH2 0x132C JUMP JUMPDEST PUSH1 0x2 SLOAD PUSH1 0x40 MLOAD PUSH4 0x18160DDD PUSH1 0xE0 SHL DUP2 MSTORE SWAP2 SWAP4 SWAP2 SWAP3 PUSH1 0x20 SWAP2 DUP5 SWAP2 SWAP1 DUP3 SWAP1 PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB AND GAS STATICCALL SWAP2 DUP3 ISZERO PUSH2 0x14AD JUMPI DUP2 SWAP3 PUSH2 0x1475 JUMPI JUMPDEST POP PUSH1 0x9 SLOAD SWAP2 DUP3 DUP2 MUL SWAP3 DUP2 DUP5 DIV EQ SWAP1 ISZERO OR ISZERO PUSH2 0x1461 JUMPI POP PUSH1 0x64 SWAP1 DIV GT ISZERO SWAP2 DUP3 PUSH2 0x1457 JUMPI JUMPDEST POP POP ISZERO PUSH2 0x1449 JUMPI POP DUP1 SLOAD PUSH1 0xFF NOT AND PUSH1 0x2 OR SWAP1 SSTORE JUMPDEST CODESIZE DUP1 DUP1 DUP1 PUSH2 0x13B7 JUMP JUMPDEST PUSH1 0xFF NOT AND PUSH1 0x3 OR SWAP1 SSTORE PUSH2 0x1440 JUMP JUMPDEST GT SWAP1 POP CODESIZE DUP1 PUSH2 0x142C JUMP JUMPDEST PUSH4 0x4E487B71 PUSH1 0xE0 SHL DUP2 MSTORE PUSH1 0x11 PUSH1 0x4 MSTORE PUSH1 0x24 SWAP1 REVERT JUMPDEST SWAP1 SWAP2 POP PUSH1 0x20 DUP2 RETURNDATASIZE PUSH1 0x20 GT PUSH2 0x14A5 JUMPI JUMPDEST DUP2 PUSH2 0x1491 PUSH1 0x20 SWAP4 DUP4 PUSH2 0x1151 JUMP JUMPDEST DUP2 ADD SUB SLT PUSH2 0x14A1 JUMPI MLOAD SWAP1 CODESIZE PUSH2 0x1409 JUMP JUMPDEST POP DUP1 REVERT JUMPDEST RETURNDATASIZE SWAP2 POP PUSH2 0x1484 JUMP JUMPDEST POP PUSH1 0x40 MLOAD SWAP1 RETURNDATASIZE SWAP1 DUP3 RETURNDATACOPY RETURNDATASIZE SWAP1 REVERT JUMPDEST PUSH4 0x4E487B71 PUSH1 0xE0 SHL DUP3 MSTORE PUSH1 0x21 PUSH1 0x4 MSTORE PUSH1 0x24 DUP3 REVERT JUMPDEST PUSH1 0x2 PUSH1 0x1 SLOAD EQ PUSH2 0x14DF JUMPI PUSH1 0x2 PUSH1 0x1 SSTORE JUMP JUMPDEST PUSH1 0x40 MLOAD PUSH4 0x3EE5AEB5 PUSH1 0xE0 SHL DUP2 MSTORE PUSH1 0x4 SWAP1 REVERT JUMPDEST PUSH1 0x0 SLOAD PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB AND CALLER SUB PUSH2 0x1505 JUMPI JUMP JUMPDEST PUSH1 0x40 MLOAD PUSH4 0x118CDAA7 PUSH1 0xE0 SHL DUP2 MSTORE CALLER PUSH1 0x4 DUP3 ADD MSTORE PUSH1 0x24 SWAP1 REVERT INVALID LOG2 PUSH5 0x6970667358 0x22 SLT KECCAK256 0xA8 PUSH3 0xF00043 SWAP10 SWAP6 0xE CREATE2 DUP5 0x23 0xA8 SWAP7 KECCAK256 CALLDATACOPY OR 0xA9 LOG0 SWAP10 0x27 GT 0xCD 0xED 0x24 0xC0 0xDA 0xB9 KECCAK256 MOD NUMBER STATICCALL 0xE2 PUSH5 0x736F6C6343 STOP ADDMOD XOR STOP CALLER ", "sourceMap": "411:8158:4:-:0;;;;;;;;;-1:-1:-1;411:8158:4;;;;;;;;8426:28;8445:9;8426:28;411:8158;8426:28;:::i;:::-;;411:8158;8484:10;-1:-1:-1;411:8158:4;8464:19;411:8158;;;-1:-1:-1;411:8158:4;8464:44;8445:9;411:8158;;8464:44;:::i;:::-;411:8158;;;;8445:9;411:8158;;8523:37;411:8158;8484:10;8523:37;;411:8158;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;411:8158:4;;;;;;:::i;:::-;;;;;1195:62;411:8158;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;411:8158:4;;;;469:29;411:8158;;;-1:-1:-1;;;;;411:8158:4;;;;;;;;;;;;;;-1:-1:-1;;411:8158:4;;;;;;:::i;:::-;1500:62:0;;:::i;:::-;-1:-1:-1;;;;;411:8158:4;;;;2627:22:0;;2623:91;;411:8158:4;;;-1:-1:-1;;;;;411:8158:4;;;;;;;;3052:40:0;411:8158:4;3052:40:0;;411:8158:4;2623:91:0;411:8158:4;;-1:-1:-1;;;2672:31:0;;411:8158:4;;2672:31:0;;411:8158:4;;;2672:31:0;411:8158:4;;;;;;-1:-1:-1;;411:8158:4;;;;1500:62:0;;:::i;:::-;411:8158:4;;6885:32;411:8158;;;;;;;;-1:-1:-1;;411:8158:4;;;;;;1500:62:0;;:::i;:::-;6674:13:4;;411:8158;;6716:24;411:8158;;;;;-1:-1:-1;;;411:8158:4;;;;;;;;;;;;-1:-1:-1;;;411:8158:4;;;;;;;;;;-1:-1:-1;;411:8158:4;;;;2466:103:3;;:::i;:::-;6328:9:4;:13;411:8158;;6386:28;6328:9;6386:28;411:8158;6386:28;:::i;:::-;;411:8158;6444:10;411:8158;;6424:19;411:8158;;;;;6424:44;6328:9;411:8158;;6424:44;:::i;:::-;411:8158;;;;6328:9;411:8158;;6492:37;411:8158;6444:10;6492:37;;1857:1:3;411:8158:4;;;;;;-1:-1:-1;;;411:8158:4;;;;;;;;;;;;-1:-1:-1;;;411:8158:4;;;;;;;;;;;;;-1:-1:-1;;411:8158:4;;;;;1263:28;411:8158;;;;;;;;;;;;;-1:-1:-1;;411:8158:4;;;;;;;;;;;;;;;;;;;;;;;;3580:9;411:8158;;;;;3619:14;411:8158;3619:14;;;411:8158;;;;;;;;;3611:70;3619:38;;3611:70;:::i;:::-;3718:16;;;411:8158;3699:15;:35;411:8158;;;;;;;;;;;3798:10;411:8158;;;;;;;;;;;;3863:15;411:8158;;;-1:-1:-1;;;3863:37:4;;3798:10;411:8158;3863:37;;411:8158;;;;;;;;;;;-1:-1:-1;;;;;411:8158:4;3863:37;;;;;;;411:8158;3863:37;;;411:8158;3918:10;;;411:8158;;;;;;;;;;;;3798:10;411:8158;;;;;;;;;;;;;;;;;;;;;;;;;;;3798:10;411:8158;;;;;;;;;;;;;;;;;4079:127;;;;4106:17;;:27;411:8158;;;4106:27;:::i;:::-;411:8158;;4079:127;411:8158;;;;;;;;4229:49;411:8158;3798:10;4229:49;;411:8158;4079:127;4164:21;;:31;411:8158;;;4164:31;:::i;:::-;411:8158;;4079:127;;411:8158;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;411:8158:4;;;;;3863:37;;;;;;;;;;;;;;;;;:::i;:::-;;;411:8158;;;;;3863:37;;;;;;;;;;411:8158;;;;;;;;;;;;-1:-1:-1;;;411:8158:4;;;;;;;;;;;;;-1:-1:-1;;;411:8158:4;;;;;;;;;;-1:-1:-1;;;411:8158:4;;;;;;;;;;;;;-1:-1:-1;;;411:8158:4;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;411:8158:4;;;;;;;;7674:9;411:8158;;;;;;;;;;;;;7751:17;;411:8158;7751:17;;411:8158;;7816:21;411:8158;7674:9;7816:21;;411:8158;;7851:24;411:8158;7851:24;;411:8158;7889:20;;;;411:8158;;7923:17;;;411:8158;7954:21;;;411:8158;7989:18;;;;411:8158;8021:16;;;;411:8158;8051:14;411:8158;7782:20;8051:14;;;411:8158;7782:20;;411:8158;:::i;:::-;;;;;;;;;;7989:18;411:8158;;;;;;;:::i;:::-;;;;;;;;;;-1:-1:-1;;411:8158:4;;;;;1364:48;411:8158;;;;;;;;;;;;;-1:-1:-1;;411:8158:4;;;;-1:-1:-1;;;;;411:8158:4;;:::i;:::-;;;;1542:54;411:8158;;;;;;;;;;;;;;;;;;;-1:-1:-1;;411:8158:4;;;;;;;;-1:-1:-1;;;;;411:8158:4;;;;;;;;;;;;;;-1:-1:-1;;411:8158:4;;;;1500:62:0;;:::i;:::-;411:8158:4;;;-1:-1:-1;;;;;;411:8158:4;;;;-1:-1:-1;;;;;411:8158:4;3052:40:0;411:8158:4;;3052:40:0;411:8158:4;;;;;;;-1:-1:-1;;411:8158:4;;;;;;;;:::i;:::-;;;;;;;;-1:-1:-1;;411:8158:4;;;;;1433:35;411:8158;;;;;;;;;;;;;-1:-1:-1;;411:8158:4;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;411:8158:4;;;;;;1500:62:0;;:::i;:::-;7096:3:4;7079:20;;411:8158;;7132:32;411:8158;;;;;-1:-1:-1;;;411:8158:4;;;;;;;;;;;;-1:-1:-1;;;411:8158:4;;;;7096:3;;411:8158;;;;;;;-1:-1:-1;;411:8158:4;;;;;1506:30;411:8158;;;;;;;;;;;;;-1:-1:-1;;411:8158:4;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;411:8158:4;;;;;;;;2534:29;411:8158;;2626:15;411:8158;;;-1:-1:-1;;;2626:37:4;;2652:10;411:8158;2626:37;;411:8158;;;;;;;;;;-1:-1:-1;;;;;411:8158:4;2626:37;;;;;;;411:8158;2626:37;;;411:8158;;2667:17;411:8158;-1:-1:-1;411:8158:4;;2788:15;411:8158;;-1:-1:-1;;411:8158:4;;;;;;;2788:15;411:8158;3196:30;3214:12;411:8158;3158:15;3196:30;:::i;:::-;411:8158;;;;;;;;;;;;;;;;;;;;;;;;2652:10;411:8158;2846:461;;411:8158;2846:461;411:8158;2846:461;;411:8158;;;;2846:461;;411:8158;;;;2846:461;;411:8158;;;;;;;2846:461;;;411:8158;;2846:461;;;411:8158;;;2846:461;;411:8158;3158:15;2846:461;;;411:8158;2846:461;;;411:8158;;2846:461;;;411:8158;;2846:461;;;411:8158;;;;;;;;;;;;;;;;;;;;;;;2846:461;;411:8158;;-1:-1:-1;;;;;411:8158:4;;;;;;;;;2846:461;;411:8158;;;;;;;;;;;;2626:15;411:8158;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2626:15;411:8158;;;;;;;2846:461;411:8158;2846:461;;411:8158;;;;;;;;;;;;;;;;;;;;;;2846:461;;411:8158;;;;;;;;;;;;;2846:461;;;411:8158;;-1:-1:-1;;;;;411:8158:4;;;;;;;;2846:461;;;411:8158;2788:15;411:8158;;;;2846:461;;411:8158;3214:12;411:8158;;;2846:461;;;411:8158;2667:17;411:8158;;;2846:461;;;411:8158;;;;;;2846:461;;;411:8158;;;;;;;;;;;2846:461;411:8158;;2846:461;;411:8158;;;2667:17;411:8158;;;;;;;;;;;;;3331:66;411:8158;;;;;;;;;;;;:::i;:::-;;;;;;;;;:::i;:::-;2652:10;;3331:66;;;;411:8158;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;411:8158:4;;;;;2626:15;411:8158;;;;;;;;;;-1:-1:-1;;;411:8158:4;;;;;;;;;;;;;;;;;;;;;;;;;;;2626:15;411:8158;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2626:15;411:8158;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;411:8158:4;;;;;-1:-1:-1;411:8158:4;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;411:8158:4;;;;;;;;;;;;;;;;;-1:-1:-1;;;411:8158:4;;;;;;;2626:37;;;411:8158;2626:37;;411:8158;2626:37;;;;;;411:8158;2626:37;;;:::i;:::-;;;411:8158;;;;;2626:37;;;;;;-1:-1:-1;2626:37:4;;411:8158;;;-1:-1:-1;;;411:8158:4;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2466:103:3;;;:::i;:::-;411:8158:4;;;5227:9;411:8158;;;;;5278:10;;;;:::i;:::-;5316:14;;;411:8158;;;;;;;;;;;5334:23;5316:41;411:8158;;;;;;;;;-1:-1:-1;;411:8158:4;;;;;5227:9;5565:21;;;411:8158;;;;;;;;;;5565:45;5561:569;;411:8158;6153:28;;411:8158;6153:28;;411:8158;;;;5561:569;411:8158;5634:24;;411:8158;;;;5316:14;411:8158;5634:43;;;;411:8158;;;5731:20;411:8158;;-1:-1:-1;;;;;411:8158:4;5731:20;;411:8158;;;;5731:34;411:8158;;;;;;;;;5316:14;411:8158;;;;;;;;;;;5905:71;;;411:8158;;;;;;;;:::i;:::-;;;;;;;;:::i;:::-;;;;;;;;;;;;;;6057:62;411:8158;;;;;;;;;;;6057:62;5561:569;;;;411:8158;;;-1:-1:-1;;;411:8158:4;;;;;;;;;;;;;-1:-1:-1;;;411:8158:4;;;;;;;;;;;;;-1:-1:-1;;;411:8158:4;;;;;;;;;;;;;-1:-1:-1;;;411:8158:4;;;;;;;;;;-1:-1:-1;;;411:8158:4;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;411:8158:4;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;411:8158:4;;;;;;;;;;;;;-1:-1:-1;;;411:8158:4;;;;;;;;;;;;;-1:-1:-1;;411:8158:4;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8325:10;411:8158;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;411:8158:4;;;;;1322:36;411:8158;;;;;;;;;;;;;-1:-1:-1;;411:8158:4;;;;;;;;1078:45;411:8158;;;;;;;;;;;;;1078:45;411:8158;1078:45;;411:8158;;1078:45;;;;;;:::i;:::-;;411:8158;1078:45;;;411:8158;;1078:45;411:8158;1078:45;;411:8158;1078:45;;;;411:8158;;1078:45;;;411:8158;1078:45;;;411:8158;1078:45;;;;411:8158;1078:45;;;;;411:8158;1078:45;;411:8158;;;;;;;;;;1078:45;411:8158;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;:::o;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;;;;-1:-1:-1;411:8158:4;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;:::o;:::-;-1:-1:-1;411:8158:4;;;;;;;;-1:-1:-1;;411:8158:4;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;411:8158:4;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;411:8158:4;;;;;;;;;-1:-1:-1;411:8158:4;;;;;;;;;;;;;;;;;:::o;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;;-1:-1:-1;;;;;411:8158:4;;;;;;:::o;:::-;;;;-1:-1:-1;;;;;411:8158:4;;;;;;:::o;:::-;;;;;;;;-1:-1:-1;;411:8158:4;;;;:::o;:::-;;;;;;;;;;:::o;:::-;;;;:::o;:::-;;;-1:-1:-1;;;411:8158:4;;;;;;;;;;;;-1:-1:-1;;;411:8158:4;;;;;;;4342:729;-1:-1:-1;411:8158:4;;;4436:9;411:8158;;;;;4475:14;;;;411:8158;;;;;;;;;;;;;4493:20;4467:70;4475:38;;4467:70;:::i;:::-;4578:16;;;411:8158;4560:15;:34;4556:509;;4342:729;;;;;:::o;4556:509::-;4651:21;4631:17;;;411:8158;4651:21;;411:8158;4631:41;4708:29;4631:41;;;;:::i;:::-;4708:15;411:8158;;;-1:-1:-1;;;4708:29:4;;411:8158;;;;;;;;;;;-1:-1:-1;;;;;411:8158:4;4708:29;;;;;;;;;;;4556:509;411:8158;4578:16;411:8158;;;;;;;;;;;;;;;;;4803:3;411:8158;;-1:-1:-1;4837:20:4;:65;;;;4556:509;-1:-1:-1;;4833:222:4;;;-1:-1:-1;411:8158:4;;-1:-1:-1;;411:8158:4;4708:15;411:8158;;;4833:222;4556:509;;;;;;4833:222;-1:-1:-1;;411:8158:4;4436:9;411:8158;;;4833:222;;4837:65;4861:41;;-1:-1:-1;4837:65:4;;;;411:8158;-1:-1:-1;;;411:8158:4;;;4708:29;411:8158;;;;4708:29;;;;411:8158;4708:29;;411:8158;4708:29;;;;;;411:8158;4708:29;;;:::i;:::-;;;411:8158;;;;;4708:29;;;;411:8158;;;;4708:29;;;-1:-1:-1;4708:29:4;;;411:8158;;;;;;;;;;;;-1:-1:-1;;;411:8158:4;;;;;;;;2575:307:3;1899:1;2702:7;411:8158:4;2702:18:3;2698:86;;1899:1;2702:7;411:8158:4;2575:307:3:o;2698:86::-;411:8158:4;;-1:-1:-1;;;2743:30:3;;;;;1796:162:0;1710:6;411:8158:4;-1:-1:-1;;;;;411:8158:4;735:10:2;1855:23:0;1851:101;;1796:162::o;1851:101::-;411:8158:4;;-1:-1:-1;;;1901:40:0;;735:10:2;1901:40:0;;;411:8158:4;;;1901:40:0"}, "methodIdentifiers": {"createProposal(string,uint8,uint256,address)": "1a216bbd", "depositFunds()": "e2c41dbc", "executeProposal(uint256)": "0d61b519", "getProposal(uint256)": "c7f758a8", "getUserVote(uint256,address)": "03c7881a", "governanceToken()": "f96dae0a", "hasVoted(uint256,address)": "43859632", "memberContributions(address)": "a93271af", "owner()": "8da5cb5b", "proposalCount()": "da35c664", "proposalThreshold()": "b58131b0", "proposals(uint256)": "013cf08b", "quorumPercentage()": "4fa76ec9", "renounceOwnership()": "715018a6", "setProposalThreshold(uint256)": "ece40cc1", "setQuorumPercentage(uint256)": "32f6a1dc", "setVotingPeriod(uint256)": "ea0217cf", "transferOwnership(address)": "f2fde38b", "treasuryBalance()": "313dab20", "updateProposalState(uint256)": "6ac600c7", "vote(uint256,bool)": "c9d27afe", "voteChoice(uint256,address)": "fb468855", "votingPeriod()": "02a251a3"}}, "metadata": "{\"compiler\":{\"version\":\"0.8.24+commit.e11b9ed9\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_governanceToken\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_owner\",\"type\":\"address\"}],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"name\":\"OwnableInvalidOwner\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"OwnableUnauthorizedAccount\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ReentrancyGuardReentrantCall\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"depositor\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"FundsDeposited\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"recipient\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"FundsWithdrawn\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"previousOwner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"OwnershipTransferred\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"proposalId\",\"type\":\"uint256\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"proposer\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"string\",\"name\":\"description\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"enum SimpleDAO.ProposalType\",\"name\":\"proposalType\",\"type\":\"uint8\"}],\"name\":\"ProposalCreated\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"proposalId\",\"type\":\"uint256\"}],\"name\":\"ProposalExecuted\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"proposalId\",\"type\":\"uint256\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"voter\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"bool\",\"name\":\"support\",\"type\":\"bool\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"weight\",\"type\":\"uint256\"}],\"name\":\"VoteCast\",\"type\":\"event\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"description\",\"type\":\"string\"},{\"internalType\":\"enum SimpleDAO.ProposalType\",\"name\":\"proposalType\",\"type\":\"uint8\"},{\"internalType\":\"uint256\",\"name\":\"requestedAmount\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"beneficiary\",\"type\":\"address\"}],\"name\":\"createProposal\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"depositFunds\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"proposalId\",\"type\":\"uint256\"}],\"name\":\"executeProposal\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"proposalId\",\"type\":\"uint256\"}],\"name\":\"getProposal\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"id\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"proposer\",\"type\":\"address\"},{\"internalType\":\"string\",\"name\":\"description\",\"type\":\"string\"},{\"internalType\":\"enum SimpleDAO.ProposalType\",\"name\":\"proposalType\",\"type\":\"uint8\"},{\"internalType\":\"uint256\",\"name\":\"requestedAmount\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"beneficiary\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"forVotes\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"againstVotes\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"startTime\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"endTime\",\"type\":\"uint256\"},{\"internalType\":\"enum SimpleDAO.ProposalState\",\"name\":\"state\",\"type\":\"uint8\"},{\"internalType\":\"bool\",\"name\":\"executed\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"proposalId\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"user\",\"type\":\"address\"}],\"name\":\"getUserVote\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"voted\",\"type\":\"bool\"},{\"internalType\":\"bool\",\"name\":\"choice\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"governanceToken\",\"outputs\":[{\"internalType\":\"contract IERC20\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"hasVoted\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"memberContributions\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"owner\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"proposalCount\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"proposalThreshold\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"proposals\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"id\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"proposer\",\"type\":\"address\"},{\"internalType\":\"string\",\"name\":\"description\",\"type\":\"string\"},{\"internalType\":\"enum SimpleDAO.ProposalType\",\"name\":\"proposalType\",\"type\":\"uint8\"},{\"internalType\":\"uint256\",\"name\":\"requestedAmount\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"beneficiary\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"forVotes\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"againstVotes\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"startTime\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"endTime\",\"type\":\"uint256\"},{\"internalType\":\"enum SimpleDAO.ProposalState\",\"name\":\"state\",\"type\":\"uint8\"},{\"internalType\":\"bool\",\"name\":\"executed\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"quorumPercentage\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"renounceOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"newThreshold\",\"type\":\"uint256\"}],\"name\":\"setProposalThreshold\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"newPercentage\",\"type\":\"uint256\"}],\"name\":\"setQuorumPercentage\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"newPeriod\",\"type\":\"uint256\"}],\"name\":\"setVotingPeriod\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"transferOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"treasuryBalance\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"proposalId\",\"type\":\"uint256\"}],\"name\":\"updateProposalState\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"proposalId\",\"type\":\"uint256\"},{\"internalType\":\"bool\",\"name\":\"support\",\"type\":\"bool\"}],\"name\":\"vote\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"voteChoice\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"votingPeriod\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"stateMutability\":\"payable\",\"type\":\"receive\"}],\"devdoc\":{\"details\":\"\\u7b80\\u5316\\u7684 DAO \\u6cbb\\u7406\\u5408\\u7ea6  \\u529f\\u80fd\\u7279\\u6027\\uff1a - \\u57fa\\u4e8e\\u4ee3\\u5e01\\u7684\\u6295\\u7968\\u6743\\u91cd - \\u63d0\\u6848\\u521b\\u5efa\\u548c\\u6267\\u884c - \\u6295\\u7968\\u673a\\u5236 - \\u8d44\\u91d1\\u7ba1\\u7406\",\"errors\":{\"OwnableInvalidOwner(address)\":[{\"details\":\"The owner is not a valid owner account. (eg. `address(0)`)\"}],\"OwnableUnauthorizedAccount(address)\":[{\"details\":\"The caller account is not authorized to perform an operation.\"}],\"ReentrancyGuardReentrantCall()\":[{\"details\":\"Unauthorized reentrant call.\"}]},\"kind\":\"dev\",\"methods\":{\"createProposal(string,uint8,uint256,address)\":{\"details\":\"\\u521b\\u5efa\\u63d0\\u6848\"},\"depositFunds()\":{\"details\":\"\\u5411 DAO \\u8d44\\u91d1\\u6c60\\u5b58\\u5165\\u8d44\\u91d1\"},\"executeProposal(uint256)\":{\"details\":\"\\u6267\\u884c\\u63d0\\u6848\"},\"getProposal(uint256)\":{\"details\":\"\\u83b7\\u53d6\\u63d0\\u6848\\u4fe1\\u606f\"},\"getUserVote(uint256,address)\":{\"details\":\"\\u83b7\\u53d6\\u7528\\u6237\\u6295\\u7968\\u4fe1\\u606f\"},\"owner()\":{\"details\":\"Returns the address of the current owner.\"},\"renounceOwnership()\":{\"details\":\"Leaves the contract without owner. It will not be possible to call `onlyOwner` functions. Can only be called by the current owner. NOTE: Renouncing ownership will leave the contract without an owner, thereby disabling any functionality that is only available to the owner.\"},\"setProposalThreshold(uint256)\":{\"details\":\"\\u8bbe\\u7f6e\\u63d0\\u6848\\u9608\\u503c\"},\"setQuorumPercentage(uint256)\":{\"details\":\"\\u8bbe\\u7f6e\\u6cd5\\u5b9a\\u4eba\\u6570\\u767e\\u5206\\u6bd4\"},\"setVotingPeriod(uint256)\":{\"details\":\"\\u8bbe\\u7f6e\\u6295\\u7968\\u671f\\u9650\"},\"transferOwnership(address)\":{\"details\":\"Transfers ownership of the contract to a new account (`newOwner`). Can only be called by the current owner.\"},\"updateProposalState(uint256)\":{\"details\":\"\\u66f4\\u65b0\\u63d0\\u6848\\u72b6\\u6001\"},\"vote(uint256,bool)\":{\"details\":\"\\u6295\\u7968\"}},\"title\":\"SimpleDAO\",\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"contracts/dao/SimpleDAO.sol\":\"SimpleDAO\"},\"evmVersion\":\"paris\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[],\"viaIR\":true},\"sources\":{\"@openzeppelin/contracts/access/Ownable.sol\":{\"keccak256\":\"0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8ed324d3920bb545059d66ab97d43e43ee85fd3bd52e03e401f020afb0b120f6\",\"dweb:/ipfs/QmfEckWLmZkDDcoWrkEvMWhms66xwTLff9DDhegYpvHo1a\"]},\"@openzeppelin/contracts/token/ERC20/IERC20.sol\":{\"keccak256\":\"0x74ed01eb66b923d0d0cfe3be84604ac04b76482a55f9dd655e1ef4d367f95bc2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5282825a626cfe924e504274b864a652b0023591fa66f06a067b25b51ba9b303\",\"dweb:/ipfs/QmeCfPykghhMc81VJTrHTC7sF6CRvaA1FXVq2pJhwYp1dV\"]},\"@openzeppelin/contracts/utils/Context.sol\":{\"keccak256\":\"0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12\",\"dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF\"]},\"@openzeppelin/contracts/utils/ReentrancyGuard.sol\":{\"keccak256\":\"0x11a5a79827df29e915a12740caf62fe21ebe27c08c9ae3e09abe9ee3ba3866d3\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3cf0c69ab827e3251db9ee6a50647d62c90ba580a4d7bbff21f2bea39e7b2f4a\",\"dweb:/ipfs/QmZiKwtKU1SBX4RGfQtY7PZfiapbbu6SZ9vizGQD9UHjRA\"]},\"contracts/dao/SimpleDAO.sol\":{\"keccak256\":\"0x50ab2a1750894349342c9842e0ed4704a3c878890743b521f748e44d20944400\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://9a374defa2dc9fd8d7104e3b587fd098e83094de7a22cee05acac923e9e76b5d\",\"dweb:/ipfs/Qme2K5Y5Kcbf8XH1tkFrUgPBXL6GiJ13fjMJ9gZeYh9mVJ\"]}},\"version\":1}"}}}}}