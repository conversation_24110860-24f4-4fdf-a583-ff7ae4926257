import { define<PERSON>hai<PERSON> } from '../../utils/chain/defineChain.js'

export const vanar = /*#__PURE__*/ defineChain({
  id: 2040,
  name: 'Vanar Mainnet',
  nativeCurrency: { name: 'VANR<PERSON>', symbol: 'VANRY', decimals: 18 },
  rpcUrls: {
    default: {
      http: ['https://rpc.vanarchain.com'],
    },
  },
  blockExplorers: {
    default: {
      name: 'Vanar Mainnet Explorer',
      url: 'https://explorer.vanarchain.com/',
    },
  },
  testnet: false,
})
