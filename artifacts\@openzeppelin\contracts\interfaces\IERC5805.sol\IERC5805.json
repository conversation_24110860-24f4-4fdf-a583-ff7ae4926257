{"_format": "hh-sol-artifact-1", "contractName": "IERC5805", "sourceName": "@openzeppelin/contracts/interfaces/IERC5805.sol", "abi": [{"inputs": [{"internalType": "uint256", "name": "expiry", "type": "uint256"}], "name": "VotesExpiredSignature", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "delegator", "type": "address"}, {"indexed": true, "internalType": "address", "name": "fromDelegate", "type": "address"}, {"indexed": true, "internalType": "address", "name": "toDelegate", "type": "address"}], "name": "DelegateChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "delegate", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "previousVotes", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "newVotes", "type": "uint256"}], "name": "DelegateVotesChanged", "type": "event"}, {"inputs": [], "name": "CLOCK_MODE", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "clock", "outputs": [{"internalType": "uint48", "name": "", "type": "uint48"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "delegatee", "type": "address"}], "name": "delegate", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "delegatee", "type": "address"}, {"internalType": "uint256", "name": "nonce", "type": "uint256"}, {"internalType": "uint256", "name": "expiry", "type": "uint256"}, {"internalType": "uint8", "name": "v", "type": "uint8"}, {"internalType": "bytes32", "name": "r", "type": "bytes32"}, {"internalType": "bytes32", "name": "s", "type": "bytes32"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "delegates", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "timepoint", "type": "uint256"}], "name": "getPastTotalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "uint256", "name": "timepoint", "type": "uint256"}], "name": "getPastVotes", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "getVotes", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}