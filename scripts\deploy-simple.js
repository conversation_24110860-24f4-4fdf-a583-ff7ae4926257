const { ethers } = require("hardhat");

async function main() {
  console.log("🚀 开始部署简化版 Web3 合约...\n");

  const [deployer] = await ethers.getSigners();
  console.log("部署账户:", deployer.address);
  console.log("账户余额:", ethers.formatEther(await deployer.provider.getBalance(deployer.address)), "ETH\n");

  const deployedContracts = {};

  try {
    // 1. 部署 SimpleToken
    console.log("📄 部署 SimpleToken...");
    const SimpleToken = await ethers.getContractFactory("SimpleToken");
    const simpleToken = await SimpleToken.deploy(
      "Web3 Token",
      "W3T",
      ethers.parseEther("1000000"),
      deployer.address,
      { gasLimit: 2000000 }
    );
    await simpleToken.waitForDeployment();
    deployedContracts.SimpleToken = await simpleToken.getAddress();
    console.log("✅ SimpleToken 部署成功:", deployedContracts.SimpleToken);

    // 2. 部署 Web3NFT
    console.log("\n🎨 部署 Web3NFT...");
    const Web3NFT = await ethers.getContractFactory("Web3NFT");
    const web3NFT = await Web3NFT.deploy(
      "Web3 Ecosystem NFT",
      "W3NFT",
      "https://api.web3ecosystem.com/metadata/",
      deployer.address,
      deployer.address,
      { gasLimit: 4000000 }
    );
    await web3NFT.waitForDeployment();
    deployedContracts.Web3NFT = await web3NFT.getAddress();
    console.log("✅ Web3NFT 部署成功:", deployedContracts.Web3NFT);

    // 3. 部署 NFT 市场
    console.log("\n🏪 部署 NFTMarketplace...");
    const NFTMarketplace = await ethers.getContractFactory("NFTMarketplace");
    const nftMarketplace = await NFTMarketplace.deploy(
      deployer.address,
      { gasLimit: 3000000 }
    );
    await nftMarketplace.waitForDeployment();
    deployedContracts.NFTMarketplace = await nftMarketplace.getAddress();
    console.log("✅ NFTMarketplace 部署成功:", deployedContracts.NFTMarketplace);

    // 4. 部署 SimpleDAO
    console.log("\n🏛️ 部署 SimpleDAO...");
    const SimpleDAO = await ethers.getContractFactory("SimpleDAO");
    const simpleDAO = await SimpleDAO.deploy(
      deployedContracts.SimpleToken,
      deployer.address,
      { gasLimit: 3000000 }
    );
    await simpleDAO.waitForDeployment();
    deployedContracts.SimpleDAO = await simpleDAO.getAddress();
    console.log("✅ SimpleDAO 部署成功:", deployedContracts.SimpleDAO);

    // 5. 配置合约
    console.log("\n⚙️ 配置合约...");

    // 设置 NFT 为公开铸造
    await web3NFT.setMintPhase(2); // PUBLIC = 2
    console.log("   ✅ NFT 设置为公开铸造");

    console.log("\n🎉 部署完成！合约地址:");
    console.log("=====================================");
    Object.entries(deployedContracts).forEach(([name, address]) => {
      console.log(`${name.padEnd(20)}: ${address}`);
    });
    console.log("=====================================");

    return deployedContracts;

  } catch (error) {
    console.error("\n❌ 部署失败:", error.message);
    throw error;
  }
}

if (require.main === module) {
  main()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error(error);
      process.exit(1);
    });
}

module.exports = main;
