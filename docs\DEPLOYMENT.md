# 🚀 部署指南

本文档详细介绍了如何在不同环境中部署 Web3 生态系统。

## 📋 目录

- [环境准备](#环境准备)
- [本地部署](#本地部署)
- [测试网部署](#测试网部署)
- [主网部署](#主网部署)
- [验证合约](#验证合约)
- [前端部署](#前端部署)
- [故障排除](#故障排除)

## 🛠️ 环境准备

### 系统要求

- **Node.js**: >= 16.0.0
- **npm**: >= 8.0.0
- **Git**: 最新版本
- **MetaMask**: 浏览器钱包扩展

### 依赖安装

```bash
# 克隆项目
git clone https://github.com/your-username/web3-ecosystem.git
cd web3-ecosystem

# 安装依赖
npm install

# 验证安装
npm run compile
```

### 环境变量配置

创建 `.env` 文件：

```bash
cp .env.example .env
```

配置必要的环境变量：

```env
# 钱包私钥 (不要在生产环境中使用测试私钥)
PRIVATE_KEY=your_private_key_here

# API 密钥
INFURA_PROJECT_ID=your_infura_project_id
ALCHEMY_API_KEY=your_alchemy_api_key
ETHERSCAN_API_KEY=your_etherscan_api_key
POLYGONSCAN_API_KEY=your_polygonscan_api_key

# IPFS 配置
IPFS_PROJECT_ID=your_ipfs_project_id
IPFS_PROJECT_SECRET=your_ipfs_project_secret

# 前端配置
NEXT_PUBLIC_WALLET_CONNECT_PROJECT_ID=your_walletconnect_project_id
```

## 🏠 本地部署

### 1. 启动本地区块链

```bash
# 启动 Hardhat 本地节点
npm run node
```

这将启动一个本地以太坊网络，并提供 20 个测试账户。

### 2. 编译合约

```bash
npm run compile
```

### 3. 部署合约

```bash
# 部署所有合约到本地网络
npm run deploy:local

# 或者部署简化版本
npx hardhat run scripts/deploy-simple.js --network localhost
```

### 4. 配置 MetaMask

1. 打开 MetaMask
2. 添加自定义网络：
   - 网络名称: `Hardhat Local`
   - RPC URL: `http://127.0.0.1:8545`
   - 链 ID: `31337`
   - 货币符号: `ETH`

3. 导入测试账户私钥（从控制台输出中获取）

### 5. 启动前端

```bash
npm run dev
```

访问 `http://localhost:3000` 查看应用。

## 🧪 测试网部署

### Sepolia 测试网

1. **获取测试 ETH**
   - 访问 [Sepolia Faucet](https://sepoliafaucet.com/)
   - 获取测试 ETH

2. **部署合约**
```bash
npm run deploy:sepolia
```

3. **验证部署**
```bash
# 检查合约是否部署成功
npx hardhat verify --network sepolia <CONTRACT_ADDRESS> <CONSTRUCTOR_ARGS>
```

### Polygon Mumbai 测试网

1. **获取测试 MATIC**
   - 访问 [Mumbai Faucet](https://faucet.polygon.technology/)

2. **部署合约**
```bash
npm run deploy:mumbai
```

### BSC 测试网

1. **获取测试 BNB**
   - 访问 [BSC Testnet Faucet](https://testnet.binance.org/faucet-smart)

2. **部署合约**
```bash
npm run deploy:bsc-testnet
```

## 🌐 主网部署

> ⚠️ **警告**: 主网部署需要真实的 ETH/MATIC/BNB，请确保充分测试后再部署。

### 以太坊主网

1. **准备资金**
   - 确保钱包有足够的 ETH 支付 gas 费用
   - 建议准备 0.5-1 ETH 用于部署

2. **部署合约**
```bash
npm run deploy:mainnet
```

3. **验证合约**
```bash
npm run verify:mainnet
```

### Polygon 主网

1. **准备资金**
   - 确保钱包有足够的 MATIC

2. **部署合约**
```bash
npm run deploy:polygon
```

### BSC 主网

1. **准备资金**
   - 确保钱包有足够的 BNB

2. **部署合约**
```bash
npm run deploy:bsc
```

## ✅ 验证合约

### 自动验证

大多数部署脚本会自动验证合约：

```bash
# 部署时自动验证
npm run deploy:sepolia -- --verify
```

### 手动验证

如果自动验证失败，可以手动验证：

```bash
# 验证单个合约
npx hardhat verify --network sepolia <CONTRACT_ADDRESS> <CONSTRUCTOR_ARGS>

# 验证带有复杂构造函数的合约
npx hardhat verify --network sepolia <CONTRACT_ADDRESS> --constructor-args arguments.js
```

### 创建构造函数参数文件

创建 `arguments.js` 文件：

```javascript
module.exports = [
  "Token Name",
  "TKN",
  ethers.parseEther("1000000"),
  "******************************************"
];
```

## 🌐 前端部署

### Vercel 部署

1. **连接 GitHub**
   - 将项目推送到 GitHub
   - 在 Vercel 中导入项目

2. **配置环境变量**
   - 在 Vercel 项目设置中添加环境变量
   - 确保所有 `NEXT_PUBLIC_` 前缀的变量都已配置

3. **部署**
   - Vercel 会自动部署
   - 每次推送到主分支都会触发重新部署

### Netlify 部署

1. **构建设置**
   - Build command: `npm run build`
   - Publish directory: `out`

2. **配置 next.config.js**
```javascript
/** @type {import('next').NextConfig} */
const nextConfig = {
  output: 'export',
  trailingSlash: true,
  images: {
    unoptimized: true
  }
};

module.exports = nextConfig;
```

### IPFS 部署

1. **构建静态文件**
```bash
npm run build
npm run export
```

2. **上传到 IPFS**
```bash
# 使用 IPFS CLI
ipfs add -r out/

# 或使用 Pinata
# 将 out/ 目录上传到 Pinata
```

## 🔧 故障排除

### 常见问题

#### 1. Gas 费用过高

**问题**: 部署时 gas 费用超出预期

**解决方案**:
```bash
# 检查当前 gas 价格
npx hardhat run scripts/check-gas-price.js --network mainnet

# 使用自定义 gas 价格部署
npx hardhat run scripts/deploy.js --network mainnet --gas-price 20000000000
```

#### 2. 合约验证失败

**问题**: Etherscan 验证失败

**解决方案**:
```bash
# 检查编译器版本
npx hardhat compile --show-stack-traces

# 使用正确的编译器设置重新验证
npx hardhat verify --network mainnet <ADDRESS> --contract contracts/Token.sol:Token
```

#### 3. 前端连接问题

**问题**: 前端无法连接到合约

**解决方案**:
1. 检查合约地址配置
2. 确认网络配置正确
3. 验证 ABI 文件是否最新

#### 4. IPFS 连接问题

**问题**: 无法连接到 IPFS 节点

**解决方案**:
```bash
# 检查 IPFS 节点状态
ipfs id

# 重启 IPFS 守护进程
ipfs daemon
```

### 调试技巧

#### 1. 使用 Hardhat Console

```bash
npx hardhat console --network localhost
```

```javascript
// 在控制台中测试合约
const Token = await ethers.getContractFactory("SimpleToken");
const token = await Token.attach("CONTRACT_ADDRESS");
const balance = await token.balanceOf("USER_ADDRESS");
console.log(ethers.formatEther(balance));
```

#### 2. 查看交易详情

```bash
# 使用 Hardhat 查看交易
npx hardhat run scripts/check-transaction.js --network mainnet
```

#### 3. 监控合约事件

```javascript
// 监听合约事件
const contract = new ethers.Contract(address, abi, provider);
contract.on("Transfer", (from, to, amount) => {
  console.log(`Transfer: ${from} -> ${to}, Amount: ${ethers.formatEther(amount)}`);
});
```

## 📊 部署检查清单

### 部署前检查

- [ ] 所有测试通过
- [ ] 安全审计完成
- [ ] Gas 优化完成
- [ ] 环境变量配置正确
- [ ] 钱包资金充足

### 部署后检查

- [ ] 合约地址记录
- [ ] 合约验证成功
- [ ] 前端配置更新
- [ ] 功能测试通过
- [ ] 文档更新

### 监控设置

- [ ] 设置合约监控
- [ ] 配置错误报警
- [ ] 监控 gas 使用情况
- [ ] 跟踪用户活动

## 📞 支持

如果在部署过程中遇到问题：

1. 查看 [故障排除](#故障排除) 部分
2. 检查 [GitHub Issues](https://github.com/your-username/web3-ecosystem/issues)
3. 在社区论坛寻求帮助
4. 联系开发团队

---

🎉 恭喜！您已成功部署 Web3 生态系统。
