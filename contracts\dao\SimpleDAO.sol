// SPDX-License-Identifier: MIT
pragma solidity ^0.8.24;

import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";

/**
 * @title SimpleDAO
 * @dev 简化的 DAO 治理合约
 * 
 * 功能特性：
 * - 基于代币的投票权重
 * - 提案创建和执行
 * - 投票机制
 * - 资金管理
 */
contract SimpleDAO is Ownable, ReentrancyGuard {
    
    IERC20 public governanceToken;
    
    // 提案状态
    enum ProposalState { PENDING, ACTIVE, SUCCEEDED, DEFEATED, EXECUTED }
    
    // 提案类型
    enum ProposalType { GENERAL, FUNDING, PARAMETER_CHANGE }
    
    // 提案结构
    struct Proposal {
        uint256 id;
        address proposer;
        string description;
        ProposalType proposalType;
        uint256 requestedAmount;
        address beneficiary;
        uint256 forVotes;
        uint256 againstVotes;
        uint256 startTime;
        uint256 endTime;
        ProposalState state;
        bool executed;
    }
    
    mapping(uint256 => Proposal) public proposals;
    mapping(uint256 => mapping(address => bool)) public hasVoted;
    mapping(uint256 => mapping(address => bool)) public voteChoice;
    uint256 public proposalCount;
    
    // 治理参数
    uint256 public votingPeriod = 3 days;
    uint256 public proposalThreshold = 1000 * 10**18; // 1000 代币
    uint256 public quorumPercentage = 4; // 4%
    
    // DAO 资金池
    uint256 public treasuryBalance;
    mapping(address => uint256) public memberContributions;
    
    // 事件
    event ProposalCreated(
        uint256 indexed proposalId,
        address indexed proposer,
        string description,
        ProposalType proposalType
    );
    event VoteCast(
        uint256 indexed proposalId,
        address indexed voter,
        bool support,
        uint256 weight
    );
    event ProposalExecuted(uint256 indexed proposalId);
    event FundsDeposited(address indexed depositor, uint256 amount);
    event FundsWithdrawn(address indexed recipient, uint256 amount);
    
    constructor(
        address _governanceToken,
        address _owner
    ) Ownable(_owner) {
        governanceToken = IERC20(_governanceToken);
    }
    
    /**
     * @dev 创建提案
     */
    function createProposal(
        string memory description,
        ProposalType proposalType,
        uint256 requestedAmount,
        address beneficiary
    ) external returns (uint256) {
        require(bytes(description).length > 0, "Description cannot be empty");
        require(
            governanceToken.balanceOf(msg.sender) >= proposalThreshold,
            "Insufficient tokens to create proposal"
        );
        
        uint256 proposalId = proposalCount++;
        
        proposals[proposalId] = Proposal({
            id: proposalId,
            proposer: msg.sender,
            description: description,
            proposalType: proposalType,
            requestedAmount: requestedAmount,
            beneficiary: beneficiary,
            forVotes: 0,
            againstVotes: 0,
            startTime: block.timestamp,
            endTime: block.timestamp + votingPeriod,
            state: ProposalState.ACTIVE,
            executed: false
        });
        
        emit ProposalCreated(proposalId, msg.sender, description, proposalType);
        
        return proposalId;
    }
    
    /**
     * @dev 投票
     */
    function vote(uint256 proposalId, bool support) external {
        Proposal storage proposal = proposals[proposalId];
        require(proposal.state == ProposalState.ACTIVE, "Proposal not active");
        require(block.timestamp <= proposal.endTime, "Voting period ended");
        require(!hasVoted[proposalId][msg.sender], "Already voted");
        
        uint256 weight = governanceToken.balanceOf(msg.sender);
        require(weight > 0, "No voting power");
        
        hasVoted[proposalId][msg.sender] = true;
        voteChoice[proposalId][msg.sender] = support;
        
        if (support) {
            proposal.forVotes += weight;
        } else {
            proposal.againstVotes += weight;
        }
        
        emit VoteCast(proposalId, msg.sender, support, weight);
    }
    
    /**
     * @dev 更新提案状态
     */
    function updateProposalState(uint256 proposalId) public {
        Proposal storage proposal = proposals[proposalId];
        require(proposal.state == ProposalState.ACTIVE, "Proposal not active");
        
        if (block.timestamp > proposal.endTime) {
            uint256 totalVotes = proposal.forVotes + proposal.againstVotes;
            uint256 totalSupply = governanceToken.totalSupply();
            uint256 quorum = (totalSupply * quorumPercentage) / 100;
            
            if (totalVotes >= quorum && proposal.forVotes > proposal.againstVotes) {
                proposal.state = ProposalState.SUCCEEDED;
            } else {
                proposal.state = ProposalState.DEFEATED;
            }
        }
    }
    
    /**
     * @dev 执行提案
     */
    function executeProposal(uint256 proposalId) external nonReentrant {
        Proposal storage proposal = proposals[proposalId];
        updateProposalState(proposalId);
        
        require(proposal.state == ProposalState.SUCCEEDED, "Proposal not succeeded");
        require(!proposal.executed, "Proposal already executed");
        
        proposal.executed = true;
        proposal.state = ProposalState.EXECUTED;
        
        if (proposal.proposalType == ProposalType.FUNDING) {
            require(proposal.requestedAmount <= treasuryBalance, "Insufficient treasury funds");
            require(proposal.beneficiary != address(0), "Invalid beneficiary");
            
            treasuryBalance -= proposal.requestedAmount;
            
            (bool success, ) = payable(proposal.beneficiary).call{value: proposal.requestedAmount}("");
            require(success, "Transfer failed");
            
            emit FundsWithdrawn(proposal.beneficiary, proposal.requestedAmount);
        }
        
        emit ProposalExecuted(proposalId);
    }
    
    /**
     * @dev 向 DAO 资金池存入资金
     */
    function depositFunds() external payable nonReentrant {
        require(msg.value > 0, "Must deposit some ETH");
        
        treasuryBalance += msg.value;
        memberContributions[msg.sender] += msg.value;
        
        emit FundsDeposited(msg.sender, msg.value);
    }
    
    /**
     * @dev 设置投票期限
     */
    function setVotingPeriod(uint256 newPeriod) external onlyOwner {
        require(newPeriod > 0, "Invalid period");
        votingPeriod = newPeriod;
    }
    
    /**
     * @dev 设置提案阈值
     */
    function setProposalThreshold(uint256 newThreshold) external onlyOwner {
        proposalThreshold = newThreshold;
    }
    
    /**
     * @dev 设置法定人数百分比
     */
    function setQuorumPercentage(uint256 newPercentage) external onlyOwner {
        require(newPercentage <= 100, "Invalid percentage");
        quorumPercentage = newPercentage;
    }
    
    /**
     * @dev 获取提案信息
     */
    function getProposal(uint256 proposalId) external view returns (
        uint256 id,
        address proposer,
        string memory description,
        ProposalType proposalType,
        uint256 requestedAmount,
        address beneficiary,
        uint256 forVotes,
        uint256 againstVotes,
        uint256 startTime,
        uint256 endTime,
        ProposalState state,
        bool executed
    ) {
        Proposal storage proposal = proposals[proposalId];
        return (
            proposal.id,
            proposal.proposer,
            proposal.description,
            proposal.proposalType,
            proposal.requestedAmount,
            proposal.beneficiary,
            proposal.forVotes,
            proposal.againstVotes,
            proposal.startTime,
            proposal.endTime,
            proposal.state,
            proposal.executed
        );
    }
    
    /**
     * @dev 获取用户投票信息
     */
    function getUserVote(uint256 proposalId, address user) external view returns (bool voted, bool choice) {
        return (hasVoted[proposalId][user], voteChoice[proposalId][user]);
    }
    
    // 接收 ETH
    receive() external payable {
        treasuryBalance += msg.value;
        memberContributions[msg.sender] += msg.value;
        emit FundsDeposited(msg.sender, msg.value);
    }
}
