// SPDX-License-Identifier: MIT
pragma solidity ^0.8.24;

import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/token/ERC721/IERC721.sol";

/**
 * @title MultiSigWallet
 * @dev 多重签名钱包合约，支持 ETH、ERC20、ERC721 资产管理
 * 
 * 功能特性：
 * - 多重签名确认机制
 * - 支持 ETH、ERC20、ERC721 转账
 * - 动态添加/移除签名者
 * - 修改确认阈值
 * - 交易历史记录
 */
contract MultiSigWallet is ReentrancyGuard {
    
    // 交易类型
    enum TransactionType { ETH_TRANSFER, ERC20_TRANSFER, ERC721_TRANSFER, ADD_SIGNER, REMOVE_SIGNER, CHANGE_THRESHOLD }
    
    // 交易结构
    struct Transaction {
        address to;                    // 目标地址
        uint256 value;                 // ETH 数量
        bytes data;                    // 调用数据
        TransactionType txType;        // 交易类型
        address tokenAddress;          // 代币合约地址 (ERC20/ERC721)
        uint256 tokenId;              // NFT ID (仅 ERC721)
        bool executed;                 // 是否已执行
        uint256 confirmations;         // 确认数量
        uint256 timestamp;             // 创建时间
    }
    
    // 状态变量
    address[] public signers;                              // 签名者列表
    mapping(address => bool) public isSigner;              // 是否为签名者
    uint256 public threshold;                              // 确认阈值
    
    Transaction[] public transactions;                     // 交易列表
    mapping(uint256 => mapping(address => bool)) public confirmations; // 交易确认状态
    
    // 事件
    event SignerAdded(address indexed signer);
    event SignerRemoved(address indexed signer);
    event ThresholdChanged(uint256 newThreshold);
    event TransactionSubmitted(uint256 indexed txId, address indexed submitter);
    event TransactionConfirmed(uint256 indexed txId, address indexed signer);
    event TransactionRevoked(uint256 indexed txId, address indexed signer);
    event TransactionExecuted(uint256 indexed txId);
    event EtherReceived(address indexed sender, uint256 amount);
    
    // 修饰符
    modifier onlySigners() {
        require(isSigner[msg.sender], "Not a signer");
        _;
    }
    
    modifier transactionExists(uint256 txId) {
        require(txId < transactions.length, "Transaction does not exist");
        _;
    }
    
    modifier notExecuted(uint256 txId) {
        require(!transactions[txId].executed, "Transaction already executed");
        _;
    }
    
    modifier notConfirmed(uint256 txId) {
        require(!confirmations[txId][msg.sender], "Transaction already confirmed");
        _;
    }
    
    constructor(address[] memory _signers, uint256 _threshold) {
        require(_signers.length > 0, "Signers required");
        require(_threshold > 0 && _threshold <= _signers.length, "Invalid threshold");
        
        for (uint256 i = 0; i < _signers.length; i++) {
            address signer = _signers[i];
            require(signer != address(0), "Invalid signer address");
            require(!isSigner[signer], "Duplicate signer");
            
            isSigner[signer] = true;
            signers.push(signer);
        }
        
        threshold = _threshold;
    }
    
    // 接收 ETH
    receive() external payable {
        emit EtherReceived(msg.sender, msg.value);
    }
    
    /**
     * @dev 提交 ETH 转账交易
     */
    function submitEthTransfer(address to, uint256 value) external onlySigners returns (uint256) {
        require(to != address(0), "Invalid recipient");
        require(value > 0, "Invalid amount");
        require(address(this).balance >= value, "Insufficient balance");
        
        uint256 txId = _submitTransaction(
            to,
            value,
            "",
            TransactionType.ETH_TRANSFER,
            address(0),
            0
        );
        
        return txId;
    }
    
    /**
     * @dev 提交 ERC20 转账交易
     */
    function submitERC20Transfer(
        address tokenAddress,
        address to,
        uint256 amount
    ) external onlySigners returns (uint256) {
        require(tokenAddress != address(0), "Invalid token address");
        require(to != address(0), "Invalid recipient");
        require(amount > 0, "Invalid amount");
        
        IERC20 token = IERC20(tokenAddress);
        require(token.balanceOf(address(this)) >= amount, "Insufficient token balance");
        
        bytes memory data = abi.encodeWithSignature("transfer(address,uint256)", to, amount);
        
        uint256 txId = _submitTransaction(
            tokenAddress,
            0,
            data,
            TransactionType.ERC20_TRANSFER,
            tokenAddress,
            0
        );
        
        return txId;
    }
    
    /**
     * @dev 提交 ERC721 转账交易
     */
    function submitERC721Transfer(
        address tokenAddress,
        address to,
        uint256 tokenId
    ) external onlySigners returns (uint256) {
        require(tokenAddress != address(0), "Invalid token address");
        require(to != address(0), "Invalid recipient");
        
        IERC721 nft = IERC721(tokenAddress);
        require(nft.ownerOf(tokenId) == address(this), "Wallet does not own this NFT");
        
        bytes memory data = abi.encodeWithSignature(
            "safeTransferFrom(address,address,uint256)", 
            address(this), 
            to, 
            tokenId
        );
        
        uint256 txId = _submitTransaction(
            tokenAddress,
            0,
            data,
            TransactionType.ERC721_TRANSFER,
            tokenAddress,
            tokenId
        );
        
        return txId;
    }
    
    /**
     * @dev 提交添加签名者交易
     */
    function submitAddSigner(address newSigner) external onlySigners returns (uint256) {
        require(newSigner != address(0), "Invalid signer address");
        require(!isSigner[newSigner], "Already a signer");
        
        bytes memory data = abi.encodeWithSignature("addSigner(address)", newSigner);
        
        uint256 txId = _submitTransaction(
            address(this),
            0,
            data,
            TransactionType.ADD_SIGNER,
            address(0),
            0
        );
        
        return txId;
    }
    
    /**
     * @dev 提交移除签名者交易
     */
    function submitRemoveSigner(address signer) external onlySigners returns (uint256) {
        require(isSigner[signer], "Not a signer");
        require(signers.length > 1, "Cannot remove last signer");
        
        bytes memory data = abi.encodeWithSignature("removeSigner(address)", signer);
        
        uint256 txId = _submitTransaction(
            address(this),
            0,
            data,
            TransactionType.REMOVE_SIGNER,
            address(0),
            0
        );
        
        return txId;
    }
    
    /**
     * @dev 提交修改阈值交易
     */
    function submitChangeThreshold(uint256 newThreshold) external onlySigners returns (uint256) {
        require(newThreshold > 0 && newThreshold <= signers.length, "Invalid threshold");
        
        bytes memory data = abi.encodeWithSignature("changeThreshold(uint256)", newThreshold);
        
        uint256 txId = _submitTransaction(
            address(this),
            0,
            data,
            TransactionType.CHANGE_THRESHOLD,
            address(0),
            0
        );
        
        return txId;
    }
    
    /**
     * @dev 确认交易
     */
    function confirmTransaction(uint256 txId) 
        external 
        onlySigners 
        transactionExists(txId) 
        notExecuted(txId) 
        notConfirmed(txId) 
    {
        confirmations[txId][msg.sender] = true;
        transactions[txId].confirmations++;
        
        emit TransactionConfirmed(txId, msg.sender);
        
        // 如果达到阈值，自动执行
        if (transactions[txId].confirmations >= threshold) {
            executeTransaction(txId);
        }
    }
    
    /**
     * @dev 撤销确认
     */
    function revokeConfirmation(uint256 txId) 
        external 
        onlySigners 
        transactionExists(txId) 
        notExecuted(txId) 
    {
        require(confirmations[txId][msg.sender], "Transaction not confirmed");
        
        confirmations[txId][msg.sender] = false;
        transactions[txId].confirmations--;
        
        emit TransactionRevoked(txId, msg.sender);
    }
    
    /**
     * @dev 执行交易
     */
    function executeTransaction(uint256 txId) 
        public 
        onlySigners 
        transactionExists(txId) 
        notExecuted(txId) 
    {
        require(transactions[txId].confirmations >= threshold, "Insufficient confirmations");
        
        Transaction storage transaction = transactions[txId];
        transaction.executed = true;
        
        bool success;
        if (transaction.txType == TransactionType.ETH_TRANSFER) {
            (success, ) = transaction.to.call{value: transaction.value}("");
        } else {
            (success, ) = transaction.to.call(transaction.data);
        }
        
        require(success, "Transaction execution failed");
        emit TransactionExecuted(txId);
    }
    
    /**
     * @dev 内部函数：提交交易
     */
    function _submitTransaction(
        address to,
        uint256 value,
        bytes memory data,
        TransactionType txType,
        address tokenAddress,
        uint256 tokenId
    ) internal returns (uint256) {
        uint256 txId = transactions.length;
        
        transactions.push(Transaction({
            to: to,
            value: value,
            data: data,
            txType: txType,
            tokenAddress: tokenAddress,
            tokenId: tokenId,
            executed: false,
            confirmations: 0,
            timestamp: block.timestamp
        }));
        
        emit TransactionSubmitted(txId, msg.sender);

        return txId;
    }
    
    /**
     * @dev 添加签名者 (内部调用)
     */
    function addSigner(address newSigner) external {
        require(msg.sender == address(this), "Only callable by wallet");
        require(newSigner != address(0), "Invalid signer address");
        require(!isSigner[newSigner], "Already a signer");
        
        isSigner[newSigner] = true;
        signers.push(newSigner);
        
        emit SignerAdded(newSigner);
    }
    
    /**
     * @dev 移除签名者 (内部调用)
     */
    function removeSigner(address signer) external {
        require(msg.sender == address(this), "Only callable by wallet");
        require(isSigner[signer], "Not a signer");
        require(signers.length > 1, "Cannot remove last signer");
        
        isSigner[signer] = false;
        
        // 从数组中移除
        for (uint256 i = 0; i < signers.length; i++) {
            if (signers[i] == signer) {
                signers[i] = signers[signers.length - 1];
                signers.pop();
                break;
            }
        }
        
        // 如果阈值大于签名者数量，调整阈值
        if (threshold > signers.length) {
            threshold = signers.length;
            emit ThresholdChanged(threshold);
        }
        
        emit SignerRemoved(signer);
    }
    
    /**
     * @dev 修改阈值 (内部调用)
     */
    function changeThreshold(uint256 newThreshold) external {
        require(msg.sender == address(this), "Only callable by wallet");
        require(newThreshold > 0 && newThreshold <= signers.length, "Invalid threshold");
        
        threshold = newThreshold;
        emit ThresholdChanged(newThreshold);
    }
    
    // 查询函数
    function getSigners() external view returns (address[] memory) {
        return signers;
    }
    
    function getTransactionCount() external view returns (uint256) {
        return transactions.length;
    }
    
    function getTransaction(uint256 txId) external view returns (
        address to,
        uint256 value,
        bytes memory data,
        TransactionType txType,
        address tokenAddress,
        uint256 tokenId,
        bool executed,
        uint256 confirmationCount,
        uint256 timestamp
    ) {
        Transaction storage transaction = transactions[txId];
        return (
            transaction.to,
            transaction.value,
            transaction.data,
            transaction.txType,
            transaction.tokenAddress,
            transaction.tokenId,
            transaction.executed,
            transaction.confirmations,
            transaction.timestamp
        );
    }
    
    function isConfirmed(uint256 txId, address signer) external view returns (bool) {
        return confirmations[txId][signer];
    }
}
