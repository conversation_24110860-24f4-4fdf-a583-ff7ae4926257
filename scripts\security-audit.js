const { ethers } = require("hardhat");
const fs = require("fs");
const path = require("path");

/**
 * 安全审计脚本
 * 检查智能合约的常见安全问题
 */

class SecurityAuditor {
  constructor() {
    this.issues = [];
    this.warnings = [];
    this.info = [];
  }

  async getABI(contractName) {
    // 简化的 ABI，只包含审计需要的函数
    const abis = {
      SimpleToken: [
        "function name() view returns (string)",
        "function symbol() view returns (string)",
        "function totalSupply() view returns (uint256)",
        "function MAX_SUPPLY() view returns (uint256)",
        "function owner() view returns (address)"
      ],
      Web3NFT: [
        "function name() view returns (string)",
        "function symbol() view returns (string)",
        "function totalSupply() view returns (uint256)",
        "function MAX_SUPPLY() view returns (uint256)",
        "function mintPrice() view returns (uint256)",
        "function royaltyInfo(uint256, uint256) view returns (address, uint256)"
      ],
      SimpleDAO: [
        "function proposalCount() view returns (uint256)",
        "function treasuryBalance() view returns (uint256)",
        "function votingPeriod() view returns (uint256)",
        "function proposalThreshold() view returns (uint256)",
        "function quorumPercentage() view returns (uint256)",
        "function governanceToken() view returns (address)"
      ],
      MultiSigWallet: [
        "function getSigners() view returns (address[])",
        "function requiredSignatures() view returns (uint256)",
        "function getTransactionCount() view returns (uint256)"
      ]
    };

    return abis[contractName] || [];
  }

  addIssue(severity, contract, issue, description, recommendation) {
    const item = {
      severity,
      contract,
      issue,
      description,
      recommendation,
      timestamp: new Date().toISOString()
    };

    switch (severity) {
      case 'HIGH':
      case 'CRITICAL':
        this.issues.push(item);
        break;
      case 'MEDIUM':
        this.warnings.push(item);
        break;
      case 'LOW':
      case 'INFO':
        this.info.push(item);
        break;
    }
  }

  async auditContract(contractName, contractAddress) {
    console.log(`\n🔍 审计合约: ${contractName} (${contractAddress})`);

    try {
      // 连接到本地网络
      const provider = new ethers.JsonRpcProvider("http://127.0.0.1:8545");
      const contract = new ethers.Contract(contractAddress, await this.getABI(contractName), provider);
      
      // 根据合约类型执行不同的审计
      switch (contractName) {
        case 'SimpleToken':
          await this.auditERC20(contract, contractName);
          break;
        case 'SimpleDAO':
          await this.auditDAO(contract, contractName);
          break;
        case 'Web3NFT':
          await this.auditNFT(contract, contractName);
          break;
        case 'MultiSigWallet':
          await this.auditMultiSig(contract, contractName);
          break;
        case 'SimpleAMM':
          await this.auditAMM(contract, contractName);
          break;
        case 'CrossChainBridge':
          await this.auditBridge(contract, contractName);
          break;
        default:
          console.log(`⚠️ 未知合约类型: ${contractName}`);
      }
    } catch (error) {
      this.addIssue('HIGH', contractName, 'Contract Access Error', 
        `无法访问合约: ${error.message}`, 
        '检查合约地址和ABI是否正确');
    }
  }

  async auditERC20(contract, contractName) {
    console.log('  📊 审计 ERC20 代币合约...');

    try {
      // 检查基本信息
      const name = await contract.name();
      const symbol = await contract.symbol();
      const totalSupply = await contract.totalSupply();
      const maxSupply = await contract.MAX_SUPPLY();

      console.log(`    名称: ${name}`);
      console.log(`    符号: ${symbol}`);
      console.log(`    总供应量: ${ethers.formatEther(totalSupply)}`);
      console.log(`    最大供应量: ${ethers.formatEther(maxSupply)}`);

      // 检查供应量限制
      if (totalSupply > maxSupply) {
        this.addIssue('CRITICAL', contractName, 'Supply Overflow',
          '当前总供应量超过最大供应量限制',
          '立即检查铸造逻辑，确保不能超过最大供应量');
      }

      // 检查所有权
      try {
        const owner = await contract.owner();
        console.log(`    所有者: ${owner}`);
        
        if (owner === ethers.ZeroAddress) {
          this.addIssue('MEDIUM', contractName, 'No Owner',
            '合约没有所有者，无法进行管理操作',
            '考虑是否需要设置所有者或使用多签管理');
        }
      } catch (error) {
        this.addIssue('INFO', contractName, 'No Ownership',
          '合约不支持所有权功能', '这可能是设计选择');
      }

      // 检查铸造功能
      try {
        // 尝试调用铸造函数（只是检查是否存在，不实际执行）
        const mintFunction = contract.interface.getFunction('mint');
        if (mintFunction) {
          this.addIssue('MEDIUM', contractName, 'Mint Function Exists',
            '合约具有铸造功能，需要确保访问控制正确',
            '确保只有授权地址可以铸造代币');
        }
      } catch (error) {
        this.addIssue('INFO', contractName, 'No Mint Function',
          '合约不支持铸造功能', '这可能是设计选择');
      }

    } catch (error) {
      this.addIssue('HIGH', contractName, 'ERC20 Audit Error',
        `ERC20审计失败: ${error.message}`,
        '检查合约是否正确实现了ERC20标准');
    }
  }

  async auditDAO(contract, contractName) {
    console.log('  🏛️ 审计 DAO 治理合约...');

    try {
      const proposalCount = await contract.proposalCount();
      const treasuryBalance = await contract.treasuryBalance();
      const votingPeriod = await contract.votingPeriod();
      const proposalThreshold = await contract.proposalThreshold();
      const quorumPercentage = await contract.quorumPercentage();

      console.log(`    提案数量: ${proposalCount}`);
      console.log(`    资金池: ${ethers.formatEther(treasuryBalance)} ETH`);
      console.log(`    投票期限: ${votingPeriod} 秒`);
      console.log(`    提案阈值: ${ethers.formatEther(proposalThreshold)}`);
      console.log(`    法定人数: ${quorumPercentage}%`);

      // 检查投票期限
      if (votingPeriod < 24 * 60 * 60) { // 少于1天
        this.addIssue('MEDIUM', contractName, 'Short Voting Period',
          '投票期限过短，可能导致参与度不足',
          '建议设置至少3-7天的投票期限');
      }

      // 检查法定人数
      if (quorumPercentage < 1) {
        this.addIssue('MEDIUM', contractName, 'Low Quorum',
          '法定人数过低，可能导致少数人控制决策',
          '建议设置合理的法定人数（通常3-10%）');
      }

      if (quorumPercentage > 50) {
        this.addIssue('MEDIUM', contractName, 'High Quorum',
          '法定人数过高，可能导致提案难以通过',
          '考虑降低法定人数要求');
      }

      // 检查提案阈值
      const governanceToken = await contract.governanceToken();
      const tokenContract = await ethers.getContractAt('SimpleToken', governanceToken);
      const totalSupply = await tokenContract.totalSupply();
      const thresholdPercentage = (proposalThreshold * BigInt(100)) / totalSupply;

      if (thresholdPercentage > BigInt(10)) {
        this.addIssue('MEDIUM', contractName, 'High Proposal Threshold',
          '提案阈值过高，可能阻止合理提案的创建',
          '考虑降低提案阈值');
      }

    } catch (error) {
      this.addIssue('HIGH', contractName, 'DAO Audit Error',
        `DAO审计失败: ${error.message}`,
        '检查DAO合约的基本功能是否正常');
    }
  }

  async auditNFT(contract, contractName) {
    console.log('  🎨 审计 NFT 合约...');

    try {
      const name = await contract.name();
      const symbol = await contract.symbol();
      const totalSupply = await contract.totalSupply();
      const maxSupply = await contract.MAX_SUPPLY();
      const mintPrice = await contract.mintPrice();

      console.log(`    名称: ${name}`);
      console.log(`    符号: ${symbol}`);
      console.log(`    已铸造: ${totalSupply}`);
      console.log(`    最大供应量: ${maxSupply}`);
      console.log(`    铸造价格: ${ethers.formatEther(mintPrice)} ETH`);

      // 检查铸造价格
      if (mintPrice === BigInt(0)) {
        this.addIssue('MEDIUM', contractName, 'Free Minting',
          'NFT可以免费铸造，可能导致滥用',
          '考虑设置合理的铸造价格或限制机制');
      }

      // 检查供应量
      if (totalSupply >= maxSupply) {
        this.addIssue('INFO', contractName, 'Fully Minted',
          'NFT已完全铸造', '这可能是预期的');
      }

      // 检查版税设置
      try {
        const royaltyInfo = await contract.royaltyInfo(1, ethers.parseEther("1"));
        const royaltyAmount = royaltyInfo[1];
        const royaltyPercentage = (royaltyAmount * BigInt(100)) / ethers.parseEther("1");
        
        console.log(`    版税: ${royaltyPercentage}%`);
        
        if (royaltyPercentage > BigInt(10)) {
          this.addIssue('MEDIUM', contractName, 'High Royalty',
            '版税过高，可能影响二级市场交易',
            '建议设置合理的版税（通常2.5-10%）');
        }
      } catch (error) {
        this.addIssue('INFO', contractName, 'No Royalty Support',
          'NFT不支持版税功能', '考虑是否需要添加版税支持');
      }

    } catch (error) {
      this.addIssue('HIGH', contractName, 'NFT Audit Error',
        `NFT审计失败: ${error.message}`,
        '检查NFT合约是否正确实现了ERC721标准');
    }
  }

  async auditMultiSig(contract, contractName) {
    console.log('  🔐 审计多签钱包合约...');

    try {
      const signers = await contract.getSigners();
      const threshold = await contract.requiredSignatures();
      const transactionCount = await contract.getTransactionCount();

      console.log(`    签名者数量: ${signers.length}`);
      console.log(`    所需签名: ${threshold}`);
      console.log(`    交易数量: ${transactionCount}`);

      // 检查签名者数量
      if (signers.length < 2) {
        this.addIssue('HIGH', contractName, 'Insufficient Signers',
          '签名者数量过少，不符合多签安全要求',
          '建议至少设置3个签名者');
      }

      // 检查阈值设置
      if (threshold < 2) {
        this.addIssue('HIGH', contractName, 'Low Threshold',
          '签名阈值过低，安全性不足',
          '建议设置至少2个签名的阈值');
      }

      if (threshold > signers.length) {
        this.addIssue('CRITICAL', contractName, 'Invalid Threshold',
          '签名阈值超过签名者数量，钱包将无法使用',
          '立即修正阈值设置');
      }

      // 检查签名者重复
      const uniqueSigners = new Set(signers);
      if (uniqueSigners.size !== signers.length) {
        this.addIssue('HIGH', contractName, 'Duplicate Signers',
          '存在重复的签名者地址',
          '移除重复的签名者');
      }

    } catch (error) {
      this.addIssue('HIGH', contractName, 'MultiSig Audit Error',
        `多签钱包审计失败: ${error.message}`,
        '检查多签钱包合约的基本功能');
    }
  }

  async auditAMM(contract, contractName) {
    console.log('  💱 审计 AMM 合约...');
    
    this.addIssue('INFO', contractName, 'AMM Not Deployed',
      'AMM合约尚未部署或配置',
      '完成AMM合约的部署和配置');
  }

  async auditBridge(contract, contractName) {
    console.log('  🌉 审计跨链桥合约...');
    
    this.addIssue('INFO', contractName, 'Bridge Not Deployed',
      '跨链桥合约尚未部署或配置',
      '完成跨链桥合约的部署和配置');
  }

  generateReport() {
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        critical: this.issues.filter(i => i.severity === 'CRITICAL').length,
        high: this.issues.filter(i => i.severity === 'HIGH').length,
        medium: this.warnings.length,
        low: this.info.filter(i => i.severity === 'LOW').length,
        info: this.info.filter(i => i.severity === 'INFO').length
      },
      issues: this.issues,
      warnings: this.warnings,
      info: this.info
    };

    return report;
  }

  printReport() {
    console.log('\n📋 安全审计报告');
    console.log('='.repeat(50));
    
    const summary = this.generateReport().summary;
    console.log(`🔴 严重问题: ${summary.critical}`);
    console.log(`🟠 高风险问题: ${summary.high}`);
    console.log(`🟡 中等风险问题: ${summary.medium}`);
    console.log(`🔵 低风险问题: ${summary.low}`);
    console.log(`ℹ️  信息提示: ${summary.info}`);

    if (this.issues.length > 0) {
      console.log('\n🚨 需要立即处理的问题:');
      this.issues.forEach((issue, index) => {
        console.log(`\n${index + 1}. [${issue.severity}] ${issue.contract}: ${issue.issue}`);
        console.log(`   描述: ${issue.description}`);
        console.log(`   建议: ${issue.recommendation}`);
      });
    }

    if (this.warnings.length > 0) {
      console.log('\n⚠️  警告:');
      this.warnings.forEach((warning, index) => {
        console.log(`\n${index + 1}. [${warning.severity}] ${warning.contract}: ${warning.issue}`);
        console.log(`   描述: ${warning.description}`);
        console.log(`   建议: ${warning.recommendation}`);
      });
    }

    console.log('\n✅ 审计完成');
  }

  async saveReport(filename) {
    const report = this.generateReport();
    const reportPath = path.join(__dirname, '../reports', filename);
    
    // 确保reports目录存在
    const reportsDir = path.dirname(reportPath);
    if (!fs.existsSync(reportsDir)) {
      fs.mkdirSync(reportsDir, { recursive: true });
    }

    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    console.log(`\n📄 报告已保存到: ${reportPath}`);
  }
}

async function main() {
  console.log('🔒 开始安全审计...\n');

  const auditor = new SecurityAuditor();

  // 读取部署的合约地址
  try {
    const contractsConfig = JSON.parse(
      fs.readFileSync(path.join(__dirname, '../frontend/config/contracts.json'), 'utf8')
    );

    const contracts = contractsConfig.contracts;

    // 审计每个已部署的合约
    for (const [contractName, address] of Object.entries(contracts)) {
      if (address && address !== '0x0000000000000000000000000000000000000000') {
        await auditor.auditContract(contractName, address);
      } else {
        console.log(`⏭️  跳过未部署的合约: ${contractName}`);
      }
    }

  } catch (error) {
    console.error('❌ 读取合约配置失败:', error.message);
    auditor.addIssue('CRITICAL', 'Configuration', 'Config Read Error',
      '无法读取合约配置文件',
      '确保contracts.json文件存在且格式正确');
  }

  // 生成并显示报告
  auditor.printReport();

  // 保存报告
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  await auditor.saveReport(`security-audit-${timestamp}.json`);

  // 返回审计结果
  const report = auditor.generateReport();
  const hasIssues = report.summary.critical > 0 || report.summary.high > 0;
  
  if (hasIssues) {
    console.log('\n❌ 发现安全问题，请及时处理！');
    process.exit(1);
  } else {
    console.log('\n✅ 未发现严重安全问题');
    process.exit(0);
  }
}

if (require.main === module) {
  main().catch((error) => {
    console.error('审计过程中发生错误:', error);
    process.exit(1);
  });
}

module.exports = { SecurityAuditor };
