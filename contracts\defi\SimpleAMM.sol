// SPDX-License-Identifier: MIT
pragma solidity ^0.8.24;

import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/token/ERC20/ERC20.sol";
import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/utils/math/Math.sol";

/**
 * @title SimpleAMM
 * @dev 简单的自动做市商 (AMM) 实现，支持双代币流动性池
 * 
 * 功能特性：
 * - 恒定乘积公式 (x * y = k)
 * - 流动性提供和移除
 * - 代币交换
 * - 手续费机制
 * - LP 代币奖励
 */
contract SimpleAMM is ERC20, ReentrancyGuard, Ownable {
    
    IERC20 public immutable tokenA;
    IERC20 public immutable tokenB;
    
    uint256 public reserveA;
    uint256 public reserveB;
    
    uint256 public constant FEE_RATE = 30; // 0.3% 手续费
    uint256 public constant FEE_DENOMINATOR = 10000;
    
    uint256 public totalFeeA;
    uint256 public totalFeeB;
    
    // 最小流动性锁定
    uint256 public constant MINIMUM_LIQUIDITY = 10**3;
    
    // 事件
    event LiquidityAdded(
        address indexed provider,
        uint256 amountA,
        uint256 amountB,
        uint256 liquidity
    );
    
    event LiquidityRemoved(
        address indexed provider,
        uint256 amountA,
        uint256 amountB,
        uint256 liquidity
    );
    
    event Swap(
        address indexed user,
        address indexed tokenIn,
        address indexed tokenOut,
        uint256 amountIn,
        uint256 amountOut
    );
    
    event FeesCollected(uint256 feeA, uint256 feeB);
    
    constructor(
        address _tokenA,
        address _tokenB,
        string memory _name,
        string memory _symbol,
        address _owner
    ) ERC20(_name, _symbol) Ownable(_owner) {
        require(_tokenA != _tokenB, "Identical tokens");
        require(_tokenA != address(0) && _tokenB != address(0), "Zero address");
        
        tokenA = IERC20(_tokenA);
        tokenB = IERC20(_tokenB);
    }
    
    /**
     * @dev 添加流动性
     */
    function addLiquidity(
        uint256 amountADesired,
        uint256 amountBDesired,
        uint256 amountAMin,
        uint256 amountBMin,
        address to
    ) external nonReentrant returns (uint256 amountA, uint256 amountB, uint256 liquidity) {
        require(to != address(0), "Invalid recipient");
        
        (amountA, amountB) = _calculateLiquidityAmounts(
            amountADesired,
            amountBDesired,
            amountAMin,
            amountBMin
        );
        
        // 转移代币到合约
        tokenA.transferFrom(msg.sender, address(this), amountA);
        tokenB.transferFrom(msg.sender, address(this), amountB);
        
        // 计算 LP 代币数量
        uint256 totalSupply = totalSupply();
        if (totalSupply == 0) {
            // 首次添加流动性
            liquidity = Math.sqrt(amountA * amountB) - MINIMUM_LIQUIDITY;
            _mint(address(0), MINIMUM_LIQUIDITY); // 永久锁定最小流动性
        } else {
            liquidity = Math.min(
                (amountA * totalSupply) / reserveA,
                (amountB * totalSupply) / reserveB
            );
        }
        
        require(liquidity > 0, "Insufficient liquidity minted");
        
        _mint(to, liquidity);
        
        // 更新储备
        reserveA += amountA;
        reserveB += amountB;
        
        emit LiquidityAdded(to, amountA, amountB, liquidity);
    }
    
    /**
     * @dev 移除流动性
     */
    function removeLiquidity(
        uint256 liquidity,
        uint256 amountAMin,
        uint256 amountBMin,
        address to
    ) external nonReentrant returns (uint256 amountA, uint256 amountB) {
        require(to != address(0), "Invalid recipient");
        require(liquidity > 0, "Invalid liquidity amount");
        
        uint256 totalSupply = totalSupply();
        
        // 计算可提取的代币数量
        amountA = (liquidity * reserveA) / totalSupply;
        amountB = (liquidity * reserveB) / totalSupply;
        
        require(amountA >= amountAMin, "Insufficient A amount");
        require(amountB >= amountBMin, "Insufficient B amount");
        
        // 销毁 LP 代币
        _burn(msg.sender, liquidity);
        
        // 转移代币给用户
        tokenA.transfer(to, amountA);
        tokenB.transfer(to, amountB);
        
        // 更新储备
        reserveA -= amountA;
        reserveB -= amountB;
        
        emit LiquidityRemoved(to, amountA, amountB, liquidity);
    }
    
    /**
     * @dev 交换代币 A 到代币 B
     */
    function swapAForB(
        uint256 amountAIn,
        uint256 amountBOutMin,
        address to
    ) external nonReentrant returns (uint256 amountBOut) {
        require(amountAIn > 0, "Invalid input amount");
        require(to != address(0), "Invalid recipient");
        
        amountBOut = getAmountOut(amountAIn, reserveA, reserveB);
        require(amountBOut >= amountBOutMin, "Insufficient output amount");
        
        // 转移输入代币
        tokenA.transferFrom(msg.sender, address(this), amountAIn);
        
        // 计算手续费
        uint256 fee = (amountAIn * FEE_RATE) / FEE_DENOMINATOR;
        totalFeeA += fee;
        
        // 转移输出代币
        tokenB.transfer(to, amountBOut);
        
        // 更新储备
        reserveA += amountAIn;
        reserveB -= amountBOut;
        
        emit Swap(msg.sender, address(tokenA), address(tokenB), amountAIn, amountBOut);
    }
    
    /**
     * @dev 交换代币 B 到代币 A
     */
    function swapBForA(
        uint256 amountBIn,
        uint256 amountAOutMin,
        address to
    ) external nonReentrant returns (uint256 amountAOut) {
        require(amountBIn > 0, "Invalid input amount");
        require(to != address(0), "Invalid recipient");
        
        amountAOut = getAmountOut(amountBIn, reserveB, reserveA);
        require(amountAOut >= amountAOutMin, "Insufficient output amount");
        
        // 转移输入代币
        tokenB.transferFrom(msg.sender, address(this), amountBIn);
        
        // 计算手续费
        uint256 fee = (amountBIn * FEE_RATE) / FEE_DENOMINATOR;
        totalFeeB += fee;
        
        // 转移输出代币
        tokenA.transfer(to, amountAOut);
        
        // 更新储备
        reserveB += amountBIn;
        reserveA -= amountAOut;
        
        emit Swap(msg.sender, address(tokenB), address(tokenA), amountBIn, amountAOut);
    }
    
    /**
     * @dev 计算输出数量 (恒定乘积公式)
     */
    function getAmountOut(
        uint256 amountIn,
        uint256 reserveIn,
        uint256 reserveOut
    ) public pure returns (uint256 amountOut) {
        require(amountIn > 0, "Invalid input amount");
        require(reserveIn > 0 && reserveOut > 0, "Insufficient liquidity");
        
        // 扣除手续费后的输入数量
        uint256 amountInWithFee = amountIn * (FEE_DENOMINATOR - FEE_RATE);
        uint256 numerator = amountInWithFee * reserveOut;
        uint256 denominator = (reserveIn * FEE_DENOMINATOR) + amountInWithFee;
        
        amountOut = numerator / denominator;
    }
    
    /**
     * @dev 计算所需输入数量
     */
    function getAmountIn(
        uint256 amountOut,
        uint256 reserveIn,
        uint256 reserveOut
    ) public pure returns (uint256 amountIn) {
        require(amountOut > 0, "Invalid output amount");
        require(reserveIn > 0 && reserveOut > 0, "Insufficient liquidity");
        require(amountOut < reserveOut, "Insufficient liquidity");
        
        uint256 numerator = reserveIn * amountOut * FEE_DENOMINATOR;
        uint256 denominator = (reserveOut - amountOut) * (FEE_DENOMINATOR - FEE_RATE);
        
        amountIn = (numerator / denominator) + 1;
    }
    
    /**
     * @dev 获取当前价格 (A/B)
     */
    function getPrice() external view returns (uint256 priceAB, uint256 priceBA) {
        require(reserveA > 0 && reserveB > 0, "No liquidity");
        
        priceAB = (reserveB * 1e18) / reserveA; // B per A
        priceBA = (reserveA * 1e18) / reserveB; // A per B
    }
    
    /**
     * @dev 获取池子信息
     */
    function getPoolInfo() external view returns (
        uint256 _reserveA,
        uint256 _reserveB,
        uint256 _totalSupply,
        uint256 _totalFeeA,
        uint256 _totalFeeB
    ) {
        return (reserveA, reserveB, totalSupply(), totalFeeA, totalFeeB);
    }
    
    /**
     * @dev 收取累积的手续费 (仅所有者)
     */
    function collectFees() external onlyOwner {
        require(totalFeeA > 0 || totalFeeB > 0, "No fees to collect");
        
        if (totalFeeA > 0) {
            tokenA.transfer(owner(), totalFeeA);
        }
        
        if (totalFeeB > 0) {
            tokenB.transfer(owner(), totalFeeB);
        }
        
        emit FeesCollected(totalFeeA, totalFeeB);
        
        totalFeeA = 0;
        totalFeeB = 0;
    }
    
    /**
     * @dev 内部函数：计算流动性数量
     */
    function _calculateLiquidityAmounts(
        uint256 amountADesired,
        uint256 amountBDesired,
        uint256 amountAMin,
        uint256 amountBMin
    ) internal view returns (uint256 amountA, uint256 amountB) {
        if (reserveA == 0 && reserveB == 0) {
            // 首次添加流动性
            (amountA, amountB) = (amountADesired, amountBDesired);
        } else {
            // 根据当前比例计算
            uint256 amountBOptimal = (amountADesired * reserveB) / reserveA;
            
            if (amountBOptimal <= amountBDesired) {
                require(amountBOptimal >= amountBMin, "Insufficient B amount");
                (amountA, amountB) = (amountADesired, amountBOptimal);
            } else {
                uint256 amountAOptimal = (amountBDesired * reserveA) / reserveB;
                require(amountAOptimal <= amountADesired && amountAOptimal >= amountAMin, "Insufficient A amount");
                (amountA, amountB) = (amountAOptimal, amountBDesired);
            }
        }
    }
    
    /**
     * @dev 紧急提取函数 (仅所有者)
     */
    function emergencyWithdraw(address token, uint256 amount) external onlyOwner {
        IERC20(token).transfer(owner(), amount);
    }
}
