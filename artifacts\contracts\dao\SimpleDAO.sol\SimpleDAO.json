{"_format": "hh-sol-artifact-1", "contractName": "SimpleDAO", "sourceName": "contracts/dao/SimpleDAO.sol", "abi": [{"inputs": [{"internalType": "address", "name": "_governanceToken", "type": "address"}, {"internalType": "address", "name": "_owner", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "OwnableInvalidOwner", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "OwnableUnauthorizedAccount", "type": "error"}, {"inputs": [], "name": "ReentrancyGuardReentrantCall", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "depositor", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "FundsDeposited", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "recipient", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "FundsWithdrawn", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "proposalId", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "proposer", "type": "address"}, {"indexed": false, "internalType": "string", "name": "description", "type": "string"}, {"indexed": false, "internalType": "enum SimpleDAO.ProposalType", "name": "proposalType", "type": "uint8"}], "name": "ProposalCreated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "proposalId", "type": "uint256"}], "name": "ProposalExecuted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "proposalId", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "voter", "type": "address"}, {"indexed": false, "internalType": "bool", "name": "support", "type": "bool"}, {"indexed": false, "internalType": "uint256", "name": "weight", "type": "uint256"}], "name": "VoteCast", "type": "event"}, {"inputs": [{"internalType": "string", "name": "description", "type": "string"}, {"internalType": "enum SimpleDAO.ProposalType", "name": "proposalType", "type": "uint8"}, {"internalType": "uint256", "name": "requestedAmount", "type": "uint256"}, {"internalType": "address", "name": "beneficiary", "type": "address"}], "name": "createProposal", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "depositFunds", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "proposalId", "type": "uint256"}], "name": "executeProposal", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "proposalId", "type": "uint256"}], "name": "getProposal", "outputs": [{"internalType": "uint256", "name": "id", "type": "uint256"}, {"internalType": "address", "name": "proposer", "type": "address"}, {"internalType": "string", "name": "description", "type": "string"}, {"internalType": "enum SimpleDAO.ProposalType", "name": "proposalType", "type": "uint8"}, {"internalType": "uint256", "name": "requestedAmount", "type": "uint256"}, {"internalType": "address", "name": "beneficiary", "type": "address"}, {"internalType": "uint256", "name": "forVotes", "type": "uint256"}, {"internalType": "uint256", "name": "againstVotes", "type": "uint256"}, {"internalType": "uint256", "name": "startTime", "type": "uint256"}, {"internalType": "uint256", "name": "endTime", "type": "uint256"}, {"internalType": "enum SimpleDAO.ProposalState", "name": "state", "type": "uint8"}, {"internalType": "bool", "name": "executed", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "proposalId", "type": "uint256"}, {"internalType": "address", "name": "user", "type": "address"}], "name": "getUserVote", "outputs": [{"internalType": "bool", "name": "voted", "type": "bool"}, {"internalType": "bool", "name": "choice", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "governanceToken", "outputs": [{"internalType": "contract IERC20", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "address", "name": "", "type": "address"}], "name": "hasVoted", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "memberContributions", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "proposalCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "proposalThreshold", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "proposals", "outputs": [{"internalType": "uint256", "name": "id", "type": "uint256"}, {"internalType": "address", "name": "proposer", "type": "address"}, {"internalType": "string", "name": "description", "type": "string"}, {"internalType": "enum SimpleDAO.ProposalType", "name": "proposalType", "type": "uint8"}, {"internalType": "uint256", "name": "requestedAmount", "type": "uint256"}, {"internalType": "address", "name": "beneficiary", "type": "address"}, {"internalType": "uint256", "name": "forVotes", "type": "uint256"}, {"internalType": "uint256", "name": "againstVotes", "type": "uint256"}, {"internalType": "uint256", "name": "startTime", "type": "uint256"}, {"internalType": "uint256", "name": "endTime", "type": "uint256"}, {"internalType": "enum SimpleDAO.ProposalState", "name": "state", "type": "uint8"}, {"internalType": "bool", "name": "executed", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "quorumPercentage", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "newThreshold", "type": "uint256"}], "name": "setProposalThreshold", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "newPercentage", "type": "uint256"}], "name": "setQuorumPercentage", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "newPeriod", "type": "uint256"}], "name": "setVotingPeriod", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "treasuryBalance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "proposalId", "type": "uint256"}], "name": "updateProposalState", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "proposalId", "type": "uint256"}, {"internalType": "bool", "name": "support", "type": "bool"}], "name": "vote", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "address", "name": "", "type": "address"}], "name": "voteChoice", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "votingPeriod", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"stateMutability": "payable", "type": "receive"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}