// IPFS 工具函数
import { create } from 'ipfs-http-client';

// IPFS 客户端配置
const IPFS_CONFIG = {
  // 使用公共 IPFS 网关（生产环境建议使用自己的节点）
  host: 'ipfs.infura.io',
  port: 5001,
  protocol: 'https',
  headers: {
    authorization: process.env.NEXT_PUBLIC_IPFS_PROJECT_ID && process.env.NEXT_PUBLIC_IPFS_PROJECT_SECRET
      ? `Basic ${Buffer.from(
          `${process.env.NEXT_PUBLIC_IPFS_PROJECT_ID}:${process.env.NEXT_PUBLIC_IPFS_PROJECT_SECRET}`
        ).toString('base64')}`
      : undefined,
  },
};

// 创建 IPFS 客户端
let ipfsClient = null;

const getIPFSClient = () => {
  if (!ipfsClient) {
    try {
      ipfsClient = create(IPFS_CONFIG);
    } catch (error) {
      console.error('创建 IPFS 客户端失败:', error);
      // 回退到本地节点
      ipfsClient = create({ host: 'localhost', port: 5001, protocol: 'http' });
    }
  }
  return ipfsClient;
};

/**
 * 上传文件到 IPFS
 * @param {File} file - 要上传的文件
 * @returns {Promise<string>} IPFS 哈希
 */
export const uploadFileToIPFS = async (file) => {
  try {
    const client = getIPFSClient();
    
    // 将文件转换为 ArrayBuffer
    const buffer = await file.arrayBuffer();
    
    // 上传到 IPFS
    const result = await client.add(buffer, {
      progress: (prog) => console.log(`上传进度: ${prog}`),
      pin: true, // 固定文件
    });
    
    console.log('文件上传成功:', result.path);
    return result.path;
  } catch (error) {
    console.error('上传文件到 IPFS 失败:', error);
    throw new Error('上传失败: ' + error.message);
  }
};

/**
 * 上传 JSON 数据到 IPFS
 * @param {Object} data - 要上传的 JSON 数据
 * @returns {Promise<string>} IPFS 哈希
 */
export const uploadJSONToIPFS = async (data) => {
  try {
    const client = getIPFSClient();
    
    // 将 JSON 转换为字符串
    const jsonString = JSON.stringify(data, null, 2);
    
    // 上传到 IPFS
    const result = await client.add(jsonString, {
      pin: true,
    });
    
    console.log('JSON 数据上传成功:', result.path);
    return result.path;
  } catch (error) {
    console.error('上传 JSON 到 IPFS 失败:', error);
    throw new Error('上传失败: ' + error.message);
  }
};

/**
 * 从 IPFS 获取文件
 * @param {string} hash - IPFS 哈希
 * @returns {Promise<Uint8Array>} 文件数据
 */
export const getFileFromIPFS = async (hash) => {
  try {
    const client = getIPFSClient();
    
    const chunks = [];
    for await (const chunk of client.cat(hash)) {
      chunks.push(chunk);
    }
    
    // 合并所有块
    const totalLength = chunks.reduce((acc, chunk) => acc + chunk.length, 0);
    const result = new Uint8Array(totalLength);
    let offset = 0;
    
    for (const chunk of chunks) {
      result.set(chunk, offset);
      offset += chunk.length;
    }
    
    return result;
  } catch (error) {
    console.error('从 IPFS 获取文件失败:', error);
    throw new Error('获取失败: ' + error.message);
  }
};

/**
 * 从 IPFS 获取 JSON 数据
 * @param {string} hash - IPFS 哈希
 * @returns {Promise<Object>} JSON 数据
 */
export const getJSONFromIPFS = async (hash) => {
  try {
    const data = await getFileFromIPFS(hash);
    const jsonString = new TextDecoder().decode(data);
    return JSON.parse(jsonString);
  } catch (error) {
    console.error('从 IPFS 获取 JSON 失败:', error);
    throw new Error('获取失败: ' + error.message);
  }
};

/**
 * 固定文件到 IPFS
 * @param {string} hash - IPFS 哈希
 * @returns {Promise<void>}
 */
export const pinFileToIPFS = async (hash) => {
  try {
    const client = getIPFSClient();
    await client.pin.add(hash);
    console.log('文件固定成功:', hash);
  } catch (error) {
    console.error('固定文件失败:', error);
    throw new Error('固定失败: ' + error.message);
  }
};

/**
 * 取消固定文件
 * @param {string} hash - IPFS 哈希
 * @returns {Promise<void>}
 */
export const unpinFileFromIPFS = async (hash) => {
  try {
    const client = getIPFSClient();
    await client.pin.rm(hash);
    console.log('取消固定成功:', hash);
  } catch (error) {
    console.error('取消固定失败:', error);
    throw new Error('取消固定失败: ' + error.message);
  }
};

/**
 * 获取 IPFS 网关 URL
 * @param {string} hash - IPFS 哈希
 * @param {string} gateway - 网关地址（可选）
 * @returns {string} 完整的 URL
 */
export const getIPFSGatewayURL = (hash, gateway = 'https://ipfs.io/ipfs/') => {
  return `${gateway}${hash}`;
};

/**
 * 创建 NFT 元数据
 * @param {Object} metadata - 元数据对象
 * @param {string} metadata.name - NFT 名称
 * @param {string} metadata.description - NFT 描述
 * @param {string} metadata.image - 图片 IPFS 哈希
 * @param {Array} metadata.attributes - 属性数组
 * @returns {Promise<string>} 元数据 IPFS 哈希
 */
export const createNFTMetadata = async (metadata) => {
  try {
    // 验证必需字段
    if (!metadata.name || !metadata.description) {
      throw new Error('名称和描述是必需的');
    }

    // 构建标准的 NFT 元数据格式
    const nftMetadata = {
      name: metadata.name,
      description: metadata.description,
      image: metadata.image ? getIPFSGatewayURL(metadata.image) : undefined,
      external_url: metadata.external_url,
      attributes: metadata.attributes || [],
      properties: metadata.properties || {},
      created_at: new Date().toISOString(),
    };

    // 上传元数据到 IPFS
    const hash = await uploadJSONToIPFS(nftMetadata);
    return hash;
  } catch (error) {
    console.error('创建 NFT 元数据失败:', error);
    throw new Error('创建元数据失败: ' + error.message);
  }
};

/**
 * 批量上传文件到 IPFS
 * @param {FileList} files - 文件列表
 * @param {Function} onProgress - 进度回调
 * @returns {Promise<Array>} 上传结果数组
 */
export const uploadMultipleFilesToIPFS = async (files, onProgress) => {
  const results = [];
  const total = files.length;

  for (let i = 0; i < total; i++) {
    try {
      const hash = await uploadFileToIPFS(files[i]);
      results.push({
        file: files[i].name,
        hash,
        success: true,
      });
      
      if (onProgress) {
        onProgress((i + 1) / total * 100, files[i].name);
      }
    } catch (error) {
      results.push({
        file: files[i].name,
        error: error.message,
        success: false,
      });
    }
  }

  return results;
};

/**
 * 检查 IPFS 节点状态
 * @returns {Promise<Object>} 节点信息
 */
export const checkIPFSStatus = async () => {
  try {
    const client = getIPFSClient();
    const id = await client.id();
    const version = await client.version();
    
    return {
      connected: true,
      id: id.id,
      version: version.version,
      addresses: id.addresses,
    };
  } catch (error) {
    console.error('检查 IPFS 状态失败:', error);
    return {
      connected: false,
      error: error.message,
    };
  }
};

/**
 * 获取文件统计信息
 * @param {string} hash - IPFS 哈希
 * @returns {Promise<Object>} 文件统计
 */
export const getFileStats = async (hash) => {
  try {
    const client = getIPFSClient();
    const stats = await client.files.stat(`/ipfs/${hash}`);
    
    return {
      hash,
      size: stats.size,
      cumulativeSize: stats.cumulativeSize,
      blocks: stats.blocks,
      type: stats.type,
    };
  } catch (error) {
    console.error('获取文件统计失败:', error);
    throw new Error('获取统计失败: ' + error.message);
  }
};

// 导出默认配置
export const IPFS_GATEWAYS = [
  'https://ipfs.io/ipfs/',
  'https://gateway.ipfs.io/ipfs/',
  'https://cloudflare-ipfs.com/ipfs/',
  'https://dweb.link/ipfs/',
];

export default {
  uploadFileToIPFS,
  uploadJSONToIPFS,
  getFileFromIPFS,
  getJSONFromIPFS,
  pinFileToIPFS,
  unpinFileFromIPFS,
  getIPFSGatewayURL,
  createNFTMetadata,
  uploadMultipleFilesToIPFS,
  checkIPFSStatus,
  getFileStats,
};
