import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useWeb3 } from './_app';
import {
  uploadFileToIPFS,
  uploadJSONToIPFS,
  getJSONFromIPFS,
  getIPFSGatewayURL,
  createNFTMetadata,
  uploadMultipleFilesToIPFS,
  checkIPFSStatus,
  getFileStats,
  IPFS_GATEWAYS
} from '../frontend/utils/ipfs';

export default function Storage() {
  const { account, isConnected } = useWeb3();
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('upload');
  const [ipfsStatus, setIPFSStatus] = useState(null);
  const [uploadResults, setUploadResults] = useState([]);
  const [uploadProgress, setUploadProgress] = useState(0);

  // 表单状态
  const [fileUpload, setFileUpload] = useState({
    files: null,
    selectedGateway: IPFS_GATEWAYS[0]
  });

  const [jsonUpload, setJsonUpload] = useState({
    data: '',
    name: 'data.json'
  });

  const [nftMetadata, setNftMetadata] = useState({
    name: '',
    description: '',
    image: '',
    attributes: [{ trait_type: '', value: '' }],
    external_url: ''
  });

  const [retrieveForm, setRetrieveForm] = useState({
    hash: '',
    result: null,
    type: 'json'
  });

  useEffect(() => {
    checkIPFSConnection();
  }, []);

  const checkIPFSConnection = async () => {
    try {
      const status = await checkIPFSStatus();
      setIPFSStatus(status);
    } catch (error) {
      console.error('检查 IPFS 连接失败:', error);
      setIPFSStatus({ connected: false, error: error.message });
    }
  };

  const handleFileUpload = async () => {
    if (!fileUpload.files || fileUpload.files.length === 0) return;

    setLoading(true);
    setUploadProgress(0);
    setUploadResults([]);

    try {
      if (fileUpload.files.length === 1) {
        // 单文件上传
        const hash = await uploadFileToIPFS(fileUpload.files[0]);
        const stats = await getFileStats(hash);
        
        setUploadResults([{
          file: fileUpload.files[0].name,
          hash,
          url: getIPFSGatewayURL(hash, fileUpload.selectedGateway),
          size: stats.size,
          success: true
        }]);
      } else {
        // 多文件上传
        const results = await uploadMultipleFilesToIPFS(
          fileUpload.files,
          (progress, fileName) => {
            setUploadProgress(progress);
            console.log(`上传进度: ${progress.toFixed(1)}% - ${fileName}`);
          }
        );

        const enhancedResults = await Promise.all(
          results.map(async (result) => {
            if (result.success) {
              try {
                const stats = await getFileStats(result.hash);
                return {
                  ...result,
                  url: getIPFSGatewayURL(result.hash, fileUpload.selectedGateway),
                  size: stats.size
                };
              } catch (error) {
                return {
                  ...result,
                  url: getIPFSGatewayURL(result.hash, fileUpload.selectedGateway)
                };
              }
            }
            return result;
          })
        );

        setUploadResults(enhancedResults);
      }

      alert('文件上传成功!');
    } catch (error) {
      console.error('上传失败:', error);
      alert('上传失败: ' + error.message);
    } finally {
      setLoading(false);
      setUploadProgress(0);
    }
  };

  const handleJSONUpload = async () => {
    if (!jsonUpload.data) return;

    setLoading(true);
    try {
      const data = JSON.parse(jsonUpload.data);
      const hash = await uploadJSONToIPFS(data);
      const stats = await getFileStats(hash);

      setUploadResults([{
        file: jsonUpload.name,
        hash,
        url: getIPFSGatewayURL(hash, fileUpload.selectedGateway),
        size: stats.size,
        success: true,
        type: 'json'
      }]);

      alert('JSON 数据上传成功!');
    } catch (error) {
      console.error('上传失败:', error);
      alert('上传失败: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleNFTMetadataUpload = async () => {
    if (!nftMetadata.name || !nftMetadata.description) return;

    setLoading(true);
    try {
      // 过滤空属性
      const filteredAttributes = nftMetadata.attributes.filter(
        attr => attr.trait_type && attr.value
      );

      const metadata = {
        ...nftMetadata,
        attributes: filteredAttributes
      };

      const hash = await createNFTMetadata(metadata);
      const stats = await getFileStats(hash);

      setUploadResults([{
        file: `${nftMetadata.name}_metadata.json`,
        hash,
        url: getIPFSGatewayURL(hash, fileUpload.selectedGateway),
        size: stats.size,
        success: true,
        type: 'nft-metadata'
      }]);

      alert('NFT 元数据上传成功!');
    } catch (error) {
      console.error('上传失败:', error);
      alert('上传失败: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleRetrieveData = async () => {
    if (!retrieveForm.hash) return;

    setLoading(true);
    try {
      if (retrieveForm.type === 'json') {
        const data = await getJSONFromIPFS(retrieveForm.hash);
        setRetrieveForm({
          ...retrieveForm,
          result: {
            type: 'json',
            data: JSON.stringify(data, null, 2)
          }
        });
      } else {
        // 对于文件，只提供下载链接
        setRetrieveForm({
          ...retrieveForm,
          result: {
            type: 'file',
            url: getIPFSGatewayURL(retrieveForm.hash, fileUpload.selectedGateway)
          }
        });
      }
    } catch (error) {
      console.error('获取数据失败:', error);
      alert('获取数据失败: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const addAttribute = () => {
    setNftMetadata({
      ...nftMetadata,
      attributes: [...nftMetadata.attributes, { trait_type: '', value: '' }]
    });
  };

  const removeAttribute = (index) => {
    const newAttributes = nftMetadata.attributes.filter((_, i) => i !== index);
    setNftMetadata({ ...nftMetadata, attributes: newAttributes });
  };

  const updateAttribute = (index, field, value) => {
    const newAttributes = [...nftMetadata.attributes];
    newAttributes[index][field] = value;
    setNftMetadata({ ...nftMetadata, attributes: newAttributes });
  };

  return (
    <>
      <Head>
        <title>去中心化存储 - Web3 生态系统</title>
      </Head>

      <div className="container mx-auto px-4 py-8">
        {/* 导航栏 */}
        <nav className="bg-white rounded-lg shadow-lg p-4 mb-8">
          <div className="flex justify-center space-x-6">
            <a href="/" className="text-gray-600 font-medium hover:text-primary-600">
              首页
            </a>
            <a href="/nft-marketplace" className="text-gray-600 font-medium hover:text-primary-600">
              NFT 市场
            </a>
            <a href="/defi" className="text-gray-600 font-medium hover:text-primary-600">
              DeFi 协议
            </a>
            <a href="/dao" className="text-gray-600 font-medium hover:text-primary-600">
              DAO 治理
            </a>
            <a href="/storage" className="text-primary-600 font-medium hover:text-primary-800">
              去中心化存储
            </a>
          </div>
        </nav>

        <h1 className="text-4xl font-bold text-center mb-8 gradient-text">
          📁 去中心化存储
        </h1>

        {/* IPFS 状态 */}
        <div className="card max-w-md mx-auto mb-8">
          <h2 className="text-xl font-bold mb-4">IPFS 连接状态</h2>
          {ipfsStatus ? (
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-gray-600">状态:</span>
                <span className={`font-semibold ${ipfsStatus.connected ? 'text-green-600' : 'text-red-600'}`}>
                  {ipfsStatus.connected ? '已连接' : '未连接'}
                </span>
              </div>
              {ipfsStatus.connected && (
                <>
                  <div className="flex justify-between">
                    <span className="text-gray-600">版本:</span>
                    <span className="font-semibold">{ipfsStatus.version}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">节点 ID:</span>
                    <span className="font-mono text-xs">{ipfsStatus.id?.slice(0, 20)}...</span>
                  </div>
                </>
              )}
              {!ipfsStatus.connected && (
                <p className="text-sm text-red-600">{ipfsStatus.error}</p>
              )}
            </div>
          ) : (
            <div className="text-center">
              <div className="loading-spinner mx-auto mb-2"></div>
              <p className="text-gray-600">检查连接中...</p>
            </div>
          )}
          <button
            onClick={checkIPFSConnection}
            className="btn-secondary w-full mt-4"
          >
            重新检查
          </button>
        </div>

        {/* 标签页导航 */}
        <div className="flex justify-center mb-8">
          <div className="bg-white rounded-lg p-1 shadow-lg">
            {['upload', 'json', 'nft', 'retrieve'].map((tab) => (
              <button
                key={tab}
                onClick={() => setActiveTab(tab)}
                className={`px-6 py-2 rounded-md font-medium transition-colors ${
                  activeTab === tab
                    ? 'bg-primary-600 text-white'
                    : 'text-gray-600 hover:text-primary-600'
                }`}
              >
                {tab === 'upload' && '文件上传'}
                {tab === 'json' && 'JSON 数据'}
                {tab === 'nft' && 'NFT 元数据'}
                {tab === 'retrieve' && '数据获取'}
              </button>
            ))}
          </div>
        </div>

        {/* 文件上传 */}
        {activeTab === 'upload' && (
          <div className="max-w-2xl mx-auto">
            <div className="card">
              <h2 className="text-2xl font-bold mb-6">文件上传到 IPFS</h2>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    选择文件
                  </label>
                  <input
                    type="file"
                    multiple
                    onChange={(e) => setFileUpload({...fileUpload, files: e.target.files})}
                    className="input-field"
                  />
                  <p className="text-sm text-gray-500 mt-1">
                    支持多文件上传，文件将永久存储在 IPFS 网络中
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    IPFS 网关
                  </label>
                  <select
                    value={fileUpload.selectedGateway}
                    onChange={(e) => setFileUpload({...fileUpload, selectedGateway: e.target.value})}
                    className="input-field"
                  >
                    {IPFS_GATEWAYS.map((gateway) => (
                      <option key={gateway} value={gateway}>
                        {gateway}
                      </option>
                    ))}
                  </select>
                </div>

                {uploadProgress > 0 && (
                  <div className="bg-blue-50 p-4 rounded-lg">
                    <div className="flex justify-between mb-2">
                      <span className="text-sm font-medium">上传进度</span>
                      <span className="text-sm">{uploadProgress.toFixed(1)}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${uploadProgress}%` }}
                      ></div>
                    </div>
                  </div>
                )}

                <button
                  onClick={handleFileUpload}
                  disabled={loading || !fileUpload.files}
                  className="btn-primary w-full disabled:opacity-50"
                >
                  {loading ? '上传中...' : '上传到 IPFS'}
                </button>
              </div>
            </div>
          </div>
        )}

        {/* JSON 数据上传 */}
        {activeTab === 'json' && (
          <div className="max-w-2xl mx-auto">
            <div className="card">
              <h2 className="text-2xl font-bold mb-6">JSON 数据上传</h2>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    文件名
                  </label>
                  <input
                    type="text"
                    value={jsonUpload.name}
                    onChange={(e) => setJsonUpload({...jsonUpload, name: e.target.value})}
                    placeholder="data.json"
                    className="input-field"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    JSON 数据
                  </label>
                  <textarea
                    value={jsonUpload.data}
                    onChange={(e) => setJsonUpload({...jsonUpload, data: e.target.value})}
                    placeholder='{"key": "value", "array": [1, 2, 3]}'
                    rows={10}
                    className="input-field font-mono text-sm"
                  />
                  <p className="text-sm text-gray-500 mt-1">
                    请输入有效的 JSON 格式数据
                  </p>
                </div>

                <button
                  onClick={handleJSONUpload}
                  disabled={loading || !jsonUpload.data}
                  className="btn-primary w-full disabled:opacity-50"
                >
                  {loading ? '上传中...' : '上传 JSON'}
                </button>
              </div>
            </div>
          </div>
        )}

        {/* NFT 元数据 */}
        {activeTab === 'nft' && (
          <div className="max-w-2xl mx-auto">
            <div className="card">
              <h2 className="text-2xl font-bold mb-6">NFT 元数据创建</h2>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    NFT 名称 *
                  </label>
                  <input
                    type="text"
                    value={nftMetadata.name}
                    onChange={(e) => setNftMetadata({...nftMetadata, name: e.target.value})}
                    placeholder="我的 NFT"
                    className="input-field"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    描述 *
                  </label>
                  <textarea
                    value={nftMetadata.description}
                    onChange={(e) => setNftMetadata({...nftMetadata, description: e.target.value})}
                    placeholder="这是一个独特的 NFT..."
                    rows={3}
                    className="input-field"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    图片 IPFS 哈希
                  </label>
                  <input
                    type="text"
                    value={nftMetadata.image}
                    onChange={(e) => setNftMetadata({...nftMetadata, image: e.target.value})}
                    placeholder="QmXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX"
                    className="input-field"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    外部链接
                  </label>
                  <input
                    type="url"
                    value={nftMetadata.external_url}
                    onChange={(e) => setNftMetadata({...nftMetadata, external_url: e.target.value})}
                    placeholder="https://example.com"
                    className="input-field"
                  />
                </div>

                <div>
                  <div className="flex justify-between items-center mb-2">
                    <label className="block text-sm font-medium text-gray-700">
                      属性
                    </label>
                    <button
                      onClick={addAttribute}
                      className="btn-secondary text-sm"
                    >
                      添加属性
                    </button>
                  </div>
                  
                  {nftMetadata.attributes.map((attr, index) => (
                    <div key={index} className="flex space-x-2">
                      <input
                        type="text"
                        value={attr.trait_type}
                        onChange={(e) => updateAttribute(index, 'trait_type', e.target.value)}
                        placeholder="属性名"
                        className="input-field flex-1"
                      />
                      <input
                        type="text"
                        value={attr.value}
                        onChange={(e) => updateAttribute(index, 'value', e.target.value)}
                        placeholder="属性值"
                        className="input-field flex-1"
                      />
                      <button
                        onClick={() => removeAttribute(index)}
                        className="bg-red-500 hover:bg-red-600 text-white px-3 py-2 rounded"
                      >
                        ×
                      </button>
                    </div>
                  ))}
                </div>

                <button
                  onClick={handleNFTMetadataUpload}
                  disabled={loading || !nftMetadata.name || !nftMetadata.description}
                  className="btn-primary w-full disabled:opacity-50"
                >
                  {loading ? '创建中...' : '创建 NFT 元数据'}
                </button>
              </div>
            </div>
          </div>
        )}

        {/* 数据获取 */}
        {activeTab === 'retrieve' && (
          <div className="max-w-2xl mx-auto">
            <div className="card">
              <h2 className="text-2xl font-bold mb-6">从 IPFS 获取数据</h2>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    IPFS 哈希
                  </label>
                  <input
                    type="text"
                    value={retrieveForm.hash}
                    onChange={(e) => setRetrieveForm({...retrieveForm, hash: e.target.value})}
                    placeholder="QmXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX"
                    className="input-field"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    数据类型
                  </label>
                  <select
                    value={retrieveForm.type}
                    onChange={(e) => setRetrieveForm({...retrieveForm, type: e.target.value})}
                    className="input-field"
                  >
                    <option value="json">JSON 数据</option>
                    <option value="file">文件</option>
                  </select>
                </div>

                <button
                  onClick={handleRetrieveData}
                  disabled={loading || !retrieveForm.hash}
                  className="btn-primary w-full disabled:opacity-50"
                >
                  {loading ? '获取中...' : '获取数据'}
                </button>

                {retrieveForm.result && (
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <h3 className="font-medium mb-2">获取结果</h3>
                    {retrieveForm.result.type === 'json' ? (
                      <pre className="text-sm bg-white p-3 rounded border overflow-auto max-h-64">
                        {retrieveForm.result.data}
                      </pre>
                    ) : (
                      <div>
                        <p className="text-sm text-gray-600 mb-2">文件下载链接:</p>
                        <a
                          href={retrieveForm.result.url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-primary-600 hover:text-primary-800 underline break-all"
                        >
                          {retrieveForm.result.url}
                        </a>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* 上传结果 */}
        {uploadResults.length > 0 && (
          <div className="mt-8">
            <h2 className="text-2xl font-bold text-center mb-6">上传结果</h2>
            <div className="space-y-4">
              {uploadResults.map((result, index) => (
                <div key={index} className={`card ${result.success ? 'border-green-200' : 'border-red-200'}`}>
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <h3 className="font-bold mb-2">{result.file}</h3>
                      {result.success ? (
                        <div className="space-y-1 text-sm">
                          <p><strong>IPFS 哈希:</strong> <span className="font-mono">{result.hash}</span></p>
                          {result.size && <p><strong>文件大小:</strong> {(result.size / 1024).toFixed(2)} KB</p>}
                          <p><strong>访问链接:</strong></p>
                          <a
                            href={result.url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-primary-600 hover:text-primary-800 underline break-all"
                          >
                            {result.url}
                          </a>
                        </div>
                      ) : (
                        <p className="text-red-600 text-sm">{result.error}</p>
                      )}
                    </div>
                    <span className={`px-2 py-1 rounded text-xs font-medium ${
                      result.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                    }`}>
                      {result.success ? '成功' : '失败'}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </>
  );
}
