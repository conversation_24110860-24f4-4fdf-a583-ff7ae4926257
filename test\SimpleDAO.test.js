const { expect } = require("chai");
const { ethers } = require("hardhat");
const { time } = require("@nomicfoundation/hardhat-network-helpers");

describe("SimpleDAO", function () {
  let SimpleToken;
  let SimpleDAO;
  let token;
  let dao;
  let owner;
  let addr1;
  let addr2;
  let addrs;

  const INITIAL_SUPPLY = ethers.parseEther("1000000");
  const PROPOSAL_THRESHOLD = ethers.parseEther("1000");
  const VOTING_PERIOD = 3 * 24 * 60 * 60; // 3天

  beforeEach(async function () {
    [owner, addr1, addr2, ...addrs] = await ethers.getSigners();

    // 部署代币合约
    SimpleToken = await ethers.getContractFactory("SimpleToken");
    token = await SimpleToken.deploy(
      "DAO Token",
      "DAO",
      INITIAL_SUPPLY,
      owner.address
    );

    // 部署 DAO 合约
    SimpleDAO = await ethers.getContractFactory("SimpleDAO");
    dao = await SimpleDAO.deploy(
      await token.getAddress(),
      owner.address
    );

    // 给测试账户分配代币
    await token.transfer(addr1.address, ethers.parseEther("10000"));
    await token.transfer(addr2.address, ethers.parseEther("5000"));
  });

  describe("部署", function () {
    it("应该设置正确的治理代币", async function () {
      expect(await dao.governanceToken()).to.equal(await token.getAddress());
    });

    it("应该设置正确的所有者", async function () {
      expect(await dao.owner()).to.equal(owner.address);
    });

    it("应该设置正确的初始参数", async function () {
      expect(await dao.proposalThreshold()).to.equal(PROPOSAL_THRESHOLD);
      expect(await dao.votingPeriod()).to.equal(VOTING_PERIOD);
      expect(await dao.quorumPercentage()).to.equal(4);
    });

    it("初始提案数量应该为0", async function () {
      expect(await dao.proposalCount()).to.equal(0);
    });

    it("初始资金池应该为0", async function () {
      expect(await dao.treasuryBalance()).to.equal(0);
    });
  });

  describe("资金管理", function () {
    it("应该能够接收ETH存款", async function () {
      const depositAmount = ethers.parseEther("1");
      
      await dao.connect(addr1).depositFunds({ value: depositAmount });
      
      expect(await dao.treasuryBalance()).to.equal(depositAmount);
      expect(await dao.memberContributions(addr1.address)).to.equal(depositAmount);
    });

    it("应该能够通过receive函数接收ETH", async function () {
      const depositAmount = ethers.parseEther("1");
      
      await addr1.sendTransaction({
        to: await dao.getAddress(),
        value: depositAmount
      });
      
      expect(await dao.treasuryBalance()).to.equal(depositAmount);
      expect(await dao.memberContributions(addr1.address)).to.equal(depositAmount);
    });

    it("应该触发存款事件", async function () {
      const depositAmount = ethers.parseEther("1");
      
      await expect(dao.connect(addr1).depositFunds({ value: depositAmount }))
        .to.emit(dao, "FundsDeposited")
        .withArgs(addr1.address, depositAmount);
    });

    it("应该累积多次存款", async function () {
      const depositAmount1 = ethers.parseEther("1");
      const depositAmount2 = ethers.parseEther("0.5");
      
      await dao.connect(addr1).depositFunds({ value: depositAmount1 });
      await dao.connect(addr1).depositFunds({ value: depositAmount2 });
      
      expect(await dao.treasuryBalance()).to.equal(depositAmount1 + depositAmount2);
      expect(await dao.memberContributions(addr1.address)).to.equal(depositAmount1 + depositAmount2);
    });
  });

  describe("提案创建", function () {
    beforeEach(async function () {
      // 确保addr1有足够的代币创建提案
      const balance = await token.balanceOf(addr1.address);
      expect(balance).to.be.gte(PROPOSAL_THRESHOLD);
    });

    it("应该能够创建一般提案", async function () {
      const description = "这是一个测试提案";
      
      await dao.connect(addr1).createProposal(
        description,
        0, // GENERAL
        0,
        ethers.ZeroAddress
      );
      
      expect(await dao.proposalCount()).to.equal(1);
      
      const proposal = await dao.getProposal(0);
      expect(proposal[1]).to.equal(addr1.address); // proposer
      expect(proposal[2]).to.equal(description); // description
      expect(proposal[3]).to.equal(0); // type
    });

    it("应该能够创建资金申请提案", async function () {
      // 先存入一些资金
      await dao.connect(addr1).depositFunds({ value: ethers.parseEther("2") });
      
      const description = "申请资金用于开发";
      const requestedAmount = ethers.parseEther("1");
      
      await dao.connect(addr1).createProposal(
        description,
        1, // FUNDING
        requestedAmount,
        addr2.address
      );
      
      const proposal = await dao.getProposal(0);
      expect(proposal[3]).to.equal(1); // type
      expect(proposal[4]).to.equal(requestedAmount); // requestedAmount
      expect(proposal[5]).to.equal(addr2.address); // beneficiary
    });

    it("代币不足时不应该能够创建提案", async function () {
      await expect(
        dao.connect(addr2).createProposal(
          "测试提案",
          0,
          0,
          ethers.ZeroAddress
        )
      ).to.be.revertedWith("Insufficient tokens to create proposal");
    });

    it("应该触发提案创建事件", async function () {
      const description = "测试提案";
      
      await expect(
        dao.connect(addr1).createProposal(
          description,
          0,
          0,
          ethers.ZeroAddress
        )
      ).to.emit(dao, "ProposalCreated")
        .withArgs(0, addr1.address, description, 0);
    });
  });

  describe("投票", function () {
    let proposalId;

    beforeEach(async function () {
      // 创建一个测试提案
      await dao.connect(addr1).createProposal(
        "测试提案",
        0,
        0,
        ethers.ZeroAddress
      );
      proposalId = 0;
    });

    it("应该能够投支持票", async function () {
      await dao.connect(addr1).vote(proposalId, true);
      
      const proposal = await dao.getProposal(proposalId);
      const expectedVotes = await token.balanceOf(addr1.address);
      expect(proposal[6]).to.equal(expectedVotes); // forVotes
      
      const [voted, choice] = await dao.getUserVote(proposalId, addr1.address);
      expect(voted).to.be.true;
      expect(choice).to.be.true;
    });

    it("应该能够投反对票", async function () {
      await dao.connect(addr2).vote(proposalId, false);
      
      const proposal = await dao.getProposal(proposalId);
      const expectedVotes = await token.balanceOf(addr2.address);
      expect(proposal[7]).to.equal(expectedVotes); // againstVotes
      
      const [voted, choice] = await dao.getUserVote(proposalId, addr2.address);
      expect(voted).to.be.true;
      expect(choice).to.be.false;
    });

    it("不应该能够重复投票", async function () {
      await dao.connect(addr1).vote(proposalId, true);
      
      await expect(
        dao.connect(addr1).vote(proposalId, false)
      ).to.be.revertedWith("Already voted");
    });

    it("没有代币的用户不应该能够投票", async function () {
      await expect(
        dao.connect(addrs[0]).vote(proposalId, true)
      ).to.be.revertedWith("No voting power");
    });

    it("应该触发投票事件", async function () {
      const expectedWeight = await token.balanceOf(addr1.address);
      
      await expect(dao.connect(addr1).vote(proposalId, true))
        .to.emit(dao, "VoteCast")
        .withArgs(proposalId, addr1.address, true, expectedWeight);
    });

    it("投票期结束后不应该能够投票", async function () {
      // 快进时间到投票期结束后
      await time.increase(VOTING_PERIOD + 1);
      
      await expect(
        dao.connect(addr1).vote(proposalId, true)
      ).to.be.revertedWith("Voting period ended");
    });
  });

  describe("提案执行", function () {
    let proposalId;

    beforeEach(async function () {
      // 存入资金
      await dao.connect(addr1).depositFunds({ value: ethers.parseEther("2") });
      
      // 创建资金申请提案
      await dao.connect(addr1).createProposal(
        "申请资金",
        1, // FUNDING
        ethers.parseEther("1"),
        addr2.address
      );
      proposalId = 0;
    });

    it("应该能够执行通过的提案", async function () {
      // 投票支持
      await dao.connect(addr1).vote(proposalId, true);
      
      // 快进到投票期结束
      await time.increase(VOTING_PERIOD + 1);
      
      // 记录执行前的余额
      const balanceBefore = await ethers.provider.getBalance(addr2.address);
      
      // 执行提案
      await dao.executeProposal(proposalId);
      
      // 检查余额变化
      const balanceAfter = await ethers.provider.getBalance(addr2.address);
      expect(balanceAfter - balanceBefore).to.equal(ethers.parseEther("1"));
      
      // 检查提案状态
      const proposal = await dao.getProposal(proposalId);
      expect(proposal[10]).to.equal(4); // EXECUTED
      expect(proposal[11]).to.be.true; // executed
    });

    it("不应该能够执行未通过的提案", async function () {
      // 投票反对
      await dao.connect(addr1).vote(proposalId, false);
      
      // 快进到投票期结束
      await time.increase(VOTING_PERIOD + 1);
      
      await expect(
        dao.executeProposal(proposalId)
      ).to.be.revertedWith("Proposal not succeeded");
    });

    it("不应该能够重复执行提案", async function () {
      // 投票支持并执行
      await dao.connect(addr1).vote(proposalId, true);
      await time.increase(VOTING_PERIOD + 1);
      await dao.executeProposal(proposalId);
      
      // 尝试再次执行
      await expect(
        dao.executeProposal(proposalId)
      ).to.be.revertedWith("Proposal already executed");
    });

    it("资金不足时不应该能够执行资金提案", async function () {
      // 创建超出资金池的提案
      await dao.connect(addr1).createProposal(
        "申请过多资金",
        1, // FUNDING
        ethers.parseEther("10"), // 超出资金池
        addr2.address
      );
      
      const newProposalId = 1;
      await dao.connect(addr1).vote(newProposalId, true);
      await time.increase(VOTING_PERIOD + 1);
      
      await expect(
        dao.executeProposal(newProposalId)
      ).to.be.revertedWith("Insufficient treasury funds");
    });

    it("应该触发提案执行事件", async function () {
      await dao.connect(addr1).vote(proposalId, true);
      await time.increase(VOTING_PERIOD + 1);
      
      await expect(dao.executeProposal(proposalId))
        .to.emit(dao, "ProposalExecuted")
        .withArgs(proposalId);
    });
  });

  describe("参数管理", function () {
    it("所有者应该能够设置投票期限", async function () {
      const newPeriod = 7 * 24 * 60 * 60; // 7天
      
      await dao.setVotingPeriod(newPeriod);
      
      expect(await dao.votingPeriod()).to.equal(newPeriod);
    });

    it("所有者应该能够设置提案阈值", async function () {
      const newThreshold = ethers.parseEther("2000");
      
      await dao.setProposalThreshold(newThreshold);
      
      expect(await dao.proposalThreshold()).to.equal(newThreshold);
    });

    it("所有者应该能够设置法定人数百分比", async function () {
      const newPercentage = 10;
      
      await dao.setQuorumPercentage(newPercentage);
      
      expect(await dao.quorumPercentage()).to.equal(newPercentage);
    });

    it("非所有者不应该能够修改参数", async function () {
      await expect(
        dao.connect(addr1).setVotingPeriod(7 * 24 * 60 * 60)
      ).to.be.revertedWithCustomError(dao, "OwnableUnauthorizedAccount");
    });
  });

  describe("边界条件", function () {
    it("应该处理零金额存款", async function () {
      await expect(
        dao.connect(addr1).depositFunds({ value: 0 })
      ).to.be.revertedWith("Must deposit some ETH");
    });

    it("应该处理空描述提案", async function () {
      await expect(
        dao.connect(addr1).createProposal("", 0, 0, ethers.ZeroAddress)
      ).to.be.revertedWith("Insufficient tokens to create proposal");
    });

    it("应该正确处理法定人数计算", async function () {
      // 创建提案
      await dao.connect(addr1).createProposal(
        "测试法定人数",
        0,
        0,
        ethers.ZeroAddress
      );
      
      // 计算所需的法定人数 (4% of total supply)
      const totalSupply = await token.totalSupply();
      const requiredQuorum = totalSupply * BigInt(4) / BigInt(100);
      
      // 投票但不达到法定人数
      await dao.connect(addr2).vote(0, true); // 只有5000代币
      
      await time.increase(VOTING_PERIOD + 1);
      
      // 更新提案状态
      await dao.updateProposalState(0);
      
      const proposal = await dao.getProposal(0);
      expect(proposal[10]).to.equal(3); // DEFEATED (因为未达到法定人数)
    });
  });
});
