// SPDX-License-Identifier: MIT
pragma solidity ^0.8.24;

import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import "@openzeppelin/contracts/utils/cryptography/ECDSA.sol";
import "@openzeppelin/contracts/utils/cryptography/MessageHashUtils.sol";

/**
 * @title CrossChainBridge
 * @dev 跨链桥合约，支持多链资产转移
 * 
 * 功能特性：
 * - 支持 ERC20 代币跨链转移
 * - 多签验证机制
 * - 手续费管理
 * - 暂停/恢复功能
 * - 支持多个目标链
 */
contract CrossChainBridge is Ownable, ReentrancyGuard {
    using ECDSA for bytes32;
    using MessageHashUtils for bytes32;
    
    // 支持的链 ID
    mapping(uint256 => bool) public supportedChains;
    
    // 支持的代币
    mapping(address => bool) public supportedTokens;
    
    // 验证者
    mapping(address => bool) public validators;
    address[] public validatorList;
    uint256 public requiredSignatures;
    
    // 跨链转账记录
    struct CrossChainTransfer {
        address token;
        address from;
        address to;
        uint256 amount;
        uint256 targetChainId;
        uint256 nonce;
        bool completed;
        uint256 timestamp;
    }
    
    mapping(bytes32 => CrossChainTransfer) public transfers;
    mapping(bytes32 => bool) public processedTransfers;
    mapping(address => uint256) public userNonces;
    
    // 手续费配置
    mapping(uint256 => uint256) public bridgeFees; // chainId => fee (in wei)
    mapping(address => uint256) public tokenFees;  // token => fee percentage (basis points)
    
    uint256 public constant MAX_FEE_PERCENTAGE = 1000; // 10%
    uint256 public constant FEE_DENOMINATOR = 10000;
    
    // 合约状态
    bool public paused = false;
    
    // 事件
    event TransferInitiated(
        bytes32 indexed transferId,
        address indexed token,
        address indexed from,
        address to,
        uint256 amount,
        uint256 targetChainId,
        uint256 nonce
    );
    
    event TransferCompleted(
        bytes32 indexed transferId,
        address indexed token,
        address indexed to,
        uint256 amount
    );
    
    event ValidatorAdded(address indexed validator);
    event ValidatorRemoved(address indexed validator);
    event RequiredSignaturesUpdated(uint256 newRequirement);
    event ChainSupported(uint256 indexed chainId);
    event ChainUnsupported(uint256 indexed chainId);
    event TokenSupported(address indexed token);
    event TokenUnsupported(address indexed token);
    event FeesUpdated(uint256 indexed chainId, uint256 bridgeFee, address indexed token, uint256 tokenFee);
    event ContractPaused();
    event ContractUnpaused();
    
    modifier notPaused() {
        require(!paused, "Contract is paused");
        _;
    }
    
    modifier onlyValidator() {
        require(validators[msg.sender], "Not a validator");
        _;
    }
    
    constructor(
        address[] memory _validators,
        uint256 _requiredSignatures,
        address _owner
    ) Ownable(_owner) {
        require(_validators.length > 0, "No validators provided");
        require(_requiredSignatures > 0 && _requiredSignatures <= _validators.length, "Invalid signature requirement");
        
        for (uint256 i = 0; i < _validators.length; i++) {
            require(_validators[i] != address(0), "Invalid validator address");
            require(!validators[_validators[i]], "Duplicate validator");
            
            validators[_validators[i]] = true;
            validatorList.push(_validators[i]);
        }
        
        requiredSignatures = _requiredSignatures;
    }
    
    /**
     * @dev 发起跨链转账
     */
    function initiateTransfer(
        address token,
        uint256 amount,
        address to,
        uint256 targetChainId
    ) external payable nonReentrant notPaused {
        require(supportedTokens[token], "Token not supported");
        require(supportedChains[targetChainId], "Target chain not supported");
        require(amount > 0, "Invalid amount");
        require(to != address(0), "Invalid recipient");
        require(targetChainId != block.chainid, "Cannot bridge to same chain");
        
        // 检查桥接手续费
        uint256 bridgeFee = bridgeFees[targetChainId];
        require(msg.value >= bridgeFee, "Insufficient bridge fee");
        
        // 计算代币手续费
        uint256 tokenFeePercentage = tokenFees[token];
        uint256 tokenFee = (amount * tokenFeePercentage) / FEE_DENOMINATOR;
        uint256 transferAmount = amount - tokenFee;
        
        require(transferAmount > 0, "Amount too small after fees");
        
        // 转移代币到合约
        IERC20(token).transferFrom(msg.sender, address(this), amount);
        
        // 生成转账 ID
        uint256 nonce = userNonces[msg.sender]++;
        bytes32 transferId = keccak256(abi.encodePacked(
            token,
            msg.sender,
            to,
            transferAmount,
            targetChainId,
            nonce,
            block.chainid
        ));
        
        // 记录转账信息
        transfers[transferId] = CrossChainTransfer({
            token: token,
            from: msg.sender,
            to: to,
            amount: transferAmount,
            targetChainId: targetChainId,
            nonce: nonce,
            completed: false,
            timestamp: block.timestamp
        });
        
        emit TransferInitiated(transferId, token, msg.sender, to, transferAmount, targetChainId, nonce);
    }
    
    /**
     * @dev 完成跨链转账 (需要多签验证)
     */
    function completeTransfer(
        bytes32 transferId,
        address token,
        address to,
        uint256 amount,
        uint256 sourceChainId,
        bytes[] calldata signatures
    ) external nonReentrant notPaused {
        require(!processedTransfers[transferId], "Transfer already processed");
        require(supportedTokens[token], "Token not supported");
        require(signatures.length >= requiredSignatures, "Insufficient signatures");
        
        // 验证签名
        bytes32 messageHash = keccak256(abi.encodePacked(
            transferId,
            token,
            to,
            amount,
            sourceChainId,
            block.chainid
        )).toEthSignedMessageHash();
        
        address[] memory signers = new address[](signatures.length);
        for (uint256 i = 0; i < signatures.length; i++) {
            address signer = messageHash.recover(signatures[i]);
            require(validators[signer], "Invalid validator signature");
            
            // 检查重复签名
            for (uint256 j = 0; j < i; j++) {
                require(signers[j] != signer, "Duplicate signature");
            }
            signers[i] = signer;
        }
        
        // 标记为已处理
        processedTransfers[transferId] = true;
        
        // 转移代币给接收者
        IERC20(token).transfer(to, amount);
        
        emit TransferCompleted(transferId, token, to, amount);
    }
    
    /**
     * @dev 添加验证者
     */
    function addValidator(address validator) external onlyOwner {
        require(validator != address(0), "Invalid validator address");
        require(!validators[validator], "Validator already exists");
        
        validators[validator] = true;
        validatorList.push(validator);
        
        emit ValidatorAdded(validator);
    }
    
    /**
     * @dev 移除验证者
     */
    function removeValidator(address validator) external onlyOwner {
        require(validators[validator], "Validator does not exist");
        require(validatorList.length > requiredSignatures, "Cannot remove validator below requirement");
        
        validators[validator] = false;
        
        // 从数组中移除
        for (uint256 i = 0; i < validatorList.length; i++) {
            if (validatorList[i] == validator) {
                validatorList[i] = validatorList[validatorList.length - 1];
                validatorList.pop();
                break;
            }
        }
        
        emit ValidatorRemoved(validator);
    }
    
    /**
     * @dev 更新所需签名数量
     */
    function updateRequiredSignatures(uint256 newRequirement) external onlyOwner {
        require(newRequirement > 0 && newRequirement <= validatorList.length, "Invalid requirement");
        
        requiredSignatures = newRequirement;
        emit RequiredSignaturesUpdated(newRequirement);
    }
    
    /**
     * @dev 添加支持的链
     */
    function addSupportedChain(uint256 chainId) external onlyOwner {
        require(chainId != block.chainid, "Cannot add current chain");
        require(!supportedChains[chainId], "Chain already supported");
        
        supportedChains[chainId] = true;
        emit ChainSupported(chainId);
    }
    
    /**
     * @dev 移除支持的链
     */
    function removeSupportedChain(uint256 chainId) external onlyOwner {
        require(supportedChains[chainId], "Chain not supported");
        
        supportedChains[chainId] = false;
        emit ChainUnsupported(chainId);
    }
    
    /**
     * @dev 添加支持的代币
     */
    function addSupportedToken(address token) external onlyOwner {
        require(token != address(0), "Invalid token address");
        require(!supportedTokens[token], "Token already supported");
        
        supportedTokens[token] = true;
        emit TokenSupported(token);
    }
    
    /**
     * @dev 移除支持的代币
     */
    function removeSupportedToken(address token) external onlyOwner {
        require(supportedTokens[token], "Token not supported");
        
        supportedTokens[token] = false;
        emit TokenUnsupported(token);
    }
    
    /**
     * @dev 设置桥接手续费
     */
    function setBridgeFee(uint256 chainId, uint256 fee) external onlyOwner {
        bridgeFees[chainId] = fee;
        emit FeesUpdated(chainId, fee, address(0), 0);
    }
    
    /**
     * @dev 设置代币手续费
     */
    function setTokenFee(address token, uint256 feePercentage) external onlyOwner {
        require(feePercentage <= MAX_FEE_PERCENTAGE, "Fee too high");
        
        tokenFees[token] = feePercentage;
        emit FeesUpdated(0, 0, token, feePercentage);
    }
    
    /**
     * @dev 暂停合约
     */
    function pause() external onlyOwner {
        paused = true;
        emit ContractPaused();
    }
    
    /**
     * @dev 恢复合约
     */
    function unpause() external onlyOwner {
        paused = false;
        emit ContractUnpaused();
    }
    
    /**
     * @dev 提取手续费
     */
    function withdrawFees() external onlyOwner {
        uint256 balance = address(this).balance;
        require(balance > 0, "No fees to withdraw");
        
        (bool success, ) = payable(owner()).call{value: balance}("");
        require(success, "Withdrawal failed");
    }
    
    /**
     * @dev 紧急提取代币
     */
    function emergencyWithdraw(address token, uint256 amount) external onlyOwner {
        IERC20(token).transfer(owner(), amount);
    }
    
    /**
     * @dev 获取验证者列表
     */
    function getValidators() external view returns (address[] memory) {
        return validatorList;
    }
    
    /**
     * @dev 获取转账信息
     */
    function getTransfer(bytes32 transferId) external view returns (
        address token,
        address from,
        address to,
        uint256 amount,
        uint256 targetChainId,
        uint256 nonce,
        bool completed,
        uint256 timestamp
    ) {
        CrossChainTransfer memory transfer = transfers[transferId];
        return (
            transfer.token,
            transfer.from,
            transfer.to,
            transfer.amount,
            transfer.targetChainId,
            transfer.nonce,
            transfer.completed,
            transfer.timestamp
        );
    }
    
    /**
     * @dev 计算转账费用
     */
    function calculateFees(address token, uint256 amount, uint256 targetChainId) 
        external 
        view 
        returns (uint256 bridgeFee, uint256 tokenFee, uint256 totalAmount) 
    {
        bridgeFee = bridgeFees[targetChainId];
        tokenFee = (amount * tokenFees[token]) / FEE_DENOMINATOR;
        totalAmount = amount - tokenFee;
    }
}
