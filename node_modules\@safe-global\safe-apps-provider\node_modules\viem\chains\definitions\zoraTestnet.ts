import { chainConfig } from '../../op-stack/chainConfig.js'
import { define<PERSON>hain } from '../../utils/chain/defineChain.js'

const sourceId = 5 // goerli

export const zoraTestnet = /*#__PURE__*/ defineChain({
  ...chainConfig,
  id: 999,
  name: 'Zora Goerli Testnet',
  nativeCurrency: {
    decimals: 18,
    name: '<PERSON><PERSON>',
    symbol: 'ETH',
  },
  rpcUrls: {
    default: {
      http: ['https://testnet.rpc.zora.energy'],
      webSocket: ['wss://testnet.rpc.zora.energy'],
    },
  },
  blockExplorers: {
    default: {
      name: 'Explorer',
      url: 'https://testnet.explorer.zora.energy',
      apiUrl: 'https://testnet.explorer.zora.energy/api',
    },
  },
  contracts: {
    ...chainConfig.contracts,
    multicall3: {
      address: '******************************************',
      blockCreated: 189123,
    },
    portal: {
      [sourceId]: {
        address: '******************************************',
      },
    },
  },
  sourceId,
  testnet: true,
})
