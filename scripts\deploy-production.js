const { ethers } = require("hardhat");
const fs = require("fs");
const path = require("path");

/**
 * 生产环境部署脚本
 * 包含完整的部署流程、验证和配置
 */

async function main() {
  console.log("🚀 开始生产环境部署...\n");

  const [deployer] = await ethers.getSigners();
  const network = await ethers.provider.getNetwork();
  
  console.log("部署信息:");
  console.log("=====================================");
  console.log("部署账户:", deployer.address);
  console.log("网络:", network.name);
  console.log("链 ID:", network.chainId);
  console.log("账户余额:", ethers.formatEther(await deployer.provider.getBalance(deployer.address)), "ETH");
  console.log("=====================================\n");

  // 确认部署
  if (network.chainId === 1n || network.chainId === 137n || network.chainId === 56n) {
    console.log("⚠️  您正在部署到主网！");
    console.log("请确认以下信息:");
    console.log("1. 账户有足够的资金支付 gas 费用");
    console.log("2. 合约代码已经过充分测试");
    console.log("3. 已完成安全审计");
    console.log("4. 环境变量配置正确\n");
    
    // 在实际部署中，这里应该有用户确认步骤
    // 为了自动化，我们跳过确认
  }

  const deployedContracts = {};
  const deploymentGasCosts = {};

  try {
    // 1. 部署 SimpleToken
    console.log("📄 部署 SimpleToken...");
    const SimpleToken = await ethers.getContractFactory("SimpleToken");
    
    const tokenDeployTx = await SimpleToken.getDeployTransaction(
      "Web3 Ecosystem Token",
      "W3T",
      ethers.parseEther("100000000"), // 1亿代币总供应量
      deployer.address
    );
    
    const estimatedGas = await deployer.estimateGas(tokenDeployTx);
    console.log(`  预估 Gas: ${estimatedGas.toString()}`);
    
    const simpleToken = await SimpleToken.deploy(
      "Web3 Ecosystem Token",
      "W3T",
      ethers.parseEther("100000000"),
      deployer.address
    );
    
    const tokenReceipt = await simpleToken.deploymentTransaction().wait();
    deployedContracts.SimpleToken = await simpleToken.getAddress();
    deploymentGasCosts.SimpleToken = tokenReceipt.gasUsed;
    
    console.log("✅ SimpleToken 部署成功:", deployedContracts.SimpleToken);
    console.log(`  实际 Gas 使用: ${tokenReceipt.gasUsed.toString()}\n`);

    // 2. 部署 Web3NFT
    console.log("🎨 部署 Web3NFT...");
    const Web3NFT = await ethers.getContractFactory("Web3NFT");
    
    const web3NFT = await Web3NFT.deploy(
      "Web3 Ecosystem NFT",
      "W3NFT",
      "https://api.web3ecosystem.com/metadata/",
      deployer.address,
      deployer.address
    );
    
    const nftReceipt = await web3NFT.deploymentTransaction().wait();
    deployedContracts.Web3NFT = await web3NFT.getAddress();
    deploymentGasCosts.Web3NFT = nftReceipt.gasUsed;
    
    console.log("✅ Web3NFT 部署成功:", deployedContracts.Web3NFT);
    console.log(`  实际 Gas 使用: ${nftReceipt.gasUsed.toString()}\n`);

    // 3. 部署 NFT 市场
    console.log("🏪 部署 NFTMarketplace...");
    const NFTMarketplace = await ethers.getContractFactory("NFTMarketplace");
    
    const nftMarketplace = await NFTMarketplace.deploy(deployer.address);
    const marketplaceReceipt = await nftMarketplace.deploymentTransaction().wait();
    deployedContracts.NFTMarketplace = await nftMarketplace.getAddress();
    deploymentGasCosts.NFTMarketplace = marketplaceReceipt.gasUsed;
    
    console.log("✅ NFTMarketplace 部署成功:", deployedContracts.NFTMarketplace);
    console.log(`  实际 Gas 使用: ${marketplaceReceipt.gasUsed.toString()}\n`);

    // 4. 部署 SimpleDAO
    console.log("🏛️ 部署 SimpleDAO...");
    const SimpleDAO = await ethers.getContractFactory("SimpleDAO");
    
    const simpleDAO = await SimpleDAO.deploy(
      deployedContracts.SimpleToken,
      deployer.address
    );
    
    const daoReceipt = await simpleDAO.deploymentTransaction().wait();
    deployedContracts.SimpleDAO = await simpleDAO.getAddress();
    deploymentGasCosts.SimpleDAO = daoReceipt.gasUsed;
    
    console.log("✅ SimpleDAO 部署成功:", deployedContracts.SimpleDAO);
    console.log(`  实际 Gas 使用: ${daoReceipt.gasUsed.toString()}\n`);

    // 5. 部署多签钱包
    console.log("🔐 部署 MultiSigWallet...");
    const MultiSigWallet = await ethers.getContractFactory("MultiSigWallet");
    
    const multiSigWallet = await MultiSigWallet.deploy(
      [deployer.address], // 初始签名者
      1 // 需要1个签名
    );
    
    const multiSigReceipt = await multiSigWallet.deploymentTransaction().wait();
    deployedContracts.MultiSigWallet = await multiSigWallet.getAddress();
    deploymentGasCosts.MultiSigWallet = multiSigReceipt.gasUsed;
    
    console.log("✅ MultiSigWallet 部署成功:", deployedContracts.MultiSigWallet);
    console.log(`  实际 Gas 使用: ${multiSigReceipt.gasUsed.toString()}\n`);

    // 6. 配置合约
    console.log("⚙️ 配置合约...");
    
    // 设置 NFT 为公开铸造
    const setMintPhaseTx = await web3NFT.setMintPhase(2); // PUBLIC = 2
    await setMintPhaseTx.wait();
    console.log("   ✅ NFT 设置为公开铸造");

    // 设置合理的铸造价格 (0.01 ETH)
    const setMintPriceTx = await web3NFT.setMintPrice(ethers.parseEther("0.01"));
    await setMintPriceTx.wait();
    console.log("   ✅ NFT 铸造价格设置为 0.01 ETH");

    console.log("✅ 合约配置完成\n");

    // 7. 计算总 Gas 费用
    const totalGasUsed = Object.values(deploymentGasCosts).reduce((a, b) => a + b, 0n);
    const gasPrice = (await ethers.provider.getFeeData()).gasPrice;
    const totalCost = totalGasUsed * gasPrice;
    
    console.log("💰 部署成本统计:");
    console.log("=====================================");
    Object.entries(deploymentGasCosts).forEach(([contract, gas]) => {
      const cost = gas * gasPrice;
      console.log(`${contract.padEnd(20)}: ${gas.toString().padStart(10)} gas (${ethers.formatEther(cost)} ETH)`);
    });
    console.log("-------------------------------------");
    console.log(`总计: ${totalGasUsed.toString().padStart(10)} gas (${ethers.formatEther(totalCost)} ETH)`);
    console.log("=====================================\n");

    // 8. 保存部署信息
    console.log("💾 保存部署信息...");
    const deploymentInfo = {
      network: {
        name: network.name,
        chainId: Number(network.chainId),
        blockNumber: await ethers.provider.getBlockNumber()
      },
      deployer: {
        address: deployer.address,
        balance: ethers.formatEther(await deployer.provider.getBalance(deployer.address))
      },
      contracts: deployedContracts,
      gasCosts: Object.fromEntries(
        Object.entries(deploymentGasCosts).map(([k, v]) => [k, v.toString()])
      ),
      totalGasUsed: totalGasUsed.toString(),
      totalCostETH: ethers.formatEther(totalCost),
      timestamp: new Date().toISOString(),
      version: "1.0.0"
    };

    // 保存到 deployments 目录
    const deploymentsDir = path.join(__dirname, "../deployments");
    if (!fs.existsSync(deploymentsDir)) {
      fs.mkdirSync(deploymentsDir, { recursive: true });
    }

    const networkName = network.name || "unknown";
    const filename = `${networkName}-${Date.now()}.json`;
    const filepath = path.join(deploymentsDir, filename);

    fs.writeFileSync(filepath, JSON.stringify(deploymentInfo, null, 2));
    console.log("✅ 部署信息已保存到:", filepath);

    // 9. 更新前端配置
    console.log("\n🔧 更新前端配置...");
    const frontendConfig = {
      contracts: deployedContracts,
      network: {
        chainId: Number(network.chainId),
        name: network.name
      },
      lastUpdated: new Date().toISOString()
    };

    const frontendConfigPath = path.join(__dirname, "../frontend/config/contracts.json");
    const frontendConfigDir = path.dirname(frontendConfigPath);
    
    if (!fs.existsSync(frontendConfigDir)) {
      fs.mkdirSync(frontendConfigDir, { recursive: true });
    }

    fs.writeFileSync(frontendConfigPath, JSON.stringify(frontendConfig, null, 2));
    console.log("✅ 前端配置文件已更新:", frontendConfigPath);

    // 10. 生成环境变量文件
    console.log("\n📝 生成环境变量配置...");
    const envConfig = Object.entries(deployedContracts)
      .map(([name, address]) => `NEXT_PUBLIC_${name.toUpperCase()}_ADDRESS=${address}`)
      .join('\n');
    
    const envPath = path.join(__dirname, "../.env.production");
    fs.writeFileSync(envPath, envConfig);
    console.log("✅ 生产环境变量已生成:", envPath);

    // 11. 显示部署摘要
    console.log("\n🎉 部署完成！");
    console.log("=====================================");
    console.log("合约地址:");
    Object.entries(deployedContracts).forEach(([name, address]) => {
      console.log(`  ${name.padEnd(20)}: ${address}`);
    });
    console.log("=====================================");

    console.log("\n📋 下一步操作:");
    console.log("1. 验证合约:");
    Object.entries(deployedContracts).forEach(([name, address]) => {
      console.log(`   npx hardhat verify --network ${network.name} ${address}`);
    });
    
    console.log("\n2. 更新前端环境变量");
    console.log("3. 部署前端到生产环境");
    console.log("4. 配置域名和 SSL");
    console.log("5. 设置监控和报警");
    console.log("6. 进行最终测试");

    // 12. 发送部署通知 (如果配置了 webhook)
    if (process.env.DISCORD_WEBHOOK_URL) {
      await sendDeploymentNotification(deploymentInfo);
    }

    return deployedContracts;

  } catch (error) {
    console.error("\n❌ 部署失败:", error);
    
    // 发送失败通知
    if (process.env.DISCORD_WEBHOOK_URL) {
      await sendFailureNotification(error);
    }
    
    throw error;
  }
}

async function sendDeploymentNotification(deploymentInfo) {
  try {
    const webhook = process.env.DISCORD_WEBHOOK_URL;
    if (!webhook) return;

    const message = {
      embeds: [{
        title: "🚀 Web3 生态系统部署成功",
        color: 0x00ff00,
        fields: [
          {
            name: "网络",
            value: `${deploymentInfo.network.name} (${deploymentInfo.network.chainId})`,
            inline: true
          },
          {
            name: "部署者",
            value: deploymentInfo.deployer.address,
            inline: true
          },
          {
            name: "总 Gas 费用",
            value: `${deploymentInfo.totalCostETH} ETH`,
            inline: true
          },
          {
            name: "合约数量",
            value: Object.keys(deploymentInfo.contracts).length.toString(),
            inline: true
          }
        ],
        timestamp: deploymentInfo.timestamp
      }]
    };

    await fetch(webhook, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(message)
    });

    console.log("✅ 部署通知已发送");
  } catch (error) {
    console.warn("⚠️ 发送通知失败:", error.message);
  }
}

async function sendFailureNotification(error) {
  try {
    const webhook = process.env.DISCORD_WEBHOOK_URL;
    if (!webhook) return;

    const message = {
      embeds: [{
        title: "❌ Web3 生态系统部署失败",
        color: 0xff0000,
        description: error.message,
        timestamp: new Date().toISOString()
      }]
    };

    await fetch(webhook, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(message)
    });
  } catch (notificationError) {
    console.warn("⚠️ 发送失败通知失败:", notificationError.message);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error(error);
      process.exit(1);
    });
}

module.exports = main;
