{"id": "1f3e82ca0e993e362c7d51d1397862e8", "_format": "hh-sol-build-info-1", "solcVersion": "0.8.24", "solcLongVersion": "0.8.24+commit.e11b9ed9", "input": {"language": "Solidity", "sources": {"@openzeppelin/contracts/access/Ownable.sol": {"content": "// SPDX-License-Identifier: MIT\n// OpenZeppelin Contracts (last updated v5.0.0) (access/Ownable.sol)\n\npragma solidity ^0.8.20;\n\nimport {Context} from \"../utils/Context.sol\";\n\n/**\n * @dev Contract module which provides a basic access control mechanism, where\n * there is an account (an owner) that can be granted exclusive access to\n * specific functions.\n *\n * The initial owner is set to the address provided by the deployer. This can\n * later be changed with {transferOwnership}.\n *\n * This module is used through inheritance. It will make available the modifier\n * `onlyOwner`, which can be applied to your functions to restrict their use to\n * the owner.\n */\nabstract contract Ownable is Context {\n    address private _owner;\n\n    /**\n     * @dev The caller account is not authorized to perform an operation.\n     */\n    error OwnableUnauthorizedAccount(address account);\n\n    /**\n     * @dev The owner is not a valid owner account. (eg. `address(0)`)\n     */\n    error OwnableInvalidOwner(address owner);\n\n    event OwnershipTransferred(address indexed previousOwner, address indexed newOwner);\n\n    /**\n     * @dev Initializes the contract setting the address provided by the deployer as the initial owner.\n     */\n    constructor(address initialOwner) {\n        if (initialOwner == address(0)) {\n            revert OwnableInvalidOwner(address(0));\n        }\n        _transferOwnership(initialOwner);\n    }\n\n    /**\n     * @dev Throws if called by any account other than the owner.\n     */\n    modifier onlyOwner() {\n        _checkOwner();\n        _;\n    }\n\n    /**\n     * @dev Returns the address of the current owner.\n     */\n    function owner() public view virtual returns (address) {\n        return _owner;\n    }\n\n    /**\n     * @dev Throws if the sender is not the owner.\n     */\n    function _checkOwner() internal view virtual {\n        if (owner() != _msgSender()) {\n            revert OwnableUnauthorizedAccount(_msgSender());\n        }\n    }\n\n    /**\n     * @dev Leaves the contract without owner. It will not be possible to call\n     * `onlyOwner` functions. Can only be called by the current owner.\n     *\n     * NOTE: Renouncing ownership will leave the contract without an owner,\n     * thereby disabling any functionality that is only available to the owner.\n     */\n    function renounceOwnership() public virtual onlyOwner {\n        _transferOwnership(address(0));\n    }\n\n    /**\n     * @dev Transfers ownership of the contract to a new account (`newOwner`).\n     * Can only be called by the current owner.\n     */\n    function transferOwnership(address newOwner) public virtual onlyOwner {\n        if (newOwner == address(0)) {\n            revert OwnableInvalidOwner(address(0));\n        }\n        _transferOwnership(newOwner);\n    }\n\n    /**\n     * @dev Transfers ownership of the contract to a new account (`newOwner`).\n     * Internal function without access restriction.\n     */\n    function _transferOwnership(address newOwner) internal virtual {\n        address oldOwner = _owner;\n        _owner = newOwner;\n        emit OwnershipTransferred(oldOwner, newOwner);\n    }\n}\n"}, "@openzeppelin/contracts/interfaces/draft-IERC6093.sol": {"content": "// SPDX-License-Identifier: MIT\n// OpenZeppelin Contracts (last updated v5.4.0) (interfaces/draft-IERC6093.sol)\npragma solidity >=0.8.4;\n\n/**\n * @dev Standard ERC-20 Errors\n * Interface of the https://eips.ethereum.org/EIPS/eip-6093[ERC-6093] custom errors for ERC-20 tokens.\n */\ninterface IERC20Errors {\n    /**\n     * @dev Indicates an error related to the current `balance` of a `sender`. Used in transfers.\n     * @param sender Address whose tokens are being transferred.\n     * @param balance Current balance for the interacting account.\n     * @param needed Minimum amount required to perform a transfer.\n     */\n    error ERC20InsufficientBalance(address sender, uint256 balance, uint256 needed);\n\n    /**\n     * @dev Indicates a failure with the token `sender`. Used in transfers.\n     * @param sender Address whose tokens are being transferred.\n     */\n    error ERC20InvalidSender(address sender);\n\n    /**\n     * @dev Indicates a failure with the token `receiver`. Used in transfers.\n     * @param receiver Address to which tokens are being transferred.\n     */\n    error ERC20Invalid<PERSON><PERSON><PERSON><PERSON>(address receiver);\n\n    /**\n     * @dev Indicates a failure with the `spender`’s `allowance`. Used in transfers.\n     * @param spender Address that may be allowed to operate on tokens without being their owner.\n     * @param allowance Amount of tokens a `spender` is allowed to operate with.\n     * @param needed Minimum amount required to perform a transfer.\n     */\n    error ERC20InsufficientAllowance(address spender, uint256 allowance, uint256 needed);\n\n    /**\n     * @dev Indicates a failure with the `approver` of a token to be approved. Used in approvals.\n     * @param approver Address initiating an approval operation.\n     */\n    error ERC20InvalidApprover(address approver);\n\n    /**\n     * @dev Indicates a failure with the `spender` to be approved. Used in approvals.\n     * @param spender Address that may be allowed to operate on tokens without being their owner.\n     */\n    error ERC20InvalidSpender(address spender);\n}\n\n/**\n * @dev Standard ERC-721 Errors\n * Interface of the https://eips.ethereum.org/EIPS/eip-6093[ERC-6093] custom errors for ERC-721 tokens.\n */\ninterface IERC721Errors {\n    /**\n     * @dev Indicates that an address can't be an owner. For example, `address(0)` is a forbidden owner in ERC-20.\n     * Used in balance queries.\n     * @param owner Address of the current owner of a token.\n     */\n    error ERC721InvalidOwner(address owner);\n\n    /**\n     * @dev Indicates a `tokenId` whose `owner` is the zero address.\n     * @param tokenId Identifier number of a token.\n     */\n    error ERC721NonexistentToken(uint256 tokenId);\n\n    /**\n     * @dev Indicates an error related to the ownership over a particular token. Used in transfers.\n     * @param sender Address whose tokens are being transferred.\n     * @param tokenId Identifier number of a token.\n     * @param owner Address of the current owner of a token.\n     */\n    error ERC721IncorrectOwner(address sender, uint256 tokenId, address owner);\n\n    /**\n     * @dev Indicates a failure with the token `sender`. Used in transfers.\n     * @param sender Address whose tokens are being transferred.\n     */\n    error ERC721InvalidSender(address sender);\n\n    /**\n     * @dev Indicates a failure with the token `receiver`. Used in transfers.\n     * @param receiver Address to which tokens are being transferred.\n     */\n    error ERC721InvalidReceiver(address receiver);\n\n    /**\n     * @dev Indicates a failure with the `operator`’s approval. Used in transfers.\n     * @param operator Address that may be allowed to operate on tokens without being their owner.\n     * @param tokenId Identifier number of a token.\n     */\n    error ERC721InsufficientApproval(address operator, uint256 tokenId);\n\n    /**\n     * @dev Indicates a failure with the `approver` of a token to be approved. Used in approvals.\n     * @param approver Address initiating an approval operation.\n     */\n    error ERC721InvalidApprover(address approver);\n\n    /**\n     * @dev Indicates a failure with the `operator` to be approved. Used in approvals.\n     * @param operator Address that may be allowed to operate on tokens without being their owner.\n     */\n    error ERC721InvalidOperator(address operator);\n}\n\n/**\n * @dev Standard ERC-1155 Errors\n * Interface of the https://eips.ethereum.org/EIPS/eip-6093[ERC-6093] custom errors for ERC-1155 tokens.\n */\ninterface IERC1155Errors {\n    /**\n     * @dev Indicates an error related to the current `balance` of a `sender`. Used in transfers.\n     * @param sender Address whose tokens are being transferred.\n     * @param balance Current balance for the interacting account.\n     * @param needed Minimum amount required to perform a transfer.\n     * @param tokenId Identifier number of a token.\n     */\n    error ERC1155InsufficientBalance(address sender, uint256 balance, uint256 needed, uint256 tokenId);\n\n    /**\n     * @dev Indicates a failure with the token `sender`. Used in transfers.\n     * @param sender Address whose tokens are being transferred.\n     */\n    error ERC1155InvalidSender(address sender);\n\n    /**\n     * @dev Indicates a failure with the token `receiver`. Used in transfers.\n     * @param receiver Address to which tokens are being transferred.\n     */\n    error ERC1155InvalidReceiver(address receiver);\n\n    /**\n     * @dev Indicates a failure with the `operator`’s approval. Used in transfers.\n     * @param operator Address that may be allowed to operate on tokens without being their owner.\n     * @param owner Address of the current owner of a token.\n     */\n    error ERC1155MissingApprovalForAll(address operator, address owner);\n\n    /**\n     * @dev Indicates a failure with the `approver` of a token to be approved. Used in approvals.\n     * @param approver Address initiating an approval operation.\n     */\n    error ERC1155InvalidApprover(address approver);\n\n    /**\n     * @dev Indicates a failure with the `operator` to be approved. Used in approvals.\n     * @param operator Address that may be allowed to operate on tokens without being their owner.\n     */\n    error ERC1155InvalidOperator(address operator);\n\n    /**\n     * @dev Indicates an array length mismatch between ids and values in a safeBatchTransferFrom operation.\n     * Used in batch transfers.\n     * @param idsLength Length of the array of token identifiers\n     * @param valuesLength Length of the array of token amounts\n     */\n    error ERC1155InvalidArrayLength(uint256 idsLength, uint256 valuesLength);\n}\n"}, "@openzeppelin/contracts/token/ERC20/ERC20.sol": {"content": "// SPDX-License-Identifier: MIT\n// OpenZeppelin Contracts (last updated v5.4.0) (token/ERC20/ERC20.sol)\n\npragma solidity ^0.8.20;\n\nimport {IERC20} from \"./IERC20.sol\";\nimport {IERC20Metadata} from \"./extensions/IERC20Metadata.sol\";\nimport {Context} from \"../../utils/Context.sol\";\nimport {IERC20Errors} from \"../../interfaces/draft-IERC6093.sol\";\n\n/**\n * @dev Implementation of the {IERC20} interface.\n *\n * This implementation is agnostic to the way tokens are created. This means\n * that a supply mechanism has to be added in a derived contract using {_mint}.\n *\n * TIP: For a detailed writeup see our guide\n * https://forum.openzeppelin.com/t/how-to-implement-erc20-supply-mechanisms/226[How\n * to implement supply mechanisms].\n *\n * The default value of {decimals} is 18. To change this, you should override\n * this function so it returns a different value.\n *\n * We have followed general OpenZeppelin Contracts guidelines: functions revert\n * instead returning `false` on failure. This behavior is nonetheless\n * conventional and does not conflict with the expectations of ERC-20\n * applications.\n */\nabstract contract ERC20 is Context, IERC20, IERC20Metadata, IERC20Errors {\n    mapping(address account => uint256) private _balances;\n\n    mapping(address account => mapping(address spender => uint256)) private _allowances;\n\n    uint256 private _totalSupply;\n\n    string private _name;\n    string private _symbol;\n\n    /**\n     * @dev Sets the values for {name} and {symbol}.\n     *\n     * Both values are immutable: they can only be set once during construction.\n     */\n    constructor(string memory name_, string memory symbol_) {\n        _name = name_;\n        _symbol = symbol_;\n    }\n\n    /**\n     * @dev Returns the name of the token.\n     */\n    function name() public view virtual returns (string memory) {\n        return _name;\n    }\n\n    /**\n     * @dev Returns the symbol of the token, usually a shorter version of the\n     * name.\n     */\n    function symbol() public view virtual returns (string memory) {\n        return _symbol;\n    }\n\n    /**\n     * @dev Returns the number of decimals used to get its user representation.\n     * For example, if `decimals` equals `2`, a balance of `505` tokens should\n     * be displayed to a user as `5.05` (`505 / 10 ** 2`).\n     *\n     * Tokens usually opt for a value of 18, imitating the relationship between\n     * Ether and Wei. This is the default value returned by this function, unless\n     * it's overridden.\n     *\n     * NOTE: This information is only used for _display_ purposes: it in\n     * no way affects any of the arithmetic of the contract, including\n     * {IERC20-balanceOf} and {IERC20-transfer}.\n     */\n    function decimals() public view virtual returns (uint8) {\n        return 18;\n    }\n\n    /// @inheritdoc IERC20\n    function totalSupply() public view virtual returns (uint256) {\n        return _totalSupply;\n    }\n\n    /// @inheritdoc IERC20\n    function balanceOf(address account) public view virtual returns (uint256) {\n        return _balances[account];\n    }\n\n    /**\n     * @dev See {IERC20-transfer}.\n     *\n     * Requirements:\n     *\n     * - `to` cannot be the zero address.\n     * - the caller must have a balance of at least `value`.\n     */\n    function transfer(address to, uint256 value) public virtual returns (bool) {\n        address owner = _msgSender();\n        _transfer(owner, to, value);\n        return true;\n    }\n\n    /// @inheritdoc IERC20\n    function allowance(address owner, address spender) public view virtual returns (uint256) {\n        return _allowances[owner][spender];\n    }\n\n    /**\n     * @dev See {IERC20-approve}.\n     *\n     * NOTE: If `value` is the maximum `uint256`, the allowance is not updated on\n     * `transferFrom`. This is semantically equivalent to an infinite approval.\n     *\n     * Requirements:\n     *\n     * - `spender` cannot be the zero address.\n     */\n    function approve(address spender, uint256 value) public virtual returns (bool) {\n        address owner = _msgSender();\n        _approve(owner, spender, value);\n        return true;\n    }\n\n    /**\n     * @dev See {IERC20-transferFrom}.\n     *\n     * Skips emitting an {Approval} event indicating an allowance update. This is not\n     * required by the ERC. See {xref-ERC20-_approve-address-address-uint256-bool-}[_approve].\n     *\n     * NOTE: Does not update the allowance if the current allowance\n     * is the maximum `uint256`.\n     *\n     * Requirements:\n     *\n     * - `from` and `to` cannot be the zero address.\n     * - `from` must have a balance of at least `value`.\n     * - the caller must have allowance for ``from``'s tokens of at least\n     * `value`.\n     */\n    function transferFrom(address from, address to, uint256 value) public virtual returns (bool) {\n        address spender = _msgSender();\n        _spendAllowance(from, spender, value);\n        _transfer(from, to, value);\n        return true;\n    }\n\n    /**\n     * @dev Moves a `value` amount of tokens from `from` to `to`.\n     *\n     * This internal function is equivalent to {transfer}, and can be used to\n     * e.g. implement automatic token fees, slashing mechanisms, etc.\n     *\n     * Emits a {Transfer} event.\n     *\n     * NOTE: This function is not virtual, {_update} should be overridden instead.\n     */\n    function _transfer(address from, address to, uint256 value) internal {\n        if (from == address(0)) {\n            revert ERC20InvalidSender(address(0));\n        }\n        if (to == address(0)) {\n            revert ERC20InvalidReceiver(address(0));\n        }\n        _update(from, to, value);\n    }\n\n    /**\n     * @dev Transfers a `value` amount of tokens from `from` to `to`, or alternatively mints (or burns) if `from`\n     * (or `to`) is the zero address. All customizations to transfers, mints, and burns should be done by overriding\n     * this function.\n     *\n     * Emits a {Transfer} event.\n     */\n    function _update(address from, address to, uint256 value) internal virtual {\n        if (from == address(0)) {\n            // Overflow check required: The rest of the code assumes that totalSupply never overflows\n            _totalSupply += value;\n        } else {\n            uint256 fromBalance = _balances[from];\n            if (fromBalance < value) {\n                revert ERC20InsufficientBalance(from, fromBalance, value);\n            }\n            unchecked {\n                // Overflow not possible: value <= fromBalance <= totalSupply.\n                _balances[from] = fromBalance - value;\n            }\n        }\n\n        if (to == address(0)) {\n            unchecked {\n                // Overflow not possible: value <= totalSupply or value <= fromBalance <= totalSupply.\n                _totalSupply -= value;\n            }\n        } else {\n            unchecked {\n                // Overflow not possible: balance + value is at most totalSupply, which we know fits into a uint256.\n                _balances[to] += value;\n            }\n        }\n\n        emit Transfer(from, to, value);\n    }\n\n    /**\n     * @dev Creates a `value` amount of tokens and assigns them to `account`, by transferring it from address(0).\n     * Relies on the `_update` mechanism\n     *\n     * Emits a {Transfer} event with `from` set to the zero address.\n     *\n     * NOTE: This function is not virtual, {_update} should be overridden instead.\n     */\n    function _mint(address account, uint256 value) internal {\n        if (account == address(0)) {\n            revert ERC20InvalidReceiver(address(0));\n        }\n        _update(address(0), account, value);\n    }\n\n    /**\n     * @dev Destroys a `value` amount of tokens from `account`, lowering the total supply.\n     * Relies on the `_update` mechanism.\n     *\n     * Emits a {Transfer} event with `to` set to the zero address.\n     *\n     * NOTE: This function is not virtual, {_update} should be overridden instead\n     */\n    function _burn(address account, uint256 value) internal {\n        if (account == address(0)) {\n            revert ERC20InvalidSender(address(0));\n        }\n        _update(account, address(0), value);\n    }\n\n    /**\n     * @dev Sets `value` as the allowance of `spender` over the `owner`'s tokens.\n     *\n     * This internal function is equivalent to `approve`, and can be used to\n     * e.g. set automatic allowances for certain subsystems, etc.\n     *\n     * Emits an {Approval} event.\n     *\n     * Requirements:\n     *\n     * - `owner` cannot be the zero address.\n     * - `spender` cannot be the zero address.\n     *\n     * Overrides to this logic should be done to the variant with an additional `bool emitEvent` argument.\n     */\n    function _approve(address owner, address spender, uint256 value) internal {\n        _approve(owner, spender, value, true);\n    }\n\n    /**\n     * @dev Variant of {_approve} with an optional flag to enable or disable the {Approval} event.\n     *\n     * By default (when calling {_approve}) the flag is set to true. On the other hand, approval changes made by\n     * `_spendAllowance` during the `transferFrom` operation set the flag to false. This saves gas by not emitting any\n     * `Approval` event during `transferFrom` operations.\n     *\n     * Anyone who wishes to continue emitting `Approval` events on the`transferFrom` operation can force the flag to\n     * true using the following override:\n     *\n     * ```solidity\n     * function _approve(address owner, address spender, uint256 value, bool) internal virtual override {\n     *     super._approve(owner, spender, value, true);\n     * }\n     * ```\n     *\n     * Requirements are the same as {_approve}.\n     */\n    function _approve(address owner, address spender, uint256 value, bool emitEvent) internal virtual {\n        if (owner == address(0)) {\n            revert ERC20InvalidApprover(address(0));\n        }\n        if (spender == address(0)) {\n            revert ERC20InvalidSpender(address(0));\n        }\n        _allowances[owner][spender] = value;\n        if (emitEvent) {\n            emit Approval(owner, spender, value);\n        }\n    }\n\n    /**\n     * @dev Updates `owner`'s allowance for `spender` based on spent `value`.\n     *\n     * Does not update the allowance value in case of infinite allowance.\n     * Revert if not enough allowance is available.\n     *\n     * Does not emit an {Approval} event.\n     */\n    function _spendAllowance(address owner, address spender, uint256 value) internal virtual {\n        uint256 currentAllowance = allowance(owner, spender);\n        if (currentAllowance < type(uint256).max) {\n            if (currentAllowance < value) {\n                revert ERC20InsufficientAllowance(spender, currentAllowance, value);\n            }\n            unchecked {\n                _approve(owner, spender, currentAllowance - value, false);\n            }\n        }\n    }\n}\n"}, "@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol": {"content": "// SPDX-License-Identifier: MIT\n// OpenZeppelin Contracts (last updated v5.4.0) (token/ERC20/extensions/IERC20Metadata.sol)\n\npragma solidity >=0.6.2;\n\nimport {IERC20} from \"../IERC20.sol\";\n\n/**\n * @dev Interface for the optional metadata functions from the ERC-20 standard.\n */\ninterface IERC20Metadata is IERC20 {\n    /**\n     * @dev Returns the name of the token.\n     */\n    function name() external view returns (string memory);\n\n    /**\n     * @dev Returns the symbol of the token.\n     */\n    function symbol() external view returns (string memory);\n\n    /**\n     * @dev Returns the decimals places of the token.\n     */\n    function decimals() external view returns (uint8);\n}\n"}, "@openzeppelin/contracts/token/ERC20/IERC20.sol": {"content": "// SPDX-License-Identifier: MIT\n// OpenZeppelin Contracts (last updated v5.4.0) (token/ERC20/IERC20.sol)\n\npragma solidity >=0.4.16;\n\n/**\n * @dev Interface of the ERC-20 standard as defined in the ERC.\n */\ninterface IERC20 {\n    /**\n     * @dev Emitted when `value` tokens are moved from one account (`from`) to\n     * another (`to`).\n     *\n     * Note that `value` may be zero.\n     */\n    event Transfer(address indexed from, address indexed to, uint256 value);\n\n    /**\n     * @dev Emitted when the allowance of a `spender` for an `owner` is set by\n     * a call to {approve}. `value` is the new allowance.\n     */\n    event Approval(address indexed owner, address indexed spender, uint256 value);\n\n    /**\n     * @dev Returns the value of tokens in existence.\n     */\n    function totalSupply() external view returns (uint256);\n\n    /**\n     * @dev Returns the value of tokens owned by `account`.\n     */\n    function balanceOf(address account) external view returns (uint256);\n\n    /**\n     * @dev Moves a `value` amount of tokens from the caller's account to `to`.\n     *\n     * Returns a boolean value indicating whether the operation succeeded.\n     *\n     * Emits a {Transfer} event.\n     */\n    function transfer(address to, uint256 value) external returns (bool);\n\n    /**\n     * @dev Returns the remaining number of tokens that `spender` will be\n     * allowed to spend on behalf of `owner` through {transferFrom}. This is\n     * zero by default.\n     *\n     * This value changes when {approve} or {transferFrom} are called.\n     */\n    function allowance(address owner, address spender) external view returns (uint256);\n\n    /**\n     * @dev Sets a `value` amount of tokens as the allowance of `spender` over the\n     * caller's tokens.\n     *\n     * Returns a boolean value indicating whether the operation succeeded.\n     *\n     * IMPORTANT: Beware that changing an allowance with this method brings the risk\n     * that someone may use both the old and the new allowance by unfortunate\n     * transaction ordering. One possible solution to mitigate this race\n     * condition is to first reduce the spender's allowance to 0 and set the\n     * desired value afterwards:\n     * https://github.com/ethereum/EIPs/issues/20#issuecomment-*********\n     *\n     * Emits an {Approval} event.\n     */\n    function approve(address spender, uint256 value) external returns (bool);\n\n    /**\n     * @dev Moves a `value` amount of tokens from `from` to `to` using the\n     * allowance mechanism. `value` is then deducted from the caller's\n     * allowance.\n     *\n     * Returns a boolean value indicating whether the operation succeeded.\n     *\n     * Emits a {Transfer} event.\n     */\n    function transferFrom(address from, address to, uint256 value) external returns (bool);\n}\n"}, "@openzeppelin/contracts/utils/Context.sol": {"content": "// SPDX-License-Identifier: MIT\n// OpenZeppelin Contracts (last updated v5.0.1) (utils/Context.sol)\n\npragma solidity ^0.8.20;\n\n/**\n * @dev Provides information about the current execution context, including the\n * sender of the transaction and its data. While these are generally available\n * via msg.sender and msg.data, they should not be accessed in such a direct\n * manner, since when dealing with meta-transactions the account sending and\n * paying for execution may not be the actual sender (as far as an application\n * is concerned).\n *\n * This contract is only required for intermediate, library-like contracts.\n */\nabstract contract Context {\n    function _msgSender() internal view virtual returns (address) {\n        return msg.sender;\n    }\n\n    function _msgData() internal view virtual returns (bytes calldata) {\n        return msg.data;\n    }\n\n    function _contextSuffixLength() internal view virtual returns (uint256) {\n        return 0;\n    }\n}\n"}, "contracts/tokens/SimpleToken.sol": {"content": "// SPDX-License-Identifier: MIT\npragma solidity ^0.8.24;\n\nimport \"@openzeppelin/contracts/token/ERC20/ERC20.sol\";\nimport \"@openzeppelin/contracts/access/Ownable.sol\";\n\n/**\n * @title SimpleToken\n * @dev 简化的 ERC-20 代币实现\n */\ncontract SimpleToken is ERC20, Ownable {\n    \n    uint256 public constant MAX_SUPPLY = 100_000_000 * 10**18;\n    \n    constructor(\n        string memory name,\n        string memory symbol,\n        uint256 initialSupply,\n        address initialOwner\n    ) \n        ERC20(name, symbol) \n        Ownable(initialOwner)\n    {\n        require(initialSupply <= MAX_SUPPLY, \"Initial supply exceeds max supply\");\n        _mint(initialOwner, initialSupply);\n    }\n    \n    function mint(address to, uint256 amount) public onlyOwner {\n        require(totalSupply() + amount <= MAX_SUPPLY, \"Exceeds max supply\");\n        _mint(to, amount);\n    }\n    \n    function burn(uint256 amount) public {\n        _burn(msg.sender, amount);\n    }\n}\n"}}, "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "output": {"sources": {"@openzeppelin/contracts/access/Ownable.sol": {"ast": {"absolutePath": "@openzeppelin/contracts/access/Ownable.sol", "exportedSymbols": {"Context": [933], "Ownable": [147]}, "id": 148, "license": "MIT", "nodeType": "SourceUnit", "nodes": [{"id": 1, "literals": ["solidity", "^", "0.8", ".20"], "nodeType": "PragmaDirective", "src": "102:24:0"}, {"absolutePath": "@openzeppelin/contracts/utils/Context.sol", "file": "../utils/Context.sol", "id": 3, "nameLocation": "-1:-1:-1", "nodeType": "ImportDirective", "scope": 148, "sourceUnit": 934, "src": "128:45:0", "symbolAliases": [{"foreign": {"id": 2, "name": "Context", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 933, "src": "136:7:0", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"abstract": true, "baseContracts": [{"baseName": {"id": 5, "name": "Context", "nameLocations": ["692:7:0"], "nodeType": "IdentifierPath", "referencedDeclaration": 933, "src": "692:7:0"}, "id": 6, "nodeType": "InheritanceSpecifier", "src": "692:7:0"}], "canonicalName": "Ownable", "contractDependencies": [], "contractKind": "contract", "documentation": {"id": 4, "nodeType": "StructuredDocumentation", "src": "175:487:0", "text": " @dev Contract module which provides a basic access control mechanism, where\n there is an account (an owner) that can be granted exclusive access to\n specific functions.\n The initial owner is set to the address provided by the deployer. This can\n later be changed with {transferOwnership}.\n This module is used through inheritance. It will make available the modifier\n `onlyOwner`, which can be applied to your functions to restrict their use to\n the owner."}, "fullyImplemented": true, "id": 147, "linearizedBaseContracts": [147, 933], "name": "Ownable", "nameLocation": "681:7:0", "nodeType": "ContractDefinition", "nodes": [{"constant": false, "id": 8, "mutability": "mutable", "name": "_owner", "nameLocation": "722:6:0", "nodeType": "VariableDeclaration", "scope": 147, "src": "706:22:0", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 7, "name": "address", "nodeType": "ElementaryTypeName", "src": "706:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "private"}, {"documentation": {"id": 9, "nodeType": "StructuredDocumentation", "src": "735:85:0", "text": " @dev The caller account is not authorized to perform an operation."}, "errorSelector": "118cdaa7", "id": 13, "name": "OwnableUnauthorizedAccount", "nameLocation": "831:26:0", "nodeType": "ErrorDefinition", "parameters": {"id": 12, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11, "mutability": "mutable", "name": "account", "nameLocation": "866:7:0", "nodeType": "VariableDeclaration", "scope": 13, "src": "858:15:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 10, "name": "address", "nodeType": "ElementaryTypeName", "src": "858:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "857:17:0"}, "src": "825:50:0"}, {"documentation": {"id": 14, "nodeType": "StructuredDocumentation", "src": "881:82:0", "text": " @dev The owner is not a valid owner account. (eg. `address(0)`)"}, "errorSelector": "1e4fbdf7", "id": 18, "name": "OwnableInvalidOwner", "nameLocation": "974:19:0", "nodeType": "ErrorDefinition", "parameters": {"id": 17, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 16, "mutability": "mutable", "name": "owner", "nameLocation": "1002:5:0", "nodeType": "VariableDeclaration", "scope": 18, "src": "994:13:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 15, "name": "address", "nodeType": "ElementaryTypeName", "src": "994:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "993:15:0"}, "src": "968:41:0"}, {"anonymous": false, "eventSelector": "8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e0", "id": 24, "name": "OwnershipTransferred", "nameLocation": "1021:20:0", "nodeType": "EventDefinition", "parameters": {"id": 23, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 20, "indexed": true, "mutability": "mutable", "name": "previousOwner", "nameLocation": "1058:13:0", "nodeType": "VariableDeclaration", "scope": 24, "src": "1042:29:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 19, "name": "address", "nodeType": "ElementaryTypeName", "src": "1042:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 22, "indexed": true, "mutability": "mutable", "name": "new<PERSON>wner", "nameLocation": "1089:8:0", "nodeType": "VariableDeclaration", "scope": 24, "src": "1073:24:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 21, "name": "address", "nodeType": "ElementaryTypeName", "src": "1073:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "1041:57:0"}, "src": "1015:84:0"}, {"body": {"id": 49, "nodeType": "Block", "src": "1259:153:0", "statements": [{"condition": {"commonType": {"typeIdentifier": "t_address", "typeString": "address"}, "id": 35, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 30, "name": "initialOwner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 27, "src": "1273:12:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"arguments": [{"hexValue": "30", "id": 33, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "1297:1:0", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}], "id": 32, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "1289:7:0", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 31, "name": "address", "nodeType": "ElementaryTypeName", "src": "1289:7:0", "typeDescriptions": {}}}, "id": 34, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1289:10:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "src": "1273:26:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 44, "nodeType": "IfStatement", "src": "1269:95:0", "trueBody": {"id": 43, "nodeType": "Block", "src": "1301:63:0", "statements": [{"errorCall": {"arguments": [{"arguments": [{"hexValue": "30", "id": 39, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "1350:1:0", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}], "id": 38, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "1342:7:0", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 37, "name": "address", "nodeType": "ElementaryTypeName", "src": "1342:7:0", "typeDescriptions": {}}}, "id": 40, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1342:10:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 36, "name": "OwnableInvalidOwner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 18, "src": "1322:19:0", "typeDescriptions": {"typeIdentifier": "t_function_error_pure$_t_address_$returns$__$", "typeString": "function (address) pure"}}, "id": 41, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1322:31:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 42, "nodeType": "RevertStatement", "src": "1315:38:0"}]}}, {"expression": {"arguments": [{"id": 46, "name": "initialOwner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 27, "src": "1392:12:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 45, "name": "_transferOwnership", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 146, "src": "1373:18:0", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_address_$returns$__$", "typeString": "function (address)"}}, "id": 47, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1373:32:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 48, "nodeType": "ExpressionStatement", "src": "1373:32:0"}]}, "documentation": {"id": 25, "nodeType": "StructuredDocumentation", "src": "1105:115:0", "text": " @dev Initializes the contract setting the address provided by the deployer as the initial owner."}, "id": 50, "implemented": true, "kind": "constructor", "modifiers": [], "name": "", "nameLocation": "-1:-1:-1", "nodeType": "FunctionDefinition", "parameters": {"id": 28, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 27, "mutability": "mutable", "name": "initialOwner", "nameLocation": "1245:12:0", "nodeType": "VariableDeclaration", "scope": 50, "src": "1237:20:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 26, "name": "address", "nodeType": "ElementaryTypeName", "src": "1237:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "1236:22:0"}, "returnParameters": {"id": 29, "nodeType": "ParameterList", "parameters": [], "src": "1259:0:0"}, "scope": 147, "src": "1225:187:0", "stateMutability": "nonpayable", "virtual": false, "visibility": "internal"}, {"body": {"id": 57, "nodeType": "Block", "src": "1521:41:0", "statements": [{"expression": {"arguments": [], "expression": {"argumentTypes": [], "id": 53, "name": "_checkOwner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 84, "src": "1531:11:0", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$__$returns$__$", "typeString": "function () view"}}, "id": 54, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1531:13:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 55, "nodeType": "ExpressionStatement", "src": "1531:13:0"}, {"id": 56, "nodeType": "PlaceholderStatement", "src": "1554:1:0"}]}, "documentation": {"id": 51, "nodeType": "StructuredDocumentation", "src": "1418:77:0", "text": " @dev Throws if called by any account other than the owner."}, "id": 58, "name": "only<PERSON><PERSON>er", "nameLocation": "1509:9:0", "nodeType": "ModifierDefinition", "parameters": {"id": 52, "nodeType": "ParameterList", "parameters": [], "src": "1518:2:0"}, "src": "1500:62:0", "virtual": false, "visibility": "internal"}, {"body": {"id": 66, "nodeType": "Block", "src": "1693:30:0", "statements": [{"expression": {"id": 64, "name": "_owner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 8, "src": "1710:6:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "functionReturnParameters": 63, "id": 65, "nodeType": "Return", "src": "1703:13:0"}]}, "documentation": {"id": 59, "nodeType": "StructuredDocumentation", "src": "1568:65:0", "text": " @dev Returns the address of the current owner."}, "functionSelector": "8da5cb5b", "id": 67, "implemented": true, "kind": "function", "modifiers": [], "name": "owner", "nameLocation": "1647:5:0", "nodeType": "FunctionDefinition", "parameters": {"id": 60, "nodeType": "ParameterList", "parameters": [], "src": "1652:2:0"}, "returnParameters": {"id": 63, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 62, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 67, "src": "1684:7:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 61, "name": "address", "nodeType": "ElementaryTypeName", "src": "1684:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "1683:9:0"}, "scope": 147, "src": "1638:85:0", "stateMutability": "view", "virtual": true, "visibility": "public"}, {"body": {"id": 83, "nodeType": "Block", "src": "1841:117:0", "statements": [{"condition": {"commonType": {"typeIdentifier": "t_address", "typeString": "address"}, "id": 75, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"arguments": [], "expression": {"argumentTypes": [], "id": 71, "name": "owner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 67, "src": "1855:5:0", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$__$returns$_t_address_$", "typeString": "function () view returns (address)"}}, "id": 72, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1855:7:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "BinaryOperation", "operator": "!=", "rightExpression": {"arguments": [], "expression": {"argumentTypes": [], "id": 73, "name": "_msgSender", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 915, "src": "1866:10:0", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$__$returns$_t_address_$", "typeString": "function () view returns (address)"}}, "id": 74, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1866:12:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "src": "1855:23:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 82, "nodeType": "IfStatement", "src": "1851:101:0", "trueBody": {"id": 81, "nodeType": "Block", "src": "1880:72:0", "statements": [{"errorCall": {"arguments": [{"arguments": [], "expression": {"argumentTypes": [], "id": 77, "name": "_msgSender", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 915, "src": "1928:10:0", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$__$returns$_t_address_$", "typeString": "function () view returns (address)"}}, "id": 78, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1928:12:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 76, "name": "OwnableUnauthorizedAccount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 13, "src": "1901:26:0", "typeDescriptions": {"typeIdentifier": "t_function_error_pure$_t_address_$returns$__$", "typeString": "function (address) pure"}}, "id": 79, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1901:40:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 80, "nodeType": "RevertStatement", "src": "1894:47:0"}]}}]}, "documentation": {"id": 68, "nodeType": "StructuredDocumentation", "src": "1729:62:0", "text": " @dev Throws if the sender is not the owner."}, "id": 84, "implemented": true, "kind": "function", "modifiers": [], "name": "_checkOwner", "nameLocation": "1805:11:0", "nodeType": "FunctionDefinition", "parameters": {"id": 69, "nodeType": "ParameterList", "parameters": [], "src": "1816:2:0"}, "returnParameters": {"id": 70, "nodeType": "ParameterList", "parameters": [], "src": "1841:0:0"}, "scope": 147, "src": "1796:162:0", "stateMutability": "view", "virtual": true, "visibility": "internal"}, {"body": {"id": 97, "nodeType": "Block", "src": "2347:47:0", "statements": [{"expression": {"arguments": [{"arguments": [{"hexValue": "30", "id": 93, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "2384:1:0", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}], "id": 92, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "2376:7:0", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 91, "name": "address", "nodeType": "ElementaryTypeName", "src": "2376:7:0", "typeDescriptions": {}}}, "id": 94, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2376:10:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 90, "name": "_transferOwnership", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 146, "src": "2357:18:0", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_address_$returns$__$", "typeString": "function (address)"}}, "id": 95, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2357:30:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 96, "nodeType": "ExpressionStatement", "src": "2357:30:0"}]}, "documentation": {"id": 85, "nodeType": "StructuredDocumentation", "src": "1964:324:0", "text": " @dev Leaves the contract without owner. It will not be possible to call\n `onlyOwner` functions. Can only be called by the current owner.\n NOTE: Renouncing ownership will leave the contract without an owner,\n thereby disabling any functionality that is only available to the owner."}, "functionSelector": "715018a6", "id": 98, "implemented": true, "kind": "function", "modifiers": [{"id": 88, "kind": "modifierInvocation", "modifierName": {"id": 87, "name": "only<PERSON><PERSON>er", "nameLocations": ["2337:9:0"], "nodeType": "IdentifierPath", "referencedDeclaration": 58, "src": "2337:9:0"}, "nodeType": "ModifierInvocation", "src": "2337:9:0"}], "name": "renounceOwnership", "nameLocation": "2302:17:0", "nodeType": "FunctionDefinition", "parameters": {"id": 86, "nodeType": "ParameterList", "parameters": [], "src": "2319:2:0"}, "returnParameters": {"id": 89, "nodeType": "ParameterList", "parameters": [], "src": "2347:0:0"}, "scope": 147, "src": "2293:101:0", "stateMutability": "nonpayable", "virtual": true, "visibility": "public"}, {"body": {"id": 125, "nodeType": "Block", "src": "2613:145:0", "statements": [{"condition": {"commonType": {"typeIdentifier": "t_address", "typeString": "address"}, "id": 111, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 106, "name": "new<PERSON>wner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 101, "src": "2627:8:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"arguments": [{"hexValue": "30", "id": 109, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "2647:1:0", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}], "id": 108, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "2639:7:0", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 107, "name": "address", "nodeType": "ElementaryTypeName", "src": "2639:7:0", "typeDescriptions": {}}}, "id": 110, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2639:10:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "src": "2627:22:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 120, "nodeType": "IfStatement", "src": "2623:91:0", "trueBody": {"id": 119, "nodeType": "Block", "src": "2651:63:0", "statements": [{"errorCall": {"arguments": [{"arguments": [{"hexValue": "30", "id": 115, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "2700:1:0", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}], "id": 114, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "2692:7:0", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 113, "name": "address", "nodeType": "ElementaryTypeName", "src": "2692:7:0", "typeDescriptions": {}}}, "id": 116, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2692:10:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 112, "name": "OwnableInvalidOwner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 18, "src": "2672:19:0", "typeDescriptions": {"typeIdentifier": "t_function_error_pure$_t_address_$returns$__$", "typeString": "function (address) pure"}}, "id": 117, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2672:31:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 118, "nodeType": "RevertStatement", "src": "2665:38:0"}]}}, {"expression": {"arguments": [{"id": 122, "name": "new<PERSON>wner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 101, "src": "2742:8:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 121, "name": "_transferOwnership", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 146, "src": "2723:18:0", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_address_$returns$__$", "typeString": "function (address)"}}, "id": 123, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2723:28:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 124, "nodeType": "ExpressionStatement", "src": "2723:28:0"}]}, "documentation": {"id": 99, "nodeType": "StructuredDocumentation", "src": "2400:138:0", "text": " @dev Transfers ownership of the contract to a new account (`newOwner`).\n Can only be called by the current owner."}, "functionSelector": "f2fde38b", "id": 126, "implemented": true, "kind": "function", "modifiers": [{"id": 104, "kind": "modifierInvocation", "modifierName": {"id": 103, "name": "only<PERSON><PERSON>er", "nameLocations": ["2603:9:0"], "nodeType": "IdentifierPath", "referencedDeclaration": 58, "src": "2603:9:0"}, "nodeType": "ModifierInvocation", "src": "2603:9:0"}], "name": "transferOwnership", "nameLocation": "2552:17:0", "nodeType": "FunctionDefinition", "parameters": {"id": 102, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 101, "mutability": "mutable", "name": "new<PERSON>wner", "nameLocation": "2578:8:0", "nodeType": "VariableDeclaration", "scope": 126, "src": "2570:16:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 100, "name": "address", "nodeType": "ElementaryTypeName", "src": "2570:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "2569:18:0"}, "returnParameters": {"id": 105, "nodeType": "ParameterList", "parameters": [], "src": "2613:0:0"}, "scope": 147, "src": "2543:215:0", "stateMutability": "nonpayable", "virtual": true, "visibility": "public"}, {"body": {"id": 145, "nodeType": "Block", "src": "2975:124:0", "statements": [{"assignments": [133], "declarations": [{"constant": false, "id": 133, "mutability": "mutable", "name": "old<PERSON>wner", "nameLocation": "2993:8:0", "nodeType": "VariableDeclaration", "scope": 145, "src": "2985:16:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 132, "name": "address", "nodeType": "ElementaryTypeName", "src": "2985:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "id": 135, "initialValue": {"id": 134, "name": "_owner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 8, "src": "3004:6:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "VariableDeclarationStatement", "src": "2985:25:0"}, {"expression": {"id": 138, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 136, "name": "_owner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 8, "src": "3020:6:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 137, "name": "new<PERSON>wner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 129, "src": "3029:8:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "src": "3020:17:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "id": 139, "nodeType": "ExpressionStatement", "src": "3020:17:0"}, {"eventCall": {"arguments": [{"id": 141, "name": "old<PERSON>wner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 133, "src": "3073:8:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 142, "name": "new<PERSON>wner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 129, "src": "3083:8:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_address", "typeString": "address"}], "id": 140, "name": "OwnershipTransferred", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 24, "src": "3052:20:0", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_address_$_t_address_$returns$__$", "typeString": "function (address,address)"}}, "id": 143, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3052:40:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 144, "nodeType": "EmitStatement", "src": "3047:45:0"}]}, "documentation": {"id": 127, "nodeType": "StructuredDocumentation", "src": "2764:143:0", "text": " @dev Transfers ownership of the contract to a new account (`newOwner`).\n Internal function without access restriction."}, "id": 146, "implemented": true, "kind": "function", "modifiers": [], "name": "_transferOwnership", "nameLocation": "2921:18:0", "nodeType": "FunctionDefinition", "parameters": {"id": 130, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 129, "mutability": "mutable", "name": "new<PERSON>wner", "nameLocation": "2948:8:0", "nodeType": "VariableDeclaration", "scope": 146, "src": "2940:16:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 128, "name": "address", "nodeType": "ElementaryTypeName", "src": "2940:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "2939:18:0"}, "returnParameters": {"id": 131, "nodeType": "ParameterList", "parameters": [], "src": "2975:0:0"}, "scope": 147, "src": "2912:187:0", "stateMutability": "nonpayable", "virtual": true, "visibility": "internal"}], "scope": 148, "src": "663:2438:0", "usedErrors": [13, 18], "usedEvents": [24]}], "src": "102:3000:0"}, "id": 0}, "@openzeppelin/contracts/interfaces/draft-IERC6093.sol": {"ast": {"absolutePath": "@openzeppelin/contracts/interfaces/draft-IERC6093.sol", "exportedSymbols": {"IERC1155Errors": [284], "IERC20Errors": [189], "IERC721Errors": [237]}, "id": 285, "license": "MIT", "nodeType": "SourceUnit", "nodes": [{"id": 149, "literals": ["solidity", ">=", "0.8", ".4"], "nodeType": "PragmaDirective", "src": "112:24:1"}, {"abstract": false, "baseContracts": [], "canonicalName": "IERC20Errors", "contractDependencies": [], "contractKind": "interface", "documentation": {"id": 150, "nodeType": "StructuredDocumentation", "src": "138:141:1", "text": " @dev Standard ERC-20 Errors\n Interface of the https://eips.ethereum.org/EIPS/eip-6093[ERC-6093] custom errors for ERC-20 tokens."}, "fullyImplemented": true, "id": 189, "linearizedBaseContracts": [189], "name": "IERC20Errors", "nameLocation": "290:12:1", "nodeType": "ContractDefinition", "nodes": [{"documentation": {"id": 151, "nodeType": "StructuredDocumentation", "src": "309:309:1", "text": " @dev Indicates an error related to the current `balance` of a `sender`. Used in transfers.\n @param sender Address whose tokens are being transferred.\n @param balance Current balance for the interacting account.\n @param needed Minimum amount required to perform a transfer."}, "errorSelector": "e450d38c", "id": 159, "name": "ERC20InsufficientBalance", "nameLocation": "629:24:1", "nodeType": "ErrorDefinition", "parameters": {"id": 158, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 153, "mutability": "mutable", "name": "sender", "nameLocation": "662:6:1", "nodeType": "VariableDeclaration", "scope": 159, "src": "654:14:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 152, "name": "address", "nodeType": "ElementaryTypeName", "src": "654:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 155, "mutability": "mutable", "name": "balance", "nameLocation": "678:7:1", "nodeType": "VariableDeclaration", "scope": 159, "src": "670:15:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 154, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "670:7:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 157, "mutability": "mutable", "name": "needed", "nameLocation": "695:6:1", "nodeType": "VariableDeclaration", "scope": 159, "src": "687:14:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 156, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "687:7:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "653:49:1"}, "src": "623:80:1"}, {"documentation": {"id": 160, "nodeType": "StructuredDocumentation", "src": "709:152:1", "text": " @dev Indicates a failure with the token `sender`. Used in transfers.\n @param sender Address whose tokens are being transferred."}, "errorSelector": "96c6fd1e", "id": 164, "name": "ERC20InvalidSender", "nameLocation": "872:18:1", "nodeType": "ErrorDefinition", "parameters": {"id": 163, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 162, "mutability": "mutable", "name": "sender", "nameLocation": "899:6:1", "nodeType": "VariableDeclaration", "scope": 164, "src": "891:14:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 161, "name": "address", "nodeType": "ElementaryTypeName", "src": "891:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "890:16:1"}, "src": "866:41:1"}, {"documentation": {"id": 165, "nodeType": "StructuredDocumentation", "src": "913:159:1", "text": " @dev Indicates a failure with the token `receiver`. Used in transfers.\n @param receiver Address to which tokens are being transferred."}, "errorSelector": "ec442f05", "id": 169, "name": "ERC20InvalidReceiver", "nameLocation": "1083:20:1", "nodeType": "ErrorDefinition", "parameters": {"id": 168, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 167, "mutability": "mutable", "name": "receiver", "nameLocation": "1112:8:1", "nodeType": "VariableDeclaration", "scope": 169, "src": "1104:16:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 166, "name": "address", "nodeType": "ElementaryTypeName", "src": "1104:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "1103:18:1"}, "src": "1077:45:1"}, {"documentation": {"id": 170, "nodeType": "StructuredDocumentation", "src": "1128:345:1", "text": " @dev Indicates a failure with the `spender`’s `allowance`. Used in transfers.\n @param spender Address that may be allowed to operate on tokens without being their owner.\n @param allowance Amount of tokens a `spender` is allowed to operate with.\n @param needed Minimum amount required to perform a transfer."}, "errorSelector": "fb8f41b2", "id": 178, "name": "ERC20InsufficientAllowance", "nameLocation": "1484:26:1", "nodeType": "ErrorDefinition", "parameters": {"id": 177, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 172, "mutability": "mutable", "name": "spender", "nameLocation": "1519:7:1", "nodeType": "VariableDeclaration", "scope": 178, "src": "1511:15:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 171, "name": "address", "nodeType": "ElementaryTypeName", "src": "1511:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 174, "mutability": "mutable", "name": "allowance", "nameLocation": "1536:9:1", "nodeType": "VariableDeclaration", "scope": 178, "src": "1528:17:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 173, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1528:7:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 176, "mutability": "mutable", "name": "needed", "nameLocation": "1555:6:1", "nodeType": "VariableDeclaration", "scope": 178, "src": "1547:14:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 175, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1547:7:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "1510:52:1"}, "src": "1478:85:1"}, {"documentation": {"id": 179, "nodeType": "StructuredDocumentation", "src": "1569:174:1", "text": " @dev Indicates a failure with the `approver` of a token to be approved. Used in approvals.\n @param approver Address initiating an approval operation."}, "errorSelector": "e602df05", "id": 183, "name": "ERC20InvalidApprover", "nameLocation": "1754:20:1", "nodeType": "ErrorDefinition", "parameters": {"id": 182, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 181, "mutability": "mutable", "name": "approver", "nameLocation": "1783:8:1", "nodeType": "VariableDeclaration", "scope": 183, "src": "1775:16:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 180, "name": "address", "nodeType": "ElementaryTypeName", "src": "1775:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "1774:18:1"}, "src": "1748:45:1"}, {"documentation": {"id": 184, "nodeType": "StructuredDocumentation", "src": "1799:195:1", "text": " @dev Indicates a failure with the `spender` to be approved. Used in approvals.\n @param spender Address that may be allowed to operate on tokens without being their owner."}, "errorSelector": "94280d62", "id": 188, "name": "ERC20InvalidSpender", "nameLocation": "2005:19:1", "nodeType": "ErrorDefinition", "parameters": {"id": 187, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 186, "mutability": "mutable", "name": "spender", "nameLocation": "2033:7:1", "nodeType": "VariableDeclaration", "scope": 188, "src": "2025:15:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 185, "name": "address", "nodeType": "ElementaryTypeName", "src": "2025:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "2024:17:1"}, "src": "1999:43:1"}], "scope": 285, "src": "280:1764:1", "usedErrors": [159, 164, 169, 178, 183, 188], "usedEvents": []}, {"abstract": false, "baseContracts": [], "canonicalName": "IERC721Errors", "contractDependencies": [], "contractKind": "interface", "documentation": {"id": 190, "nodeType": "StructuredDocumentation", "src": "2046:143:1", "text": " @dev Standard ERC-721 Errors\n Interface of the https://eips.ethereum.org/EIPS/eip-6093[ERC-6093] custom errors for ERC-721 tokens."}, "fullyImplemented": true, "id": 237, "linearizedBaseContracts": [237], "name": "IERC721Errors", "nameLocation": "2200:13:1", "nodeType": "ContractDefinition", "nodes": [{"documentation": {"id": 191, "nodeType": "StructuredDocumentation", "src": "2220:219:1", "text": " @dev Indicates that an address can't be an owner. For example, `address(0)` is a forbidden owner in ERC-20.\n Used in balance queries.\n @param owner Address of the current owner of a token."}, "errorSelector": "89c62b64", "id": 195, "name": "ERC721InvalidOwner", "nameLocation": "2450:18:1", "nodeType": "ErrorDefinition", "parameters": {"id": 194, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 193, "mutability": "mutable", "name": "owner", "nameLocation": "2477:5:1", "nodeType": "VariableDeclaration", "scope": 195, "src": "2469:13:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 192, "name": "address", "nodeType": "ElementaryTypeName", "src": "2469:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "2468:15:1"}, "src": "2444:40:1"}, {"documentation": {"id": 196, "nodeType": "StructuredDocumentation", "src": "2490:132:1", "text": " @dev Indicates a `tokenId` whose `owner` is the zero address.\n @param tokenId Identifier number of a token."}, "errorSelector": "7e273289", "id": 200, "name": "ERC721NonexistentToken", "nameLocation": "2633:22:1", "nodeType": "ErrorDefinition", "parameters": {"id": 199, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 198, "mutability": "mutable", "name": "tokenId", "nameLocation": "2664:7:1", "nodeType": "VariableDeclaration", "scope": 200, "src": "2656:15:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 197, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2656:7:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "2655:17:1"}, "src": "2627:46:1"}, {"documentation": {"id": 201, "nodeType": "StructuredDocumentation", "src": "2679:289:1", "text": " @dev Indicates an error related to the ownership over a particular token. Used in transfers.\n @param sender Address whose tokens are being transferred.\n @param tokenId Identifier number of a token.\n @param owner Address of the current owner of a token."}, "errorSelector": "64283d7b", "id": 209, "name": "ERC721IncorrectOwner", "nameLocation": "2979:20:1", "nodeType": "ErrorDefinition", "parameters": {"id": 208, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 203, "mutability": "mutable", "name": "sender", "nameLocation": "3008:6:1", "nodeType": "VariableDeclaration", "scope": 209, "src": "3000:14:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 202, "name": "address", "nodeType": "ElementaryTypeName", "src": "3000:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 205, "mutability": "mutable", "name": "tokenId", "nameLocation": "3024:7:1", "nodeType": "VariableDeclaration", "scope": 209, "src": "3016:15:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 204, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "3016:7:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 207, "mutability": "mutable", "name": "owner", "nameLocation": "3041:5:1", "nodeType": "VariableDeclaration", "scope": 209, "src": "3033:13:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 206, "name": "address", "nodeType": "ElementaryTypeName", "src": "3033:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "2999:48:1"}, "src": "2973:75:1"}, {"documentation": {"id": 210, "nodeType": "StructuredDocumentation", "src": "3054:152:1", "text": " @dev Indicates a failure with the token `sender`. Used in transfers.\n @param sender Address whose tokens are being transferred."}, "errorSelector": "73c6ac6e", "id": 214, "name": "ERC721InvalidSender", "nameLocation": "3217:19:1", "nodeType": "ErrorDefinition", "parameters": {"id": 213, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 212, "mutability": "mutable", "name": "sender", "nameLocation": "3245:6:1", "nodeType": "VariableDeclaration", "scope": 214, "src": "3237:14:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 211, "name": "address", "nodeType": "ElementaryTypeName", "src": "3237:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "3236:16:1"}, "src": "3211:42:1"}, {"documentation": {"id": 215, "nodeType": "StructuredDocumentation", "src": "3259:159:1", "text": " @dev Indicates a failure with the token `receiver`. Used in transfers.\n @param receiver Address to which tokens are being transferred."}, "errorSelector": "64a0ae92", "id": 219, "name": "ERC721InvalidReceiver", "nameLocation": "3429:21:1", "nodeType": "ErrorDefinition", "parameters": {"id": 218, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 217, "mutability": "mutable", "name": "receiver", "nameLocation": "3459:8:1", "nodeType": "VariableDeclaration", "scope": 219, "src": "3451:16:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 216, "name": "address", "nodeType": "ElementaryTypeName", "src": "3451:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "3450:18:1"}, "src": "3423:46:1"}, {"documentation": {"id": 220, "nodeType": "StructuredDocumentation", "src": "3475:247:1", "text": " @dev Indicates a failure with the `operator`’s approval. Used in transfers.\n @param operator Address that may be allowed to operate on tokens without being their owner.\n @param tokenId Identifier number of a token."}, "errorSelector": "177e802f", "id": 226, "name": "ERC721InsufficientApproval", "nameLocation": "3733:26:1", "nodeType": "ErrorDefinition", "parameters": {"id": 225, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 222, "mutability": "mutable", "name": "operator", "nameLocation": "3768:8:1", "nodeType": "VariableDeclaration", "scope": 226, "src": "3760:16:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 221, "name": "address", "nodeType": "ElementaryTypeName", "src": "3760:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 224, "mutability": "mutable", "name": "tokenId", "nameLocation": "3786:7:1", "nodeType": "VariableDeclaration", "scope": 226, "src": "3778:15:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 223, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "3778:7:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "3759:35:1"}, "src": "3727:68:1"}, {"documentation": {"id": 227, "nodeType": "StructuredDocumentation", "src": "3801:174:1", "text": " @dev Indicates a failure with the `approver` of a token to be approved. Used in approvals.\n @param approver Address initiating an approval operation."}, "errorSelector": "a9fbf51f", "id": 231, "name": "ERC721InvalidApprover", "nameLocation": "3986:21:1", "nodeType": "ErrorDefinition", "parameters": {"id": 230, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 229, "mutability": "mutable", "name": "approver", "nameLocation": "4016:8:1", "nodeType": "VariableDeclaration", "scope": 231, "src": "4008:16:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 228, "name": "address", "nodeType": "ElementaryTypeName", "src": "4008:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "4007:18:1"}, "src": "3980:46:1"}, {"documentation": {"id": 232, "nodeType": "StructuredDocumentation", "src": "4032:197:1", "text": " @dev Indicates a failure with the `operator` to be approved. Used in approvals.\n @param operator Address that may be allowed to operate on tokens without being their owner."}, "errorSelector": "5b08ba18", "id": 236, "name": "ERC721InvalidOperator", "nameLocation": "4240:21:1", "nodeType": "ErrorDefinition", "parameters": {"id": 235, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 234, "mutability": "mutable", "name": "operator", "nameLocation": "4270:8:1", "nodeType": "VariableDeclaration", "scope": 236, "src": "4262:16:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 233, "name": "address", "nodeType": "ElementaryTypeName", "src": "4262:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "4261:18:1"}, "src": "4234:46:1"}], "scope": 285, "src": "2190:2092:1", "usedErrors": [195, 200, 209, 214, 219, 226, 231, 236], "usedEvents": []}, {"abstract": false, "baseContracts": [], "canonicalName": "IERC1155Errors", "contractDependencies": [], "contractKind": "interface", "documentation": {"id": 238, "nodeType": "StructuredDocumentation", "src": "4284:145:1", "text": " @dev Standard ERC-1155 Errors\n Interface of the https://eips.ethereum.org/EIPS/eip-6093[ERC-6093] custom errors for ERC-1155 tokens."}, "fullyImplemented": true, "id": 284, "linearizedBaseContracts": [284], "name": "IERC1155Errors", "nameLocation": "4440:14:1", "nodeType": "ContractDefinition", "nodes": [{"documentation": {"id": 239, "nodeType": "StructuredDocumentation", "src": "4461:361:1", "text": " @dev Indicates an error related to the current `balance` of a `sender`. Used in transfers.\n @param sender Address whose tokens are being transferred.\n @param balance Current balance for the interacting account.\n @param needed Minimum amount required to perform a transfer.\n @param tokenId Identifier number of a token."}, "errorSelector": "03dee4c5", "id": 249, "name": "ERC1155InsufficientBalance", "nameLocation": "4833:26:1", "nodeType": "ErrorDefinition", "parameters": {"id": 248, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 241, "mutability": "mutable", "name": "sender", "nameLocation": "4868:6:1", "nodeType": "VariableDeclaration", "scope": 249, "src": "4860:14:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 240, "name": "address", "nodeType": "ElementaryTypeName", "src": "4860:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 243, "mutability": "mutable", "name": "balance", "nameLocation": "4884:7:1", "nodeType": "VariableDeclaration", "scope": 249, "src": "4876:15:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 242, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "4876:7:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 245, "mutability": "mutable", "name": "needed", "nameLocation": "4901:6:1", "nodeType": "VariableDeclaration", "scope": 249, "src": "4893:14:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 244, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "4893:7:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 247, "mutability": "mutable", "name": "tokenId", "nameLocation": "4917:7:1", "nodeType": "VariableDeclaration", "scope": 249, "src": "4909:15:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 246, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "4909:7:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "4859:66:1"}, "src": "4827:99:1"}, {"documentation": {"id": 250, "nodeType": "StructuredDocumentation", "src": "4932:152:1", "text": " @dev Indicates a failure with the token `sender`. Used in transfers.\n @param sender Address whose tokens are being transferred."}, "errorSelector": "01a83514", "id": 254, "name": "ERC1155InvalidSender", "nameLocation": "5095:20:1", "nodeType": "ErrorDefinition", "parameters": {"id": 253, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 252, "mutability": "mutable", "name": "sender", "nameLocation": "5124:6:1", "nodeType": "VariableDeclaration", "scope": 254, "src": "5116:14:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 251, "name": "address", "nodeType": "ElementaryTypeName", "src": "5116:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "5115:16:1"}, "src": "5089:43:1"}, {"documentation": {"id": 255, "nodeType": "StructuredDocumentation", "src": "5138:159:1", "text": " @dev Indicates a failure with the token `receiver`. Used in transfers.\n @param receiver Address to which tokens are being transferred."}, "errorSelector": "57f447ce", "id": 259, "name": "ERC1155InvalidReceiver", "nameLocation": "5308:22:1", "nodeType": "ErrorDefinition", "parameters": {"id": 258, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 257, "mutability": "mutable", "name": "receiver", "nameLocation": "5339:8:1", "nodeType": "VariableDeclaration", "scope": 259, "src": "5331:16:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 256, "name": "address", "nodeType": "ElementaryTypeName", "src": "5331:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "5330:18:1"}, "src": "5302:47:1"}, {"documentation": {"id": 260, "nodeType": "StructuredDocumentation", "src": "5355:256:1", "text": " @dev Indicates a failure with the `operator`’s approval. Used in transfers.\n @param operator Address that may be allowed to operate on tokens without being their owner.\n @param owner Address of the current owner of a token."}, "errorSelector": "e237d922", "id": 266, "name": "ERC1155MissingApprovalForAll", "nameLocation": "5622:28:1", "nodeType": "ErrorDefinition", "parameters": {"id": 265, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 262, "mutability": "mutable", "name": "operator", "nameLocation": "5659:8:1", "nodeType": "VariableDeclaration", "scope": 266, "src": "5651:16:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 261, "name": "address", "nodeType": "ElementaryTypeName", "src": "5651:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 264, "mutability": "mutable", "name": "owner", "nameLocation": "5677:5:1", "nodeType": "VariableDeclaration", "scope": 266, "src": "5669:13:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 263, "name": "address", "nodeType": "ElementaryTypeName", "src": "5669:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "5650:33:1"}, "src": "5616:68:1"}, {"documentation": {"id": 267, "nodeType": "StructuredDocumentation", "src": "5690:174:1", "text": " @dev Indicates a failure with the `approver` of a token to be approved. Used in approvals.\n @param approver Address initiating an approval operation."}, "errorSelector": "3e31884e", "id": 271, "name": "ERC1155InvalidApprover", "nameLocation": "5875:22:1", "nodeType": "ErrorDefinition", "parameters": {"id": 270, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 269, "mutability": "mutable", "name": "approver", "nameLocation": "5906:8:1", "nodeType": "VariableDeclaration", "scope": 271, "src": "5898:16:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 268, "name": "address", "nodeType": "ElementaryTypeName", "src": "5898:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "5897:18:1"}, "src": "5869:47:1"}, {"documentation": {"id": 272, "nodeType": "StructuredDocumentation", "src": "5922:197:1", "text": " @dev Indicates a failure with the `operator` to be approved. Used in approvals.\n @param operator Address that may be allowed to operate on tokens without being their owner."}, "errorSelector": "ced3e100", "id": 276, "name": "ERC1155InvalidOperator", "nameLocation": "6130:22:1", "nodeType": "ErrorDefinition", "parameters": {"id": 275, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 274, "mutability": "mutable", "name": "operator", "nameLocation": "6161:8:1", "nodeType": "VariableDeclaration", "scope": 276, "src": "6153:16:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 273, "name": "address", "nodeType": "ElementaryTypeName", "src": "6153:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "6152:18:1"}, "src": "6124:47:1"}, {"documentation": {"id": 277, "nodeType": "StructuredDocumentation", "src": "6177:280:1", "text": " @dev Indicates an array length mismatch between ids and values in a safeBatchTransferFrom operation.\n Used in batch transfers.\n @param idsLength Length of the array of token identifiers\n @param valuesLength Length of the array of token amounts"}, "errorSelector": "5b059991", "id": 283, "name": "ERC1155InvalidArrayLength", "nameLocation": "6468:25:1", "nodeType": "ErrorDefinition", "parameters": {"id": 282, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 279, "mutability": "mutable", "name": "idsLength", "nameLocation": "6502:9:1", "nodeType": "VariableDeclaration", "scope": 283, "src": "6494:17:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 278, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "6494:7:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 281, "mutability": "mutable", "name": "valuesLength", "nameLocation": "6521:12:1", "nodeType": "VariableDeclaration", "scope": 283, "src": "6513:20:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 280, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "6513:7:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "6493:41:1"}, "src": "6462:73:1"}], "scope": 285, "src": "4430:2107:1", "usedErrors": [249, 254, 259, 266, 271, 276, 283], "usedEvents": []}], "src": "112:6426:1"}, "id": 1}, "@openzeppelin/contracts/token/ERC20/ERC20.sol": {"ast": {"absolutePath": "@openzeppelin/contracts/token/ERC20/ERC20.sol", "exportedSymbols": {"Context": [933], "ERC20": [799], "IERC20": [877], "IERC20Errors": [189], "IERC20Metadata": [903]}, "id": 800, "license": "MIT", "nodeType": "SourceUnit", "nodes": [{"id": 286, "literals": ["solidity", "^", "0.8", ".20"], "nodeType": "PragmaDirective", "src": "105:24:2"}, {"absolutePath": "@openzeppelin/contracts/token/ERC20/IERC20.sol", "file": "./IERC20.sol", "id": 288, "nameLocation": "-1:-1:-1", "nodeType": "ImportDirective", "scope": 800, "sourceUnit": 878, "src": "131:36:2", "symbolAliases": [{"foreign": {"id": 287, "name": "IERC20", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 877, "src": "139:6:2", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"absolutePath": "@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol", "file": "./extensions/IERC20Metadata.sol", "id": 290, "nameLocation": "-1:-1:-1", "nodeType": "ImportDirective", "scope": 800, "sourceUnit": 904, "src": "168:63:2", "symbolAliases": [{"foreign": {"id": 289, "name": "IERC20Metadata", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 903, "src": "176:14:2", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"absolutePath": "@openzeppelin/contracts/utils/Context.sol", "file": "../../utils/Context.sol", "id": 292, "nameLocation": "-1:-1:-1", "nodeType": "ImportDirective", "scope": 800, "sourceUnit": 934, "src": "232:48:2", "symbolAliases": [{"foreign": {"id": 291, "name": "Context", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 933, "src": "240:7:2", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"absolutePath": "@openzeppelin/contracts/interfaces/draft-IERC6093.sol", "file": "../../interfaces/draft-IERC6093.sol", "id": 294, "nameLocation": "-1:-1:-1", "nodeType": "ImportDirective", "scope": 800, "sourceUnit": 285, "src": "281:65:2", "symbolAliases": [{"foreign": {"id": 293, "name": "IERC20Errors", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 189, "src": "289:12:2", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"abstract": true, "baseContracts": [{"baseName": {"id": 296, "name": "Context", "nameLocations": ["1133:7:2"], "nodeType": "IdentifierPath", "referencedDeclaration": 933, "src": "1133:7:2"}, "id": 297, "nodeType": "InheritanceSpecifier", "src": "1133:7:2"}, {"baseName": {"id": 298, "name": "IERC20", "nameLocations": ["1142:6:2"], "nodeType": "IdentifierPath", "referencedDeclaration": 877, "src": "1142:6:2"}, "id": 299, "nodeType": "InheritanceSpecifier", "src": "1142:6:2"}, {"baseName": {"id": 300, "name": "IERC20Metadata", "nameLocations": ["1150:14:2"], "nodeType": "IdentifierPath", "referencedDeclaration": 903, "src": "1150:14:2"}, "id": 301, "nodeType": "InheritanceSpecifier", "src": "1150:14:2"}, {"baseName": {"id": 302, "name": "IERC20Errors", "nameLocations": ["1166:12:2"], "nodeType": "IdentifierPath", "referencedDeclaration": 189, "src": "1166:12:2"}, "id": 303, "nodeType": "InheritanceSpecifier", "src": "1166:12:2"}], "canonicalName": "ERC20", "contractDependencies": [], "contractKind": "contract", "documentation": {"id": 295, "nodeType": "StructuredDocumentation", "src": "348:757:2", "text": " @dev Implementation of the {IERC20} interface.\n This implementation is agnostic to the way tokens are created. This means\n that a supply mechanism has to be added in a derived contract using {_mint}.\n TIP: For a detailed writeup see our guide\n https://forum.openzeppelin.com/t/how-to-implement-erc20-supply-mechanisms/226[How\n to implement supply mechanisms].\n The default value of {decimals} is 18. To change this, you should override\n this function so it returns a different value.\n We have followed general OpenZeppelin Contracts guidelines: functions revert\n instead returning `false` on failure. This behavior is nonetheless\n conventional and does not conflict with the expectations of ERC-20\n applications."}, "fullyImplemented": true, "id": 799, "linearizedBaseContracts": [799, 189, 903, 877, 933], "name": "ERC20", "nameLocation": "1124:5:2", "nodeType": "ContractDefinition", "nodes": [{"constant": false, "id": 307, "mutability": "mutable", "name": "_balances", "nameLocation": "1229:9:2", "nodeType": "VariableDeclaration", "scope": 799, "src": "1185:53:2", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}, "typeName": {"id": 306, "keyName": "account", "keyNameLocation": "1201:7:2", "keyType": {"id": 304, "name": "address", "nodeType": "ElementaryTypeName", "src": "1193:7:2", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "Mapping", "src": "1185:35:2", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}, "valueName": "", "valueNameLocation": "-1:-1:-1", "valueType": {"id": 305, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1212:7:2", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}}, "visibility": "private"}, {"constant": false, "id": 313, "mutability": "mutable", "name": "_allowances", "nameLocation": "1317:11:2", "nodeType": "VariableDeclaration", "scope": 799, "src": "1245:83:2", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_mapping$_t_address_$_t_uint256_$_$", "typeString": "mapping(address => mapping(address => uint256))"}, "typeName": {"id": 312, "keyName": "account", "keyNameLocation": "1261:7:2", "keyType": {"id": 308, "name": "address", "nodeType": "ElementaryTypeName", "src": "1253:7:2", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "Mapping", "src": "1245:63:2", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_mapping$_t_address_$_t_uint256_$_$", "typeString": "mapping(address => mapping(address => uint256))"}, "valueName": "", "valueNameLocation": "-1:-1:-1", "valueType": {"id": 311, "keyName": "spender", "keyNameLocation": "1288:7:2", "keyType": {"id": 309, "name": "address", "nodeType": "ElementaryTypeName", "src": "1280:7:2", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "Mapping", "src": "1272:35:2", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}, "valueName": "", "valueNameLocation": "-1:-1:-1", "valueType": {"id": 310, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1299:7:2", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}}}, "visibility": "private"}, {"constant": false, "id": 315, "mutability": "mutable", "name": "_totalSupply", "nameLocation": "1351:12:2", "nodeType": "VariableDeclaration", "scope": 799, "src": "1335:28:2", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 314, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1335:7:2", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "private"}, {"constant": false, "id": 317, "mutability": "mutable", "name": "_name", "nameLocation": "1385:5:2", "nodeType": "VariableDeclaration", "scope": 799, "src": "1370:20:2", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_string_storage", "typeString": "string"}, "typeName": {"id": 316, "name": "string", "nodeType": "ElementaryTypeName", "src": "1370:6:2", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "private"}, {"constant": false, "id": 319, "mutability": "mutable", "name": "_symbol", "nameLocation": "1411:7:2", "nodeType": "VariableDeclaration", "scope": 799, "src": "1396:22:2", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_string_storage", "typeString": "string"}, "typeName": {"id": 318, "name": "string", "nodeType": "ElementaryTypeName", "src": "1396:6:2", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "private"}, {"body": {"id": 335, "nodeType": "Block", "src": "1638:57:2", "statements": [{"expression": {"id": 329, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 327, "name": "_name", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 317, "src": "1648:5:2", "typeDescriptions": {"typeIdentifier": "t_string_storage", "typeString": "string storage ref"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 328, "name": "name_", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 322, "src": "1656:5:2", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "src": "1648:13:2", "typeDescriptions": {"typeIdentifier": "t_string_storage", "typeString": "string storage ref"}}, "id": 330, "nodeType": "ExpressionStatement", "src": "1648:13:2"}, {"expression": {"id": 333, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 331, "name": "_symbol", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 319, "src": "1671:7:2", "typeDescriptions": {"typeIdentifier": "t_string_storage", "typeString": "string storage ref"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 332, "name": "symbol_", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 324, "src": "1681:7:2", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "src": "1671:17:2", "typeDescriptions": {"typeIdentifier": "t_string_storage", "typeString": "string storage ref"}}, "id": 334, "nodeType": "ExpressionStatement", "src": "1671:17:2"}]}, "documentation": {"id": 320, "nodeType": "StructuredDocumentation", "src": "1425:152:2", "text": " @dev Sets the values for {name} and {symbol}.\n Both values are immutable: they can only be set once during construction."}, "id": 336, "implemented": true, "kind": "constructor", "modifiers": [], "name": "", "nameLocation": "-1:-1:-1", "nodeType": "FunctionDefinition", "parameters": {"id": 325, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 322, "mutability": "mutable", "name": "name_", "nameLocation": "1608:5:2", "nodeType": "VariableDeclaration", "scope": 336, "src": "1594:19:2", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 321, "name": "string", "nodeType": "ElementaryTypeName", "src": "1594:6:2", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 324, "mutability": "mutable", "name": "symbol_", "nameLocation": "1629:7:2", "nodeType": "VariableDeclaration", "scope": 336, "src": "1615:21:2", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 323, "name": "string", "nodeType": "ElementaryTypeName", "src": "1615:6:2", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "1593:44:2"}, "returnParameters": {"id": 326, "nodeType": "ParameterList", "parameters": [], "src": "1638:0:2"}, "scope": 799, "src": "1582:113:2", "stateMutability": "nonpayable", "virtual": false, "visibility": "internal"}, {"baseFunctions": [890], "body": {"id": 344, "nodeType": "Block", "src": "1820:29:2", "statements": [{"expression": {"id": 342, "name": "_name", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 317, "src": "1837:5:2", "typeDescriptions": {"typeIdentifier": "t_string_storage", "typeString": "string storage ref"}}, "functionReturnParameters": 341, "id": 343, "nodeType": "Return", "src": "1830:12:2"}]}, "documentation": {"id": 337, "nodeType": "StructuredDocumentation", "src": "1701:54:2", "text": " @dev Returns the name of the token."}, "functionSelector": "06fdde03", "id": 345, "implemented": true, "kind": "function", "modifiers": [], "name": "name", "nameLocation": "1769:4:2", "nodeType": "FunctionDefinition", "parameters": {"id": 338, "nodeType": "ParameterList", "parameters": [], "src": "1773:2:2"}, "returnParameters": {"id": 341, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 340, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 345, "src": "1805:13:2", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 339, "name": "string", "nodeType": "ElementaryTypeName", "src": "1805:6:2", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "1804:15:2"}, "scope": 799, "src": "1760:89:2", "stateMutability": "view", "virtual": true, "visibility": "public"}, {"baseFunctions": [896], "body": {"id": 353, "nodeType": "Block", "src": "2024:31:2", "statements": [{"expression": {"id": 351, "name": "_symbol", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 319, "src": "2041:7:2", "typeDescriptions": {"typeIdentifier": "t_string_storage", "typeString": "string storage ref"}}, "functionReturnParameters": 350, "id": 352, "nodeType": "Return", "src": "2034:14:2"}]}, "documentation": {"id": 346, "nodeType": "StructuredDocumentation", "src": "1855:102:2", "text": " @dev Returns the symbol of the token, usually a shorter version of the\n name."}, "functionSelector": "95d89b41", "id": 354, "implemented": true, "kind": "function", "modifiers": [], "name": "symbol", "nameLocation": "1971:6:2", "nodeType": "FunctionDefinition", "parameters": {"id": 347, "nodeType": "ParameterList", "parameters": [], "src": "1977:2:2"}, "returnParameters": {"id": 350, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 349, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 354, "src": "2009:13:2", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 348, "name": "string", "nodeType": "ElementaryTypeName", "src": "2009:6:2", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "2008:15:2"}, "scope": 799, "src": "1962:93:2", "stateMutability": "view", "virtual": true, "visibility": "public"}, {"baseFunctions": [902], "body": {"id": 362, "nodeType": "Block", "src": "2744:26:2", "statements": [{"expression": {"hexValue": "3138", "id": 360, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "2761:2:2", "typeDescriptions": {"typeIdentifier": "t_rational_18_by_1", "typeString": "int_const 18"}, "value": "18"}, "functionReturnParameters": 359, "id": 361, "nodeType": "Return", "src": "2754:9:2"}]}, "documentation": {"id": 355, "nodeType": "StructuredDocumentation", "src": "2061:622:2", "text": " @dev Returns the number of decimals used to get its user representation.\n For example, if `decimals` equals `2`, a balance of `505` tokens should\n be displayed to a user as `5.05` (`505 / 10 ** 2`).\n Tokens usually opt for a value of 18, imitating the relationship between\n <PERSON><PERSON> and <PERSON>. This is the default value returned by this function, unless\n it's overridden.\n NOTE: This information is only used for _display_ purposes: it in\n no way affects any of the arithmetic of the contract, including\n {IERC20-balanceOf} and {IERC20-transfer}."}, "functionSelector": "313ce567", "id": 363, "implemented": true, "kind": "function", "modifiers": [], "name": "decimals", "nameLocation": "2697:8:2", "nodeType": "FunctionDefinition", "parameters": {"id": 356, "nodeType": "ParameterList", "parameters": [], "src": "2705:2:2"}, "returnParameters": {"id": 359, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 358, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 363, "src": "2737:5:2", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}, "typeName": {"id": 357, "name": "uint8", "nodeType": "ElementaryTypeName", "src": "2737:5:2", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}}, "visibility": "internal"}], "src": "2736:7:2"}, "scope": 799, "src": "2688:82:2", "stateMutability": "view", "virtual": true, "visibility": "public"}, {"baseFunctions": [826], "body": {"id": 371, "nodeType": "Block", "src": "2864:36:2", "statements": [{"expression": {"id": 369, "name": "_totalSupply", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 315, "src": "2881:12:2", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "functionReturnParameters": 368, "id": 370, "nodeType": "Return", "src": "2874:19:2"}]}, "documentation": {"id": 364, "nodeType": "StructuredDocumentation", "src": "2776:22:2", "text": "@inheritdoc IERC20"}, "functionSelector": "18160ddd", "id": 372, "implemented": true, "kind": "function", "modifiers": [], "name": "totalSupply", "nameLocation": "2812:11:2", "nodeType": "FunctionDefinition", "parameters": {"id": 365, "nodeType": "ParameterList", "parameters": [], "src": "2823:2:2"}, "returnParameters": {"id": 368, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 367, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 372, "src": "2855:7:2", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 366, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2855:7:2", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "2854:9:2"}, "scope": 799, "src": "2803:97:2", "stateMutability": "view", "virtual": true, "visibility": "public"}, {"baseFunctions": [834], "body": {"id": 384, "nodeType": "Block", "src": "3007:42:2", "statements": [{"expression": {"baseExpression": {"id": 380, "name": "_balances", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 307, "src": "3024:9:2", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 382, "indexExpression": {"id": 381, "name": "account", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 375, "src": "3034:7:2", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "3024:18:2", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "functionReturnParameters": 379, "id": 383, "nodeType": "Return", "src": "3017:25:2"}]}, "documentation": {"id": 373, "nodeType": "StructuredDocumentation", "src": "2906:22:2", "text": "@inheritdoc IERC20"}, "functionSelector": "70a08231", "id": 385, "implemented": true, "kind": "function", "modifiers": [], "name": "balanceOf", "nameLocation": "2942:9:2", "nodeType": "FunctionDefinition", "parameters": {"id": 376, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 375, "mutability": "mutable", "name": "account", "nameLocation": "2960:7:2", "nodeType": "VariableDeclaration", "scope": 385, "src": "2952:15:2", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 374, "name": "address", "nodeType": "ElementaryTypeName", "src": "2952:7:2", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "2951:17:2"}, "returnParameters": {"id": 379, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 378, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 385, "src": "2998:7:2", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 377, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2998:7:2", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "2997:9:2"}, "scope": 799, "src": "2933:116:2", "stateMutability": "view", "virtual": true, "visibility": "public"}, {"baseFunctions": [844], "body": {"id": 408, "nodeType": "Block", "src": "3319:103:2", "statements": [{"assignments": [396], "declarations": [{"constant": false, "id": 396, "mutability": "mutable", "name": "owner", "nameLocation": "3337:5:2", "nodeType": "VariableDeclaration", "scope": 408, "src": "3329:13:2", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 395, "name": "address", "nodeType": "ElementaryTypeName", "src": "3329:7:2", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "id": 399, "initialValue": {"arguments": [], "expression": {"argumentTypes": [], "id": 397, "name": "_msgSender", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 915, "src": "3345:10:2", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$__$returns$_t_address_$", "typeString": "function () view returns (address)"}}, "id": 398, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3345:12:2", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "VariableDeclarationStatement", "src": "3329:28:2"}, {"expression": {"arguments": [{"id": 401, "name": "owner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 396, "src": "3377:5:2", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 402, "name": "to", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 388, "src": "3384:2:2", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 403, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 390, "src": "3388:5:2", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 400, "name": "_transfer", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 529, "src": "3367:9:2", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_address_$_t_address_$_t_uint256_$returns$__$", "typeString": "function (address,address,uint256)"}}, "id": 404, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3367:27:2", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 405, "nodeType": "ExpressionStatement", "src": "3367:27:2"}, {"expression": {"hexValue": "74727565", "id": 406, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "3411:4:2", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "true"}, "functionReturnParameters": 394, "id": 407, "nodeType": "Return", "src": "3404:11:2"}]}, "documentation": {"id": 386, "nodeType": "StructuredDocumentation", "src": "3055:184:2", "text": " @dev See {IERC20-transfer}.\n Requirements:\n - `to` cannot be the zero address.\n - the caller must have a balance of at least `value`."}, "functionSelector": "a9059cbb", "id": 409, "implemented": true, "kind": "function", "modifiers": [], "name": "transfer", "nameLocation": "3253:8:2", "nodeType": "FunctionDefinition", "parameters": {"id": 391, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 388, "mutability": "mutable", "name": "to", "nameLocation": "3270:2:2", "nodeType": "VariableDeclaration", "scope": 409, "src": "3262:10:2", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 387, "name": "address", "nodeType": "ElementaryTypeName", "src": "3262:7:2", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 390, "mutability": "mutable", "name": "value", "nameLocation": "3282:5:2", "nodeType": "VariableDeclaration", "scope": 409, "src": "3274:13:2", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 389, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "3274:7:2", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "3261:27:2"}, "returnParameters": {"id": 394, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 393, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 409, "src": "3313:4:2", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 392, "name": "bool", "nodeType": "ElementaryTypeName", "src": "3313:4:2", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "3312:6:2"}, "scope": 799, "src": "3244:178:2", "stateMutability": "nonpayable", "virtual": true, "visibility": "public"}, {"baseFunctions": [854], "body": {"id": 425, "nodeType": "Block", "src": "3544:51:2", "statements": [{"expression": {"baseExpression": {"baseExpression": {"id": 419, "name": "_allowances", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 313, "src": "3561:11:2", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_mapping$_t_address_$_t_uint256_$_$", "typeString": "mapping(address => mapping(address => uint256))"}}, "id": 421, "indexExpression": {"id": 420, "name": "owner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 412, "src": "3573:5:2", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "3561:18:2", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 423, "indexExpression": {"id": 422, "name": "spender", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 414, "src": "3580:7:2", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "3561:27:2", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "functionReturnParameters": 418, "id": 424, "nodeType": "Return", "src": "3554:34:2"}]}, "documentation": {"id": 410, "nodeType": "StructuredDocumentation", "src": "3428:22:2", "text": "@inheritdoc IERC20"}, "functionSelector": "dd62ed3e", "id": 426, "implemented": true, "kind": "function", "modifiers": [], "name": "allowance", "nameLocation": "3464:9:2", "nodeType": "FunctionDefinition", "parameters": {"id": 415, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 412, "mutability": "mutable", "name": "owner", "nameLocation": "3482:5:2", "nodeType": "VariableDeclaration", "scope": 426, "src": "3474:13:2", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 411, "name": "address", "nodeType": "ElementaryTypeName", "src": "3474:7:2", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 414, "mutability": "mutable", "name": "spender", "nameLocation": "3497:7:2", "nodeType": "VariableDeclaration", "scope": 426, "src": "3489:15:2", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 413, "name": "address", "nodeType": "ElementaryTypeName", "src": "3489:7:2", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "3473:32:2"}, "returnParameters": {"id": 418, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 417, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 426, "src": "3535:7:2", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 416, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "3535:7:2", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "3534:9:2"}, "scope": 799, "src": "3455:140:2", "stateMutability": "view", "virtual": true, "visibility": "public"}, {"baseFunctions": [864], "body": {"id": 449, "nodeType": "Block", "src": "3981:107:2", "statements": [{"assignments": [437], "declarations": [{"constant": false, "id": 437, "mutability": "mutable", "name": "owner", "nameLocation": "3999:5:2", "nodeType": "VariableDeclaration", "scope": 449, "src": "3991:13:2", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 436, "name": "address", "nodeType": "ElementaryTypeName", "src": "3991:7:2", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "id": 440, "initialValue": {"arguments": [], "expression": {"argumentTypes": [], "id": 438, "name": "_msgSender", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 915, "src": "4007:10:2", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$__$returns$_t_address_$", "typeString": "function () view returns (address)"}}, "id": 439, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4007:12:2", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "VariableDeclarationStatement", "src": "3991:28:2"}, {"expression": {"arguments": [{"id": 442, "name": "owner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 437, "src": "4038:5:2", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 443, "name": "spender", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 429, "src": "4045:7:2", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 444, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 431, "src": "4054:5:2", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 441, "name": "_approve", "nodeType": "Identifier", "overloadedDeclarations": [690, 750], "referencedDeclaration": 690, "src": "4029:8:2", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_address_$_t_address_$_t_uint256_$returns$__$", "typeString": "function (address,address,uint256)"}}, "id": 445, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4029:31:2", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 446, "nodeType": "ExpressionStatement", "src": "4029:31:2"}, {"expression": {"hexValue": "74727565", "id": 447, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "4077:4:2", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "true"}, "functionReturnParameters": 435, "id": 448, "nodeType": "Return", "src": "4070:11:2"}]}, "documentation": {"id": 427, "nodeType": "StructuredDocumentation", "src": "3601:296:2", "text": " @dev See {IERC20-approve}.\n NOTE: If `value` is the maximum `uint256`, the allowance is not updated on\n `transferFrom`. This is semantically equivalent to an infinite approval.\n Requirements:\n - `spender` cannot be the zero address."}, "functionSelector": "095ea7b3", "id": 450, "implemented": true, "kind": "function", "modifiers": [], "name": "approve", "nameLocation": "3911:7:2", "nodeType": "FunctionDefinition", "parameters": {"id": 432, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 429, "mutability": "mutable", "name": "spender", "nameLocation": "3927:7:2", "nodeType": "VariableDeclaration", "scope": 450, "src": "3919:15:2", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 428, "name": "address", "nodeType": "ElementaryTypeName", "src": "3919:7:2", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 431, "mutability": "mutable", "name": "value", "nameLocation": "3944:5:2", "nodeType": "VariableDeclaration", "scope": 450, "src": "3936:13:2", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 430, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "3936:7:2", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "3918:32:2"}, "returnParameters": {"id": 435, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 434, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 450, "src": "3975:4:2", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 433, "name": "bool", "nodeType": "ElementaryTypeName", "src": "3975:4:2", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "3974:6:2"}, "scope": 799, "src": "3902:186:2", "stateMutability": "nonpayable", "virtual": true, "visibility": "public"}, {"baseFunctions": [876], "body": {"id": 481, "nodeType": "Block", "src": "4773:151:2", "statements": [{"assignments": [463], "declarations": [{"constant": false, "id": 463, "mutability": "mutable", "name": "spender", "nameLocation": "4791:7:2", "nodeType": "VariableDeclaration", "scope": 481, "src": "4783:15:2", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 462, "name": "address", "nodeType": "ElementaryTypeName", "src": "4783:7:2", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "id": 466, "initialValue": {"arguments": [], "expression": {"argumentTypes": [], "id": 464, "name": "_msgSender", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 915, "src": "4801:10:2", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$__$returns$_t_address_$", "typeString": "function () view returns (address)"}}, "id": 465, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4801:12:2", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "VariableDeclarationStatement", "src": "4783:30:2"}, {"expression": {"arguments": [{"id": 468, "name": "from", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 453, "src": "4839:4:2", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 469, "name": "spender", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 463, "src": "4845:7:2", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 470, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 457, "src": "4854:5:2", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 467, "name": "_spendAllowance", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 798, "src": "4823:15:2", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_address_$_t_address_$_t_uint256_$returns$__$", "typeString": "function (address,address,uint256)"}}, "id": 471, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4823:37:2", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 472, "nodeType": "ExpressionStatement", "src": "4823:37:2"}, {"expression": {"arguments": [{"id": 474, "name": "from", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 453, "src": "4880:4:2", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 475, "name": "to", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 455, "src": "4886:2:2", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 476, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 457, "src": "4890:5:2", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 473, "name": "_transfer", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 529, "src": "4870:9:2", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_address_$_t_address_$_t_uint256_$returns$__$", "typeString": "function (address,address,uint256)"}}, "id": 477, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4870:26:2", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 478, "nodeType": "ExpressionStatement", "src": "4870:26:2"}, {"expression": {"hexValue": "74727565", "id": 479, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "4913:4:2", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "true"}, "functionReturnParameters": 461, "id": 480, "nodeType": "Return", "src": "4906:11:2"}]}, "documentation": {"id": 451, "nodeType": "StructuredDocumentation", "src": "4094:581:2", "text": " @dev See {IERC20-transferFrom}.\n Skips emitting an {Approval} event indicating an allowance update. This is not\n required by the ERC. See {xref-ERC20-_approve-address-address-uint256-bool-}[_approve].\n NOTE: Does not update the allowance if the current allowance\n is the maximum `uint256`.\n Requirements:\n - `from` and `to` cannot be the zero address.\n - `from` must have a balance of at least `value`.\n - the caller must have allowance for ``from``'s tokens of at least\n `value`."}, "functionSelector": "23b872dd", "id": 482, "implemented": true, "kind": "function", "modifiers": [], "name": "transferFrom", "nameLocation": "4689:12:2", "nodeType": "FunctionDefinition", "parameters": {"id": 458, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 453, "mutability": "mutable", "name": "from", "nameLocation": "4710:4:2", "nodeType": "VariableDeclaration", "scope": 482, "src": "4702:12:2", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 452, "name": "address", "nodeType": "ElementaryTypeName", "src": "4702:7:2", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 455, "mutability": "mutable", "name": "to", "nameLocation": "4724:2:2", "nodeType": "VariableDeclaration", "scope": 482, "src": "4716:10:2", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 454, "name": "address", "nodeType": "ElementaryTypeName", "src": "4716:7:2", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 457, "mutability": "mutable", "name": "value", "nameLocation": "4736:5:2", "nodeType": "VariableDeclaration", "scope": 482, "src": "4728:13:2", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 456, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "4728:7:2", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "4701:41:2"}, "returnParameters": {"id": 461, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 460, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 482, "src": "4767:4:2", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 459, "name": "bool", "nodeType": "ElementaryTypeName", "src": "4767:4:2", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "4766:6:2"}, "scope": 799, "src": "4680:244:2", "stateMutability": "nonpayable", "virtual": true, "visibility": "public"}, {"body": {"id": 528, "nodeType": "Block", "src": "5366:231:2", "statements": [{"condition": {"commonType": {"typeIdentifier": "t_address", "typeString": "address"}, "id": 497, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 492, "name": "from", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 485, "src": "5380:4:2", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"arguments": [{"hexValue": "30", "id": 495, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "5396:1:2", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}], "id": 494, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "5388:7:2", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 493, "name": "address", "nodeType": "ElementaryTypeName", "src": "5388:7:2", "typeDescriptions": {}}}, "id": 496, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5388:10:2", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "src": "5380:18:2", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 506, "nodeType": "IfStatement", "src": "5376:86:2", "trueBody": {"id": 505, "nodeType": "Block", "src": "5400:62:2", "statements": [{"errorCall": {"arguments": [{"arguments": [{"hexValue": "30", "id": 501, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "5448:1:2", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}], "id": 500, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "5440:7:2", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 499, "name": "address", "nodeType": "ElementaryTypeName", "src": "5440:7:2", "typeDescriptions": {}}}, "id": 502, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5440:10:2", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 498, "name": "ERC20InvalidSender", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 164, "src": "5421:18:2", "typeDescriptions": {"typeIdentifier": "t_function_error_pure$_t_address_$returns$__$", "typeString": "function (address) pure"}}, "id": 503, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5421:30:2", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 504, "nodeType": "RevertStatement", "src": "5414:37:2"}]}}, {"condition": {"commonType": {"typeIdentifier": "t_address", "typeString": "address"}, "id": 512, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 507, "name": "to", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 487, "src": "5475:2:2", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"arguments": [{"hexValue": "30", "id": 510, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "5489:1:2", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}], "id": 509, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "5481:7:2", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 508, "name": "address", "nodeType": "ElementaryTypeName", "src": "5481:7:2", "typeDescriptions": {}}}, "id": 511, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5481:10:2", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "src": "5475:16:2", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 521, "nodeType": "IfStatement", "src": "5471:86:2", "trueBody": {"id": 520, "nodeType": "Block", "src": "5493:64:2", "statements": [{"errorCall": {"arguments": [{"arguments": [{"hexValue": "30", "id": 516, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "5543:1:2", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}], "id": 515, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "5535:7:2", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 514, "name": "address", "nodeType": "ElementaryTypeName", "src": "5535:7:2", "typeDescriptions": {}}}, "id": 517, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5535:10:2", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 513, "name": "ERC20InvalidReceiver", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 169, "src": "5514:20:2", "typeDescriptions": {"typeIdentifier": "t_function_error_pure$_t_address_$returns$__$", "typeString": "function (address) pure"}}, "id": 518, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5514:32:2", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 519, "nodeType": "RevertStatement", "src": "5507:39:2"}]}}, {"expression": {"arguments": [{"id": 523, "name": "from", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 485, "src": "5574:4:2", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 524, "name": "to", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 487, "src": "5580:2:2", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 525, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 489, "src": "5584:5:2", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 522, "name": "_update", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 606, "src": "5566:7:2", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_address_$_t_address_$_t_uint256_$returns$__$", "typeString": "function (address,address,uint256)"}}, "id": 526, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5566:24:2", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 527, "nodeType": "ExpressionStatement", "src": "5566:24:2"}]}, "documentation": {"id": 483, "nodeType": "StructuredDocumentation", "src": "4930:362:2", "text": " @dev Moves a `value` amount of tokens from `from` to `to`.\n This internal function is equivalent to {transfer}, and can be used to\n e.g. implement automatic token fees, slashing mechanisms, etc.\n Emits a {Transfer} event.\n NOTE: This function is not virtual, {_update} should be overridden instead."}, "id": 529, "implemented": true, "kind": "function", "modifiers": [], "name": "_transfer", "nameLocation": "5306:9:2", "nodeType": "FunctionDefinition", "parameters": {"id": 490, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 485, "mutability": "mutable", "name": "from", "nameLocation": "5324:4:2", "nodeType": "VariableDeclaration", "scope": 529, "src": "5316:12:2", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 484, "name": "address", "nodeType": "ElementaryTypeName", "src": "5316:7:2", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 487, "mutability": "mutable", "name": "to", "nameLocation": "5338:2:2", "nodeType": "VariableDeclaration", "scope": 529, "src": "5330:10:2", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 486, "name": "address", "nodeType": "ElementaryTypeName", "src": "5330:7:2", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 489, "mutability": "mutable", "name": "value", "nameLocation": "5350:5:2", "nodeType": "VariableDeclaration", "scope": 529, "src": "5342:13:2", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 488, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "5342:7:2", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "5315:41:2"}, "returnParameters": {"id": 491, "nodeType": "ParameterList", "parameters": [], "src": "5366:0:2"}, "scope": 799, "src": "5297:300:2", "stateMutability": "nonpayable", "virtual": false, "visibility": "internal"}, {"body": {"id": 605, "nodeType": "Block", "src": "5987:1032:2", "statements": [{"condition": {"commonType": {"typeIdentifier": "t_address", "typeString": "address"}, "id": 544, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 539, "name": "from", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 532, "src": "6001:4:2", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"arguments": [{"hexValue": "30", "id": 542, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "6017:1:2", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}], "id": 541, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "6009:7:2", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 540, "name": "address", "nodeType": "ElementaryTypeName", "src": "6009:7:2", "typeDescriptions": {}}}, "id": 543, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6009:10:2", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "src": "6001:18:2", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "falseBody": {"id": 576, "nodeType": "Block", "src": "6175:362:2", "statements": [{"assignments": [551], "declarations": [{"constant": false, "id": 551, "mutability": "mutable", "name": "fromBalance", "nameLocation": "6197:11:2", "nodeType": "VariableDeclaration", "scope": 576, "src": "6189:19:2", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 550, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "6189:7:2", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 555, "initialValue": {"baseExpression": {"id": 552, "name": "_balances", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 307, "src": "6211:9:2", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 554, "indexExpression": {"id": 553, "name": "from", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 532, "src": "6221:4:2", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "6211:15:2", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "VariableDeclarationStatement", "src": "6189:37:2"}, {"condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 558, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 556, "name": "fromBalance", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 551, "src": "6244:11:2", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<", "rightExpression": {"id": 557, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 536, "src": "6258:5:2", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "6244:19:2", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 566, "nodeType": "IfStatement", "src": "6240:115:2", "trueBody": {"id": 565, "nodeType": "Block", "src": "6265:90:2", "statements": [{"errorCall": {"arguments": [{"id": 560, "name": "from", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 532, "src": "6315:4:2", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 561, "name": "fromBalance", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 551, "src": "6321:11:2", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"id": 562, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 536, "src": "6334:5:2", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 559, "name": "ERC20InsufficientBalance", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 159, "src": "6290:24:2", "typeDescriptions": {"typeIdentifier": "t_function_error_pure$_t_address_$_t_uint256_$_t_uint256_$returns$__$", "typeString": "function (address,uint256,uint256) pure"}}, "id": 563, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6290:50:2", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 564, "nodeType": "RevertStatement", "src": "6283:57:2"}]}}, {"id": 575, "nodeType": "UncheckedBlock", "src": "6368:159:2", "statements": [{"expression": {"id": 573, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"baseExpression": {"id": 567, "name": "_balances", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 307, "src": "6475:9:2", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 569, "indexExpression": {"id": 568, "name": "from", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 532, "src": "6485:4:2", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "6475:15:2", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 572, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 570, "name": "fromBalance", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 551, "src": "6493:11:2", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "-", "rightExpression": {"id": 571, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 536, "src": "6507:5:2", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "6493:19:2", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "6475:37:2", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 574, "nodeType": "ExpressionStatement", "src": "6475:37:2"}]}]}, "id": 577, "nodeType": "IfStatement", "src": "5997:540:2", "trueBody": {"id": 549, "nodeType": "Block", "src": "6021:148:2", "statements": [{"expression": {"id": 547, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 545, "name": "_totalSupply", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 315, "src": "6137:12:2", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "+=", "rightHandSide": {"id": 546, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 536, "src": "6153:5:2", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "6137:21:2", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 548, "nodeType": "ExpressionStatement", "src": "6137:21:2"}]}}, {"condition": {"commonType": {"typeIdentifier": "t_address", "typeString": "address"}, "id": 583, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 578, "name": "to", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 534, "src": "6551:2:2", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"arguments": [{"hexValue": "30", "id": 581, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "6565:1:2", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}], "id": 580, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "6557:7:2", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 579, "name": "address", "nodeType": "ElementaryTypeName", "src": "6557:7:2", "typeDescriptions": {}}}, "id": 582, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6557:10:2", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "src": "6551:16:2", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "falseBody": {"id": 597, "nodeType": "Block", "src": "6766:206:2", "statements": [{"id": 596, "nodeType": "UncheckedBlock", "src": "6780:182:2", "statements": [{"expression": {"id": 594, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"baseExpression": {"id": 590, "name": "_balances", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 307, "src": "6925:9:2", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 592, "indexExpression": {"id": 591, "name": "to", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 534, "src": "6935:2:2", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "6925:13:2", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "+=", "rightHandSide": {"id": 593, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 536, "src": "6942:5:2", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "6925:22:2", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 595, "nodeType": "ExpressionStatement", "src": "6925:22:2"}]}]}, "id": 598, "nodeType": "IfStatement", "src": "6547:425:2", "trueBody": {"id": 589, "nodeType": "Block", "src": "6569:191:2", "statements": [{"id": 588, "nodeType": "UncheckedBlock", "src": "6583:167:2", "statements": [{"expression": {"id": 586, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 584, "name": "_totalSupply", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 315, "src": "6714:12:2", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "-=", "rightHandSide": {"id": 585, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 536, "src": "6730:5:2", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "6714:21:2", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 587, "nodeType": "ExpressionStatement", "src": "6714:21:2"}]}]}}, {"eventCall": {"arguments": [{"id": 600, "name": "from", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 532, "src": "6996:4:2", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 601, "name": "to", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 534, "src": "7002:2:2", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 602, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 536, "src": "7006:5:2", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 599, "name": "Transfer", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 811, "src": "6987:8:2", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_address_$_t_address_$_t_uint256_$returns$__$", "typeString": "function (address,address,uint256)"}}, "id": 603, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6987:25:2", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 604, "nodeType": "EmitStatement", "src": "6982:30:2"}]}, "documentation": {"id": 530, "nodeType": "StructuredDocumentation", "src": "5603:304:2", "text": " @dev Transfers a `value` amount of tokens from `from` to `to`, or alternatively mints (or burns) if `from`\n (or `to`) is the zero address. All customizations to transfers, mints, and burns should be done by overriding\n this function.\n Emits a {Transfer} event."}, "id": 606, "implemented": true, "kind": "function", "modifiers": [], "name": "_update", "nameLocation": "5921:7:2", "nodeType": "FunctionDefinition", "parameters": {"id": 537, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 532, "mutability": "mutable", "name": "from", "nameLocation": "5937:4:2", "nodeType": "VariableDeclaration", "scope": 606, "src": "5929:12:2", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 531, "name": "address", "nodeType": "ElementaryTypeName", "src": "5929:7:2", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 534, "mutability": "mutable", "name": "to", "nameLocation": "5951:2:2", "nodeType": "VariableDeclaration", "scope": 606, "src": "5943:10:2", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 533, "name": "address", "nodeType": "ElementaryTypeName", "src": "5943:7:2", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 536, "mutability": "mutable", "name": "value", "nameLocation": "5963:5:2", "nodeType": "VariableDeclaration", "scope": 606, "src": "5955:13:2", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 535, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "5955:7:2", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "5928:41:2"}, "returnParameters": {"id": 538, "nodeType": "ParameterList", "parameters": [], "src": "5987:0:2"}, "scope": 799, "src": "5912:1107:2", "stateMutability": "nonpayable", "virtual": true, "visibility": "internal"}, {"body": {"id": 638, "nodeType": "Block", "src": "7418:152:2", "statements": [{"condition": {"commonType": {"typeIdentifier": "t_address", "typeString": "address"}, "id": 619, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 614, "name": "account", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 609, "src": "7432:7:2", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"arguments": [{"hexValue": "30", "id": 617, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "7451:1:2", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}], "id": 616, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "7443:7:2", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 615, "name": "address", "nodeType": "ElementaryTypeName", "src": "7443:7:2", "typeDescriptions": {}}}, "id": 618, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "7443:10:2", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "src": "7432:21:2", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 628, "nodeType": "IfStatement", "src": "7428:91:2", "trueBody": {"id": 627, "nodeType": "Block", "src": "7455:64:2", "statements": [{"errorCall": {"arguments": [{"arguments": [{"hexValue": "30", "id": 623, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "7505:1:2", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}], "id": 622, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "7497:7:2", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 621, "name": "address", "nodeType": "ElementaryTypeName", "src": "7497:7:2", "typeDescriptions": {}}}, "id": 624, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "7497:10:2", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 620, "name": "ERC20InvalidReceiver", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 169, "src": "7476:20:2", "typeDescriptions": {"typeIdentifier": "t_function_error_pure$_t_address_$returns$__$", "typeString": "function (address) pure"}}, "id": 625, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "7476:32:2", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 626, "nodeType": "RevertStatement", "src": "7469:39:2"}]}}, {"expression": {"arguments": [{"arguments": [{"hexValue": "30", "id": 632, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "7544:1:2", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}], "id": 631, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "7536:7:2", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 630, "name": "address", "nodeType": "ElementaryTypeName", "src": "7536:7:2", "typeDescriptions": {}}}, "id": 633, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "7536:10:2", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 634, "name": "account", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 609, "src": "7548:7:2", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 635, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 611, "src": "7557:5:2", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 629, "name": "_update", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 606, "src": "7528:7:2", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_address_$_t_address_$_t_uint256_$returns$__$", "typeString": "function (address,address,uint256)"}}, "id": 636, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "7528:35:2", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 637, "nodeType": "ExpressionStatement", "src": "7528:35:2"}]}, "documentation": {"id": 607, "nodeType": "StructuredDocumentation", "src": "7025:332:2", "text": " @dev Creates a `value` amount of tokens and assigns them to `account`, by transferring it from address(0).\n Relies on the `_update` mechanism\n Emits a {Transfer} event with `from` set to the zero address.\n NOTE: This function is not virtual, {_update} should be overridden instead."}, "id": 639, "implemented": true, "kind": "function", "modifiers": [], "name": "_mint", "nameLocation": "7371:5:2", "nodeType": "FunctionDefinition", "parameters": {"id": 612, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 609, "mutability": "mutable", "name": "account", "nameLocation": "7385:7:2", "nodeType": "VariableDeclaration", "scope": 639, "src": "7377:15:2", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 608, "name": "address", "nodeType": "ElementaryTypeName", "src": "7377:7:2", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 611, "mutability": "mutable", "name": "value", "nameLocation": "7402:5:2", "nodeType": "VariableDeclaration", "scope": 639, "src": "7394:13:2", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 610, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "7394:7:2", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "7376:32:2"}, "returnParameters": {"id": 613, "nodeType": "ParameterList", "parameters": [], "src": "7418:0:2"}, "scope": 799, "src": "7362:208:2", "stateMutability": "nonpayable", "virtual": false, "visibility": "internal"}, {"body": {"id": 671, "nodeType": "Block", "src": "7944:150:2", "statements": [{"condition": {"commonType": {"typeIdentifier": "t_address", "typeString": "address"}, "id": 652, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 647, "name": "account", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 642, "src": "7958:7:2", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"arguments": [{"hexValue": "30", "id": 650, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "7977:1:2", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}], "id": 649, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "7969:7:2", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 648, "name": "address", "nodeType": "ElementaryTypeName", "src": "7969:7:2", "typeDescriptions": {}}}, "id": 651, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "7969:10:2", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "src": "7958:21:2", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 661, "nodeType": "IfStatement", "src": "7954:89:2", "trueBody": {"id": 660, "nodeType": "Block", "src": "7981:62:2", "statements": [{"errorCall": {"arguments": [{"arguments": [{"hexValue": "30", "id": 656, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "8029:1:2", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}], "id": 655, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "8021:7:2", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 654, "name": "address", "nodeType": "ElementaryTypeName", "src": "8021:7:2", "typeDescriptions": {}}}, "id": 657, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "8021:10:2", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 653, "name": "ERC20InvalidSender", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 164, "src": "8002:18:2", "typeDescriptions": {"typeIdentifier": "t_function_error_pure$_t_address_$returns$__$", "typeString": "function (address) pure"}}, "id": 658, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "8002:30:2", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 659, "nodeType": "RevertStatement", "src": "7995:37:2"}]}}, {"expression": {"arguments": [{"id": 663, "name": "account", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 642, "src": "8060:7:2", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"arguments": [{"hexValue": "30", "id": 666, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "8077:1:2", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}], "id": 665, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "8069:7:2", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 664, "name": "address", "nodeType": "ElementaryTypeName", "src": "8069:7:2", "typeDescriptions": {}}}, "id": 667, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "8069:10:2", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 668, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 644, "src": "8081:5:2", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 662, "name": "_update", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 606, "src": "8052:7:2", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_address_$_t_address_$_t_uint256_$returns$__$", "typeString": "function (address,address,uint256)"}}, "id": 669, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "8052:35:2", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 670, "nodeType": "ExpressionStatement", "src": "8052:35:2"}]}, "documentation": {"id": 640, "nodeType": "StructuredDocumentation", "src": "7576:307:2", "text": " @dev Destroys a `value` amount of tokens from `account`, lowering the total supply.\n Relies on the `_update` mechanism.\n Emits a {Transfer} event with `to` set to the zero address.\n NOTE: This function is not virtual, {_update} should be overridden instead"}, "id": 672, "implemented": true, "kind": "function", "modifiers": [], "name": "_burn", "nameLocation": "7897:5:2", "nodeType": "FunctionDefinition", "parameters": {"id": 645, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 642, "mutability": "mutable", "name": "account", "nameLocation": "7911:7:2", "nodeType": "VariableDeclaration", "scope": 672, "src": "7903:15:2", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 641, "name": "address", "nodeType": "ElementaryTypeName", "src": "7903:7:2", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 644, "mutability": "mutable", "name": "value", "nameLocation": "7928:5:2", "nodeType": "VariableDeclaration", "scope": 672, "src": "7920:13:2", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 643, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "7920:7:2", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "7902:32:2"}, "returnParameters": {"id": 646, "nodeType": "ParameterList", "parameters": [], "src": "7944:0:2"}, "scope": 799, "src": "7888:206:2", "stateMutability": "nonpayable", "virtual": false, "visibility": "internal"}, {"body": {"id": 689, "nodeType": "Block", "src": "8704:54:2", "statements": [{"expression": {"arguments": [{"id": 683, "name": "owner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 675, "src": "8723:5:2", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 684, "name": "spender", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 677, "src": "8730:7:2", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 685, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 679, "src": "8739:5:2", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"hexValue": "74727565", "id": 686, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "8746:4:2", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "true"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_bool", "typeString": "bool"}], "id": 682, "name": "_approve", "nodeType": "Identifier", "overloadedDeclarations": [690, 750], "referencedDeclaration": 750, "src": "8714:8:2", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_address_$_t_address_$_t_uint256_$_t_bool_$returns$__$", "typeString": "function (address,address,uint256,bool)"}}, "id": 687, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "8714:37:2", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 688, "nodeType": "ExpressionStatement", "src": "8714:37:2"}]}, "documentation": {"id": 673, "nodeType": "StructuredDocumentation", "src": "8100:525:2", "text": " @dev Sets `value` as the allowance of `spender` over the `owner`'s tokens.\n This internal function is equivalent to `approve`, and can be used to\n e.g. set automatic allowances for certain subsystems, etc.\n Emits an {Approval} event.\n Requirements:\n - `owner` cannot be the zero address.\n - `spender` cannot be the zero address.\n Overrides to this logic should be done to the variant with an additional `bool emitEvent` argument."}, "id": 690, "implemented": true, "kind": "function", "modifiers": [], "name": "_approve", "nameLocation": "8639:8:2", "nodeType": "FunctionDefinition", "parameters": {"id": 680, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 675, "mutability": "mutable", "name": "owner", "nameLocation": "8656:5:2", "nodeType": "VariableDeclaration", "scope": 690, "src": "8648:13:2", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 674, "name": "address", "nodeType": "ElementaryTypeName", "src": "8648:7:2", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 677, "mutability": "mutable", "name": "spender", "nameLocation": "8671:7:2", "nodeType": "VariableDeclaration", "scope": 690, "src": "8663:15:2", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 676, "name": "address", "nodeType": "ElementaryTypeName", "src": "8663:7:2", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 679, "mutability": "mutable", "name": "value", "nameLocation": "8688:5:2", "nodeType": "VariableDeclaration", "scope": 690, "src": "8680:13:2", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 678, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "8680:7:2", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "8647:47:2"}, "returnParameters": {"id": 681, "nodeType": "ParameterList", "parameters": [], "src": "8704:0:2"}, "scope": 799, "src": "8630:128:2", "stateMutability": "nonpayable", "virtual": false, "visibility": "internal"}, {"body": {"id": 749, "nodeType": "Block", "src": "9703:334:2", "statements": [{"condition": {"commonType": {"typeIdentifier": "t_address", "typeString": "address"}, "id": 707, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 702, "name": "owner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 693, "src": "9717:5:2", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"arguments": [{"hexValue": "30", "id": 705, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "9734:1:2", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}], "id": 704, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "9726:7:2", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 703, "name": "address", "nodeType": "ElementaryTypeName", "src": "9726:7:2", "typeDescriptions": {}}}, "id": 706, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "9726:10:2", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "src": "9717:19:2", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 716, "nodeType": "IfStatement", "src": "9713:89:2", "trueBody": {"id": 715, "nodeType": "Block", "src": "9738:64:2", "statements": [{"errorCall": {"arguments": [{"arguments": [{"hexValue": "30", "id": 711, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "9788:1:2", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}], "id": 710, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "9780:7:2", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 709, "name": "address", "nodeType": "ElementaryTypeName", "src": "9780:7:2", "typeDescriptions": {}}}, "id": 712, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "9780:10:2", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 708, "name": "ERC20InvalidApprover", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 183, "src": "9759:20:2", "typeDescriptions": {"typeIdentifier": "t_function_error_pure$_t_address_$returns$__$", "typeString": "function (address) pure"}}, "id": 713, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "9759:32:2", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 714, "nodeType": "RevertStatement", "src": "9752:39:2"}]}}, {"condition": {"commonType": {"typeIdentifier": "t_address", "typeString": "address"}, "id": 722, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 717, "name": "spender", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 695, "src": "9815:7:2", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"arguments": [{"hexValue": "30", "id": 720, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "9834:1:2", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}], "id": 719, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "9826:7:2", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 718, "name": "address", "nodeType": "ElementaryTypeName", "src": "9826:7:2", "typeDescriptions": {}}}, "id": 721, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "9826:10:2", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "src": "9815:21:2", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 731, "nodeType": "IfStatement", "src": "9811:90:2", "trueBody": {"id": 730, "nodeType": "Block", "src": "9838:63:2", "statements": [{"errorCall": {"arguments": [{"arguments": [{"hexValue": "30", "id": 726, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "9887:1:2", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}], "id": 725, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "9879:7:2", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 724, "name": "address", "nodeType": "ElementaryTypeName", "src": "9879:7:2", "typeDescriptions": {}}}, "id": 727, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "9879:10:2", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 723, "name": "ERC20InvalidSpender", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 188, "src": "9859:19:2", "typeDescriptions": {"typeIdentifier": "t_function_error_pure$_t_address_$returns$__$", "typeString": "function (address) pure"}}, "id": 728, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "9859:31:2", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 729, "nodeType": "RevertStatement", "src": "9852:38:2"}]}}, {"expression": {"id": 738, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"baseExpression": {"baseExpression": {"id": 732, "name": "_allowances", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 313, "src": "9910:11:2", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_mapping$_t_address_$_t_uint256_$_$", "typeString": "mapping(address => mapping(address => uint256))"}}, "id": 735, "indexExpression": {"id": 733, "name": "owner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 693, "src": "9922:5:2", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "9910:18:2", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 736, "indexExpression": {"id": 734, "name": "spender", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 695, "src": "9929:7:2", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "9910:27:2", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 737, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 697, "src": "9940:5:2", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "9910:35:2", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 739, "nodeType": "ExpressionStatement", "src": "9910:35:2"}, {"condition": {"id": 740, "name": "emitEvent", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 699, "src": "9959:9:2", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 748, "nodeType": "IfStatement", "src": "9955:76:2", "trueBody": {"id": 747, "nodeType": "Block", "src": "9970:61:2", "statements": [{"eventCall": {"arguments": [{"id": 742, "name": "owner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 693, "src": "9998:5:2", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 743, "name": "spender", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 695, "src": "10005:7:2", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 744, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 697, "src": "10014:5:2", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 741, "name": "Approval", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 820, "src": "9989:8:2", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_address_$_t_address_$_t_uint256_$returns$__$", "typeString": "function (address,address,uint256)"}}, "id": 745, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "9989:31:2", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 746, "nodeType": "EmitStatement", "src": "9984:36:2"}]}}]}, "documentation": {"id": 691, "nodeType": "StructuredDocumentation", "src": "8764:836:2", "text": " @dev Variant of {_approve} with an optional flag to enable or disable the {Approval} event.\n By default (when calling {_approve}) the flag is set to true. On the other hand, approval changes made by\n `_spendAllowance` during the `transferFrom` operation set the flag to false. This saves gas by not emitting any\n `Approval` event during `transferFrom` operations.\n Anyone who wishes to continue emitting `Approval` events on the`transferFrom` operation can force the flag to\n true using the following override:\n ```solidity\n function _approve(address owner, address spender, uint256 value, bool) internal virtual override {\n     super._approve(owner, spender, value, true);\n }\n ```\n Requirements are the same as {_approve}."}, "id": 750, "implemented": true, "kind": "function", "modifiers": [], "name": "_approve", "nameLocation": "9614:8:2", "nodeType": "FunctionDefinition", "parameters": {"id": 700, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 693, "mutability": "mutable", "name": "owner", "nameLocation": "9631:5:2", "nodeType": "VariableDeclaration", "scope": 750, "src": "9623:13:2", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 692, "name": "address", "nodeType": "ElementaryTypeName", "src": "9623:7:2", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 695, "mutability": "mutable", "name": "spender", "nameLocation": "9646:7:2", "nodeType": "VariableDeclaration", "scope": 750, "src": "9638:15:2", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 694, "name": "address", "nodeType": "ElementaryTypeName", "src": "9638:7:2", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 697, "mutability": "mutable", "name": "value", "nameLocation": "9663:5:2", "nodeType": "VariableDeclaration", "scope": 750, "src": "9655:13:2", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 696, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "9655:7:2", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 699, "mutability": "mutable", "name": "emitEvent", "nameLocation": "9675:9:2", "nodeType": "VariableDeclaration", "scope": 750, "src": "9670:14:2", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 698, "name": "bool", "nodeType": "ElementaryTypeName", "src": "9670:4:2", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "9622:63:2"}, "returnParameters": {"id": 701, "nodeType": "ParameterList", "parameters": [], "src": "9703:0:2"}, "scope": 799, "src": "9605:432:2", "stateMutability": "nonpayable", "virtual": true, "visibility": "internal"}, {"body": {"id": 797, "nodeType": "Block", "src": "10408:387:2", "statements": [{"assignments": [761], "declarations": [{"constant": false, "id": 761, "mutability": "mutable", "name": "currentAllowance", "nameLocation": "10426:16:2", "nodeType": "VariableDeclaration", "scope": 797, "src": "10418:24:2", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 760, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "10418:7:2", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 766, "initialValue": {"arguments": [{"id": 763, "name": "owner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 753, "src": "10455:5:2", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 764, "name": "spender", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 755, "src": "10462:7:2", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_address", "typeString": "address"}], "id": 762, "name": "allowance", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 426, "src": "10445:9:2", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$_t_address_$_t_address_$returns$_t_uint256_$", "typeString": "function (address,address) view returns (uint256)"}}, "id": 765, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "10445:25:2", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "VariableDeclarationStatement", "src": "10418:52:2"}, {"condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 773, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 767, "name": "currentAllowance", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 761, "src": "10484:16:2", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<", "rightExpression": {"expression": {"arguments": [{"id": 770, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "10508:7:2", "typeDescriptions": {"typeIdentifier": "t_type$_t_uint256_$", "typeString": "type(uint256)"}, "typeName": {"id": 769, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "10508:7:2", "typeDescriptions": {}}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_type$_t_uint256_$", "typeString": "type(uint256)"}], "id": 768, "name": "type", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -27, "src": "10503:4:2", "typeDescriptions": {"typeIdentifier": "t_function_metatype_pure$__$returns$__$", "typeString": "function () pure"}}, "id": 771, "isConstant": false, "isLValue": false, "isPure": true, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "10503:13:2", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_magic_meta_type_t_uint256", "typeString": "type(uint256)"}}, "id": 772, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "10517:3:2", "memberName": "max", "nodeType": "MemberAccess", "src": "10503:17:2", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "10484:36:2", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 796, "nodeType": "IfStatement", "src": "10480:309:2", "trueBody": {"id": 795, "nodeType": "Block", "src": "10522:267:2", "statements": [{"condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 776, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 774, "name": "currentAllowance", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 761, "src": "10540:16:2", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<", "rightExpression": {"id": 775, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 757, "src": "10559:5:2", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "10540:24:2", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 784, "nodeType": "IfStatement", "src": "10536:130:2", "trueBody": {"id": 783, "nodeType": "Block", "src": "10566:100:2", "statements": [{"errorCall": {"arguments": [{"id": 778, "name": "spender", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 755, "src": "10618:7:2", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 779, "name": "currentAllowance", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 761, "src": "10627:16:2", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"id": 780, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 757, "src": "10645:5:2", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 777, "name": "ERC20InsufficientAllowance", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 178, "src": "10591:26:2", "typeDescriptions": {"typeIdentifier": "t_function_error_pure$_t_address_$_t_uint256_$_t_uint256_$returns$__$", "typeString": "function (address,uint256,uint256) pure"}}, "id": 781, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "10591:60:2", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 782, "nodeType": "RevertStatement", "src": "10584:67:2"}]}}, {"id": 794, "nodeType": "UncheckedBlock", "src": "10679:100:2", "statements": [{"expression": {"arguments": [{"id": 786, "name": "owner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 753, "src": "10716:5:2", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 787, "name": "spender", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 755, "src": "10723:7:2", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 790, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 788, "name": "currentAllowance", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 761, "src": "10732:16:2", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "-", "rightExpression": {"id": 789, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 757, "src": "10751:5:2", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "10732:24:2", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"hexValue": "66616c7365", "id": 791, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "10758:5:2", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "false"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_bool", "typeString": "bool"}], "id": 785, "name": "_approve", "nodeType": "Identifier", "overloadedDeclarations": [690, 750], "referencedDeclaration": 750, "src": "10707:8:2", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_address_$_t_address_$_t_uint256_$_t_bool_$returns$__$", "typeString": "function (address,address,uint256,bool)"}}, "id": 792, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "10707:57:2", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 793, "nodeType": "ExpressionStatement", "src": "10707:57:2"}]}]}}]}, "documentation": {"id": 751, "nodeType": "StructuredDocumentation", "src": "10043:271:2", "text": " @dev Updates `owner`'s allowance for `spender` based on spent `value`.\n Does not update the allowance value in case of infinite allowance.\n Revert if not enough allowance is available.\n Does not emit an {Approval} event."}, "id": 798, "implemented": true, "kind": "function", "modifiers": [], "name": "_spendAllowance", "nameLocation": "10328:15:2", "nodeType": "FunctionDefinition", "parameters": {"id": 758, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 753, "mutability": "mutable", "name": "owner", "nameLocation": "10352:5:2", "nodeType": "VariableDeclaration", "scope": 798, "src": "10344:13:2", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 752, "name": "address", "nodeType": "ElementaryTypeName", "src": "10344:7:2", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 755, "mutability": "mutable", "name": "spender", "nameLocation": "10367:7:2", "nodeType": "VariableDeclaration", "scope": 798, "src": "10359:15:2", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 754, "name": "address", "nodeType": "ElementaryTypeName", "src": "10359:7:2", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 757, "mutability": "mutable", "name": "value", "nameLocation": "10384:5:2", "nodeType": "VariableDeclaration", "scope": 798, "src": "10376:13:2", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 756, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "10376:7:2", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "10343:47:2"}, "returnParameters": {"id": 759, "nodeType": "ParameterList", "parameters": [], "src": "10408:0:2"}, "scope": 799, "src": "10319:476:2", "stateMutability": "nonpayable", "virtual": true, "visibility": "internal"}], "scope": 800, "src": "1106:9691:2", "usedErrors": [159, 164, 169, 178, 183, 188], "usedEvents": [811, 820]}], "src": "105:10693:2"}, "id": 2}, "@openzeppelin/contracts/token/ERC20/IERC20.sol": {"ast": {"absolutePath": "@openzeppelin/contracts/token/ERC20/IERC20.sol", "exportedSymbols": {"IERC20": [877]}, "id": 878, "license": "MIT", "nodeType": "SourceUnit", "nodes": [{"id": 801, "literals": ["solidity", ">=", "0.4", ".16"], "nodeType": "PragmaDirective", "src": "106:25:3"}, {"abstract": false, "baseContracts": [], "canonicalName": "IERC20", "contractDependencies": [], "contractKind": "interface", "documentation": {"id": 802, "nodeType": "StructuredDocumentation", "src": "133:71:3", "text": " @dev Interface of the ERC-20 standard as defined in the ERC."}, "fullyImplemented": false, "id": 877, "linearizedBaseContracts": [877], "name": "IERC20", "nameLocation": "215:6:3", "nodeType": "ContractDefinition", "nodes": [{"anonymous": false, "documentation": {"id": 803, "nodeType": "StructuredDocumentation", "src": "228:158:3", "text": " @dev Emitted when `value` tokens are moved from one account (`from`) to\n another (`to`).\n Note that `value` may be zero."}, "eventSelector": "ddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef", "id": 811, "name": "Transfer", "nameLocation": "397:8:3", "nodeType": "EventDefinition", "parameters": {"id": 810, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 805, "indexed": true, "mutability": "mutable", "name": "from", "nameLocation": "422:4:3", "nodeType": "VariableDeclaration", "scope": 811, "src": "406:20:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 804, "name": "address", "nodeType": "ElementaryTypeName", "src": "406:7:3", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 807, "indexed": true, "mutability": "mutable", "name": "to", "nameLocation": "444:2:3", "nodeType": "VariableDeclaration", "scope": 811, "src": "428:18:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 806, "name": "address", "nodeType": "ElementaryTypeName", "src": "428:7:3", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 809, "indexed": false, "mutability": "mutable", "name": "value", "nameLocation": "456:5:3", "nodeType": "VariableDeclaration", "scope": 811, "src": "448:13:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 808, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "448:7:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "405:57:3"}, "src": "391:72:3"}, {"anonymous": false, "documentation": {"id": 812, "nodeType": "StructuredDocumentation", "src": "469:148:3", "text": " @dev Emitted when the allowance of a `spender` for an `owner` is set by\n a call to {approve}. `value` is the new allowance."}, "eventSelector": "8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b925", "id": 820, "name": "Approval", "nameLocation": "628:8:3", "nodeType": "EventDefinition", "parameters": {"id": 819, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 814, "indexed": true, "mutability": "mutable", "name": "owner", "nameLocation": "653:5:3", "nodeType": "VariableDeclaration", "scope": 820, "src": "637:21:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 813, "name": "address", "nodeType": "ElementaryTypeName", "src": "637:7:3", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 816, "indexed": true, "mutability": "mutable", "name": "spender", "nameLocation": "676:7:3", "nodeType": "VariableDeclaration", "scope": 820, "src": "660:23:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 815, "name": "address", "nodeType": "ElementaryTypeName", "src": "660:7:3", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 818, "indexed": false, "mutability": "mutable", "name": "value", "nameLocation": "693:5:3", "nodeType": "VariableDeclaration", "scope": 820, "src": "685:13:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 817, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "685:7:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "636:63:3"}, "src": "622:78:3"}, {"documentation": {"id": 821, "nodeType": "StructuredDocumentation", "src": "706:65:3", "text": " @dev Returns the value of tokens in existence."}, "functionSelector": "18160ddd", "id": 826, "implemented": false, "kind": "function", "modifiers": [], "name": "totalSupply", "nameLocation": "785:11:3", "nodeType": "FunctionDefinition", "parameters": {"id": 822, "nodeType": "ParameterList", "parameters": [], "src": "796:2:3"}, "returnParameters": {"id": 825, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 824, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 826, "src": "822:7:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 823, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "822:7:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "821:9:3"}, "scope": 877, "src": "776:55:3", "stateMutability": "view", "virtual": false, "visibility": "external"}, {"documentation": {"id": 827, "nodeType": "StructuredDocumentation", "src": "837:71:3", "text": " @dev Returns the value of tokens owned by `account`."}, "functionSelector": "70a08231", "id": 834, "implemented": false, "kind": "function", "modifiers": [], "name": "balanceOf", "nameLocation": "922:9:3", "nodeType": "FunctionDefinition", "parameters": {"id": 830, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 829, "mutability": "mutable", "name": "account", "nameLocation": "940:7:3", "nodeType": "VariableDeclaration", "scope": 834, "src": "932:15:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 828, "name": "address", "nodeType": "ElementaryTypeName", "src": "932:7:3", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "931:17:3"}, "returnParameters": {"id": 833, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 832, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 834, "src": "972:7:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 831, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "972:7:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "971:9:3"}, "scope": 877, "src": "913:68:3", "stateMutability": "view", "virtual": false, "visibility": "external"}, {"documentation": {"id": 835, "nodeType": "StructuredDocumentation", "src": "987:213:3", "text": " @dev Moves a `value` amount of tokens from the caller's account to `to`.\n Returns a boolean value indicating whether the operation succeeded.\n Emits a {Transfer} event."}, "functionSelector": "a9059cbb", "id": 844, "implemented": false, "kind": "function", "modifiers": [], "name": "transfer", "nameLocation": "1214:8:3", "nodeType": "FunctionDefinition", "parameters": {"id": 840, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 837, "mutability": "mutable", "name": "to", "nameLocation": "1231:2:3", "nodeType": "VariableDeclaration", "scope": 844, "src": "1223:10:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 836, "name": "address", "nodeType": "ElementaryTypeName", "src": "1223:7:3", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 839, "mutability": "mutable", "name": "value", "nameLocation": "1243:5:3", "nodeType": "VariableDeclaration", "scope": 844, "src": "1235:13:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 838, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1235:7:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "1222:27:3"}, "returnParameters": {"id": 843, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 842, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 844, "src": "1268:4:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 841, "name": "bool", "nodeType": "ElementaryTypeName", "src": "1268:4:3", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "1267:6:3"}, "scope": 877, "src": "1205:69:3", "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}, {"documentation": {"id": 845, "nodeType": "StructuredDocumentation", "src": "1280:264:3", "text": " @dev Returns the remaining number of tokens that `spender` will be\n allowed to spend on behalf of `owner` through {transferFrom}. This is\n zero by default.\n This value changes when {approve} or {transferFrom} are called."}, "functionSelector": "dd62ed3e", "id": 854, "implemented": false, "kind": "function", "modifiers": [], "name": "allowance", "nameLocation": "1558:9:3", "nodeType": "FunctionDefinition", "parameters": {"id": 850, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 847, "mutability": "mutable", "name": "owner", "nameLocation": "1576:5:3", "nodeType": "VariableDeclaration", "scope": 854, "src": "1568:13:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 846, "name": "address", "nodeType": "ElementaryTypeName", "src": "1568:7:3", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 849, "mutability": "mutable", "name": "spender", "nameLocation": "1591:7:3", "nodeType": "VariableDeclaration", "scope": 854, "src": "1583:15:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 848, "name": "address", "nodeType": "ElementaryTypeName", "src": "1583:7:3", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "1567:32:3"}, "returnParameters": {"id": 853, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 852, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 854, "src": "1623:7:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 851, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1623:7:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "1622:9:3"}, "scope": 877, "src": "1549:83:3", "stateMutability": "view", "virtual": false, "visibility": "external"}, {"documentation": {"id": 855, "nodeType": "StructuredDocumentation", "src": "1638:667:3", "text": " @dev Sets a `value` amount of tokens as the allowance of `spender` over the\n caller's tokens.\n Returns a boolean value indicating whether the operation succeeded.\n IMPORTANT: Beware that changing an allowance with this method brings the risk\n that someone may use both the old and the new allowance by unfortunate\n transaction ordering. One possible solution to mitigate this race\n condition is to first reduce the spender's allowance to 0 and set the\n desired value afterwards:\n https://github.com/ethereum/EIPs/issues/20#issuecomment-*********\n Emits an {Approval} event."}, "functionSelector": "095ea7b3", "id": 864, "implemented": false, "kind": "function", "modifiers": [], "name": "approve", "nameLocation": "2319:7:3", "nodeType": "FunctionDefinition", "parameters": {"id": 860, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 857, "mutability": "mutable", "name": "spender", "nameLocation": "2335:7:3", "nodeType": "VariableDeclaration", "scope": 864, "src": "2327:15:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 856, "name": "address", "nodeType": "ElementaryTypeName", "src": "2327:7:3", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 859, "mutability": "mutable", "name": "value", "nameLocation": "2352:5:3", "nodeType": "VariableDeclaration", "scope": 864, "src": "2344:13:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 858, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2344:7:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "2326:32:3"}, "returnParameters": {"id": 863, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 862, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 864, "src": "2377:4:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 861, "name": "bool", "nodeType": "ElementaryTypeName", "src": "2377:4:3", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "2376:6:3"}, "scope": 877, "src": "2310:73:3", "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}, {"documentation": {"id": 865, "nodeType": "StructuredDocumentation", "src": "2389:297:3", "text": " @dev Moves a `value` amount of tokens from `from` to `to` using the\n allowance mechanism. `value` is then deducted from the caller's\n allowance.\n Returns a boolean value indicating whether the operation succeeded.\n Emits a {Transfer} event."}, "functionSelector": "23b872dd", "id": 876, "implemented": false, "kind": "function", "modifiers": [], "name": "transferFrom", "nameLocation": "2700:12:3", "nodeType": "FunctionDefinition", "parameters": {"id": 872, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 867, "mutability": "mutable", "name": "from", "nameLocation": "2721:4:3", "nodeType": "VariableDeclaration", "scope": 876, "src": "2713:12:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 866, "name": "address", "nodeType": "ElementaryTypeName", "src": "2713:7:3", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 869, "mutability": "mutable", "name": "to", "nameLocation": "2735:2:3", "nodeType": "VariableDeclaration", "scope": 876, "src": "2727:10:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 868, "name": "address", "nodeType": "ElementaryTypeName", "src": "2727:7:3", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 871, "mutability": "mutable", "name": "value", "nameLocation": "2747:5:3", "nodeType": "VariableDeclaration", "scope": 876, "src": "2739:13:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 870, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2739:7:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "2712:41:3"}, "returnParameters": {"id": 875, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 874, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 876, "src": "2772:4:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 873, "name": "bool", "nodeType": "ElementaryTypeName", "src": "2772:4:3", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "2771:6:3"}, "scope": 877, "src": "2691:87:3", "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}], "scope": 878, "src": "205:2575:3", "usedErrors": [], "usedEvents": [811, 820]}], "src": "106:2675:3"}, "id": 3}, "@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol": {"ast": {"absolutePath": "@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol", "exportedSymbols": {"IERC20": [877], "IERC20Metadata": [903]}, "id": 904, "license": "MIT", "nodeType": "SourceUnit", "nodes": [{"id": 879, "literals": ["solidity", ">=", "0.6", ".2"], "nodeType": "PragmaDirective", "src": "125:24:4"}, {"absolutePath": "@openzeppelin/contracts/token/ERC20/IERC20.sol", "file": "../IERC20.sol", "id": 881, "nameLocation": "-1:-1:-1", "nodeType": "ImportDirective", "scope": 904, "sourceUnit": 878, "src": "151:37:4", "symbolAliases": [{"foreign": {"id": 880, "name": "IERC20", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 877, "src": "159:6:4", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"abstract": false, "baseContracts": [{"baseName": {"id": 883, "name": "IERC20", "nameLocations": ["306:6:4"], "nodeType": "IdentifierPath", "referencedDeclaration": 877, "src": "306:6:4"}, "id": 884, "nodeType": "InheritanceSpecifier", "src": "306:6:4"}], "canonicalName": "IERC20Metadata", "contractDependencies": [], "contractKind": "interface", "documentation": {"id": 882, "nodeType": "StructuredDocumentation", "src": "190:87:4", "text": " @dev Interface for the optional metadata functions from the ERC-20 standard."}, "fullyImplemented": false, "id": 903, "linearizedBaseContracts": [903, 877], "name": "IERC20Metadata", "nameLocation": "288:14:4", "nodeType": "ContractDefinition", "nodes": [{"documentation": {"id": 885, "nodeType": "StructuredDocumentation", "src": "319:54:4", "text": " @dev Returns the name of the token."}, "functionSelector": "06fdde03", "id": 890, "implemented": false, "kind": "function", "modifiers": [], "name": "name", "nameLocation": "387:4:4", "nodeType": "FunctionDefinition", "parameters": {"id": 886, "nodeType": "ParameterList", "parameters": [], "src": "391:2:4"}, "returnParameters": {"id": 889, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 888, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 890, "src": "417:13:4", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 887, "name": "string", "nodeType": "ElementaryTypeName", "src": "417:6:4", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "416:15:4"}, "scope": 903, "src": "378:54:4", "stateMutability": "view", "virtual": false, "visibility": "external"}, {"documentation": {"id": 891, "nodeType": "StructuredDocumentation", "src": "438:56:4", "text": " @dev Returns the symbol of the token."}, "functionSelector": "95d89b41", "id": 896, "implemented": false, "kind": "function", "modifiers": [], "name": "symbol", "nameLocation": "508:6:4", "nodeType": "FunctionDefinition", "parameters": {"id": 892, "nodeType": "ParameterList", "parameters": [], "src": "514:2:4"}, "returnParameters": {"id": 895, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 894, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 896, "src": "540:13:4", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 893, "name": "string", "nodeType": "ElementaryTypeName", "src": "540:6:4", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "539:15:4"}, "scope": 903, "src": "499:56:4", "stateMutability": "view", "virtual": false, "visibility": "external"}, {"documentation": {"id": 897, "nodeType": "StructuredDocumentation", "src": "561:65:4", "text": " @dev Returns the decimals places of the token."}, "functionSelector": "313ce567", "id": 902, "implemented": false, "kind": "function", "modifiers": [], "name": "decimals", "nameLocation": "640:8:4", "nodeType": "FunctionDefinition", "parameters": {"id": 898, "nodeType": "ParameterList", "parameters": [], "src": "648:2:4"}, "returnParameters": {"id": 901, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 900, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 902, "src": "674:5:4", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}, "typeName": {"id": 899, "name": "uint8", "nodeType": "ElementaryTypeName", "src": "674:5:4", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}}, "visibility": "internal"}], "src": "673:7:4"}, "scope": 903, "src": "631:50:4", "stateMutability": "view", "virtual": false, "visibility": "external"}], "scope": 904, "src": "278:405:4", "usedErrors": [], "usedEvents": [811, 820]}], "src": "125:559:4"}, "id": 4}, "@openzeppelin/contracts/utils/Context.sol": {"ast": {"absolutePath": "@openzeppelin/contracts/utils/Context.sol", "exportedSymbols": {"Context": [933]}, "id": 934, "license": "MIT", "nodeType": "SourceUnit", "nodes": [{"id": 905, "literals": ["solidity", "^", "0.8", ".20"], "nodeType": "PragmaDirective", "src": "101:24:5"}, {"abstract": true, "baseContracts": [], "canonicalName": "Context", "contractDependencies": [], "contractKind": "contract", "documentation": {"id": 906, "nodeType": "StructuredDocumentation", "src": "127:496:5", "text": " @dev Provides information about the current execution context, including the\n sender of the transaction and its data. While these are generally available\n via msg.sender and msg.data, they should not be accessed in such a direct\n manner, since when dealing with meta-transactions the account sending and\n paying for execution may not be the actual sender (as far as an application\n is concerned).\n This contract is only required for intermediate, library-like contracts."}, "fullyImplemented": true, "id": 933, "linearizedBaseContracts": [933], "name": "Context", "nameLocation": "642:7:5", "nodeType": "ContractDefinition", "nodes": [{"body": {"id": 914, "nodeType": "Block", "src": "718:34:5", "statements": [{"expression": {"expression": {"id": 911, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "735:3:5", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 912, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "739:6:5", "memberName": "sender", "nodeType": "MemberAccess", "src": "735:10:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "functionReturnParameters": 910, "id": 913, "nodeType": "Return", "src": "728:17:5"}]}, "id": 915, "implemented": true, "kind": "function", "modifiers": [], "name": "_msgSender", "nameLocation": "665:10:5", "nodeType": "FunctionDefinition", "parameters": {"id": 907, "nodeType": "ParameterList", "parameters": [], "src": "675:2:5"}, "returnParameters": {"id": 910, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 909, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 915, "src": "709:7:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 908, "name": "address", "nodeType": "ElementaryTypeName", "src": "709:7:5", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "708:9:5"}, "scope": 933, "src": "656:96:5", "stateMutability": "view", "virtual": true, "visibility": "internal"}, {"body": {"id": 923, "nodeType": "Block", "src": "825:32:5", "statements": [{"expression": {"expression": {"id": 920, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "842:3:5", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 921, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "846:4:5", "memberName": "data", "nodeType": "MemberAccess", "src": "842:8:5", "typeDescriptions": {"typeIdentifier": "t_bytes_calldata_ptr", "typeString": "bytes calldata"}}, "functionReturnParameters": 919, "id": 922, "nodeType": "Return", "src": "835:15:5"}]}, "id": 924, "implemented": true, "kind": "function", "modifiers": [], "name": "_msgData", "nameLocation": "767:8:5", "nodeType": "FunctionDefinition", "parameters": {"id": 916, "nodeType": "ParameterList", "parameters": [], "src": "775:2:5"}, "returnParameters": {"id": 919, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 918, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 924, "src": "809:14:5", "stateVariable": false, "storageLocation": "calldata", "typeDescriptions": {"typeIdentifier": "t_bytes_calldata_ptr", "typeString": "bytes"}, "typeName": {"id": 917, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "809:5:5", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "visibility": "internal"}], "src": "808:16:5"}, "scope": 933, "src": "758:99:5", "stateMutability": "view", "virtual": true, "visibility": "internal"}, {"body": {"id": 931, "nodeType": "Block", "src": "935:25:5", "statements": [{"expression": {"hexValue": "30", "id": 929, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "952:1:5", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "functionReturnParameters": 928, "id": 930, "nodeType": "Return", "src": "945:8:5"}]}, "id": 932, "implemented": true, "kind": "function", "modifiers": [], "name": "_contextSuffixLength", "nameLocation": "872:20:5", "nodeType": "FunctionDefinition", "parameters": {"id": 925, "nodeType": "ParameterList", "parameters": [], "src": "892:2:5"}, "returnParameters": {"id": 928, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 927, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 932, "src": "926:7:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 926, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "926:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "925:9:5"}, "scope": 933, "src": "863:97:5", "stateMutability": "view", "virtual": true, "visibility": "internal"}], "scope": 934, "src": "624:338:5", "usedErrors": [], "usedEvents": []}], "src": "101:862:5"}, "id": 5}, "contracts/tokens/SimpleToken.sol": {"ast": {"absolutePath": "contracts/tokens/SimpleToken.sol", "exportedSymbols": {"Context": [933], "ERC20": [799], "IERC20": [877], "IERC20Errors": [189], "IERC20Metadata": [903], "Ownable": [147], "SimpleToken": [1018]}, "id": 1019, "license": "MIT", "nodeType": "SourceUnit", "nodes": [{"id": 935, "literals": ["solidity", "^", "0.8", ".24"], "nodeType": "PragmaDirective", "src": "32:24:6"}, {"absolutePath": "@openzeppelin/contracts/token/ERC20/ERC20.sol", "file": "@openzeppelin/contracts/token/ERC20/ERC20.sol", "id": 936, "nameLocation": "-1:-1:-1", "nodeType": "ImportDirective", "scope": 1019, "sourceUnit": 800, "src": "58:55:6", "symbolAliases": [], "unitAlias": ""}, {"absolutePath": "@openzeppelin/contracts/access/Ownable.sol", "file": "@openzeppelin/contracts/access/Ownable.sol", "id": 937, "nameLocation": "-1:-1:-1", "nodeType": "ImportDirective", "scope": 1019, "sourceUnit": 148, "src": "114:52:6", "symbolAliases": [], "unitAlias": ""}, {"abstract": false, "baseContracts": [{"baseName": {"id": 939, "name": "ERC20", "nameLocations": ["260:5:6"], "nodeType": "IdentifierPath", "referencedDeclaration": 799, "src": "260:5:6"}, "id": 940, "nodeType": "InheritanceSpecifier", "src": "260:5:6"}, {"baseName": {"id": 941, "name": "Ownable", "nameLocations": ["267:7:6"], "nodeType": "IdentifierPath", "referencedDeclaration": 147, "src": "267:7:6"}, "id": 942, "nodeType": "InheritanceSpecifier", "src": "267:7:6"}], "canonicalName": "SimpleToken", "contractDependencies": [], "contractKind": "contract", "documentation": {"id": 938, "nodeType": "StructuredDocumentation", "src": "168:67:6", "text": " @title SimpleToken\n @dev 简化的 ERC-20 代币实现"}, "fullyImplemented": true, "id": 1018, "linearizedBaseContracts": [1018, 147, 799, 189, 903, 877, 933], "name": "SimpleToken", "nameLocation": "245:11:6", "nodeType": "ContractDefinition", "nodes": [{"constant": true, "functionSelector": "32cb6b0c", "id": 949, "mutability": "constant", "name": "MAX_SUPPLY", "nameLocation": "310:10:6", "nodeType": "VariableDeclaration", "scope": 1018, "src": "286:57:6", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 943, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "286:7:6", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": {"commonType": {"typeIdentifier": "t_rational_100000000000000000000000000_by_1", "typeString": "int_const 100000000000000000000000000"}, "id": 948, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "leftExpression": {"hexValue": "3130305f3030305f303030", "id": 944, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "323:11:6", "typeDescriptions": {"typeIdentifier": "t_rational_100000000_by_1", "typeString": "int_const 100000000"}, "value": "100_000_000"}, "nodeType": "BinaryOperation", "operator": "*", "rightExpression": {"commonType": {"typeIdentifier": "t_rational_1000000000000000000_by_1", "typeString": "int_const 1000000000000000000"}, "id": 947, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "leftExpression": {"hexValue": "3130", "id": 945, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "337:2:6", "typeDescriptions": {"typeIdentifier": "t_rational_10_by_1", "typeString": "int_const 10"}, "value": "10"}, "nodeType": "BinaryOperation", "operator": "**", "rightExpression": {"hexValue": "3138", "id": 946, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "341:2:6", "typeDescriptions": {"typeIdentifier": "t_rational_18_by_1", "typeString": "int_const 18"}, "value": "18"}, "src": "337:6:6", "typeDescriptions": {"typeIdentifier": "t_rational_1000000000000000000_by_1", "typeString": "int_const 1000000000000000000"}}, "src": "323:20:6", "typeDescriptions": {"typeIdentifier": "t_rational_100000000000000000000000000_by_1", "typeString": "int_const 100000000000000000000000000"}}, "visibility": "public"}, {"body": {"id": 979, "nodeType": "Block", "src": "555:134:6", "statements": [{"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 970, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 968, "name": "initialSupply", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 955, "src": "573:13:6", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<=", "rightExpression": {"id": 969, "name": "MAX_SUPPLY", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 949, "src": "590:10:6", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "573:27:6", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"hexValue": "496e697469616c20737570706c792065786365656473206d617820737570706c79", "id": 971, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "602:35:6", "typeDescriptions": {"typeIdentifier": "t_stringliteral_d2b4003947a28bea510e1f497fab95ff4a8fce8b3d30c35cd98d1fb7fcb013e6", "typeString": "literal_string \"Initial supply exceeds max supply\""}, "value": "Initial supply exceeds max supply"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_d2b4003947a28bea510e1f497fab95ff4a8fce8b3d30c35cd98d1fb7fcb013e6", "typeString": "literal_string \"Initial supply exceeds max supply\""}], "id": 967, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [-18, -18], "referencedDeclaration": -18, "src": "565:7:6", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 972, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "565:73:6", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 973, "nodeType": "ExpressionStatement", "src": "565:73:6"}, {"expression": {"arguments": [{"id": 975, "name": "initialOwner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 957, "src": "654:12:6", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 976, "name": "initialSupply", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 955, "src": "668:13:6", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 974, "name": "_mint", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 639, "src": "648:5:6", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_address_$_t_uint256_$returns$__$", "typeString": "function (address,uint256)"}}, "id": 977, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "648:34:6", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 978, "nodeType": "ExpressionStatement", "src": "648:34:6"}]}, "id": 980, "implemented": true, "kind": "constructor", "modifiers": [{"arguments": [{"id": 960, "name": "name", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 951, "src": "506:4:6", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 961, "name": "symbol", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 953, "src": "512:6:6", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "id": 962, "kind": "baseConstructorSpecifier", "modifierName": {"id": 959, "name": "ERC20", "nameLocations": ["500:5:6"], "nodeType": "IdentifierPath", "referencedDeclaration": 799, "src": "500:5:6"}, "nodeType": "ModifierInvocation", "src": "500:19:6"}, {"arguments": [{"id": 964, "name": "initialOwner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 957, "src": "537:12:6", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "id": 965, "kind": "baseConstructorSpecifier", "modifierName": {"id": 963, "name": "Ownable", "nameLocations": ["529:7:6"], "nodeType": "IdentifierPath", "referencedDeclaration": 147, "src": "529:7:6"}, "nodeType": "ModifierInvocation", "src": "529:21:6"}], "name": "", "nameLocation": "-1:-1:-1", "nodeType": "FunctionDefinition", "parameters": {"id": 958, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 951, "mutability": "mutable", "name": "name", "nameLocation": "389:4:6", "nodeType": "VariableDeclaration", "scope": 980, "src": "375:18:6", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 950, "name": "string", "nodeType": "ElementaryTypeName", "src": "375:6:6", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 953, "mutability": "mutable", "name": "symbol", "nameLocation": "417:6:6", "nodeType": "VariableDeclaration", "scope": 980, "src": "403:20:6", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 952, "name": "string", "nodeType": "ElementaryTypeName", "src": "403:6:6", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 955, "mutability": "mutable", "name": "initialSupply", "nameLocation": "441:13:6", "nodeType": "VariableDeclaration", "scope": 980, "src": "433:21:6", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 954, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "433:7:6", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 957, "mutability": "mutable", "name": "initialOwner", "nameLocation": "472:12:6", "nodeType": "VariableDeclaration", "scope": 980, "src": "464:20:6", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 956, "name": "address", "nodeType": "ElementaryTypeName", "src": "464:7:6", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "365:125:6"}, "returnParameters": {"id": 966, "nodeType": "ParameterList", "parameters": [], "src": "555:0:6"}, "scope": 1018, "src": "354:335:6", "stateMutability": "nonpayable", "virtual": false, "visibility": "public"}, {"body": {"id": 1004, "nodeType": "Block", "src": "758:111:6", "statements": [{"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 995, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 993, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"arguments": [], "expression": {"argumentTypes": [], "id": 990, "name": "totalSupply", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 372, "src": "776:11:6", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$__$returns$_t_uint256_$", "typeString": "function () view returns (uint256)"}}, "id": 991, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "776:13:6", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "+", "rightExpression": {"id": 992, "name": "amount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 984, "src": "792:6:6", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "776:22:6", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<=", "rightExpression": {"id": 994, "name": "MAX_SUPPLY", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 949, "src": "802:10:6", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "776:36:6", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"hexValue": "45786365656473206d617820737570706c79", "id": 996, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "814:20:6", "typeDescriptions": {"typeIdentifier": "t_stringliteral_833ac5256bd749e59e09b3d79e65697a9a895b45768ee0e3858ea9958c2aa611", "typeString": "literal_string \"Exceeds max supply\""}, "value": "Exceeds max supply"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_833ac5256bd749e59e09b3d79e65697a9a895b45768ee0e3858ea9958c2aa611", "typeString": "literal_string \"Exceeds max supply\""}], "id": 989, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [-18, -18], "referencedDeclaration": -18, "src": "768:7:6", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 997, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "768:67:6", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 998, "nodeType": "ExpressionStatement", "src": "768:67:6"}, {"expression": {"arguments": [{"id": 1000, "name": "to", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 982, "src": "851:2:6", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 1001, "name": "amount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 984, "src": "855:6:6", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 999, "name": "_mint", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 639, "src": "845:5:6", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_address_$_t_uint256_$returns$__$", "typeString": "function (address,uint256)"}}, "id": 1002, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "845:17:6", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 1003, "nodeType": "ExpressionStatement", "src": "845:17:6"}]}, "functionSelector": "40c10f19", "id": 1005, "implemented": true, "kind": "function", "modifiers": [{"id": 987, "kind": "modifierInvocation", "modifierName": {"id": 986, "name": "only<PERSON><PERSON>er", "nameLocations": ["748:9:6"], "nodeType": "IdentifierPath", "referencedDeclaration": 58, "src": "748:9:6"}, "nodeType": "ModifierInvocation", "src": "748:9:6"}], "name": "mint", "nameLocation": "708:4:6", "nodeType": "FunctionDefinition", "parameters": {"id": 985, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 982, "mutability": "mutable", "name": "to", "nameLocation": "721:2:6", "nodeType": "VariableDeclaration", "scope": 1005, "src": "713:10:6", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 981, "name": "address", "nodeType": "ElementaryTypeName", "src": "713:7:6", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 984, "mutability": "mutable", "name": "amount", "nameLocation": "733:6:6", "nodeType": "VariableDeclaration", "scope": 1005, "src": "725:14:6", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 983, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "725:7:6", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "712:28:6"}, "returnParameters": {"id": 988, "nodeType": "ParameterList", "parameters": [], "src": "758:0:6"}, "scope": 1018, "src": "699:170:6", "stateMutability": "nonpayable", "virtual": false, "visibility": "public"}, {"body": {"id": 1016, "nodeType": "Block", "src": "916:42:6", "statements": [{"expression": {"arguments": [{"expression": {"id": 1011, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "932:3:6", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 1012, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "936:6:6", "memberName": "sender", "nodeType": "MemberAccess", "src": "932:10:6", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 1013, "name": "amount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1007, "src": "944:6:6", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 1010, "name": "_burn", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 672, "src": "926:5:6", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_address_$_t_uint256_$returns$__$", "typeString": "function (address,uint256)"}}, "id": 1014, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "926:25:6", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 1015, "nodeType": "ExpressionStatement", "src": "926:25:6"}]}, "functionSelector": "42966c68", "id": 1017, "implemented": true, "kind": "function", "modifiers": [], "name": "burn", "nameLocation": "888:4:6", "nodeType": "FunctionDefinition", "parameters": {"id": 1008, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 1007, "mutability": "mutable", "name": "amount", "nameLocation": "901:6:6", "nodeType": "VariableDeclaration", "scope": 1017, "src": "893:14:6", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 1006, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "893:7:6", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "892:16:6"}, "returnParameters": {"id": 1009, "nodeType": "ParameterList", "parameters": [], "src": "916:0:6"}, "scope": 1018, "src": "879:79:6", "stateMutability": "nonpayable", "virtual": false, "visibility": "public"}], "scope": 1019, "src": "236:724:6", "usedErrors": [13, 18, 159, 164, 169, 178, 183, 188], "usedEvents": [24, 811, 820]}], "src": "32:929:6"}, "id": 6}}, "contracts": {"@openzeppelin/contracts/access/Ownable.sol": {"Ownable": {"abi": [{"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "OwnableInvalidOwner", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "OwnableUnauthorizedAccount", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "evm": {"bytecode": {"functionDebugData": {}, "generatedSources": [], "linkReferences": {}, "object": "", "opcodes": "", "sourceMap": ""}, "deployedBytecode": {"functionDebugData": {}, "generatedSources": [], "immutableReferences": {}, "linkReferences": {}, "object": "", "opcodes": "", "sourceMap": ""}, "methodIdentifiers": {"owner()": "8da5cb5b", "renounceOwnership()": "715018a6", "transferOwnership(address)": "f2fde38b"}}, "metadata": "{\"compiler\":{\"version\":\"0.8.24+commit.e11b9ed9\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"name\":\"OwnableInvalidOwner\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"OwnableUnauthorizedAccount\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"previousOwner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"OwnershipTransferred\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"owner\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"renounceOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"transferOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"details\":\"Contract module which provides a basic access control mechanism, where there is an account (an owner) that can be granted exclusive access to specific functions. The initial owner is set to the address provided by the deployer. This can later be changed with {transferOwnership}. This module is used through inheritance. It will make available the modifier `onlyOwner`, which can be applied to your functions to restrict their use to the owner.\",\"errors\":{\"OwnableInvalidOwner(address)\":[{\"details\":\"The owner is not a valid owner account. (eg. `address(0)`)\"}],\"OwnableUnauthorizedAccount(address)\":[{\"details\":\"The caller account is not authorized to perform an operation.\"}]},\"kind\":\"dev\",\"methods\":{\"constructor\":{\"details\":\"Initializes the contract setting the address provided by the deployer as the initial owner.\"},\"owner()\":{\"details\":\"Returns the address of the current owner.\"},\"renounceOwnership()\":{\"details\":\"Leaves the contract without owner. It will not be possible to call `onlyOwner` functions. Can only be called by the current owner. NOTE: Renouncing ownership will leave the contract without an owner, thereby disabling any functionality that is only available to the owner.\"},\"transferOwnership(address)\":{\"details\":\"Transfers ownership of the contract to a new account (`newOwner`). Can only be called by the current owner.\"}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"@openzeppelin/contracts/access/Ownable.sol\":\"Ownable\"},\"evmVersion\":\"paris\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[],\"viaIR\":true},\"sources\":{\"@openzeppelin/contracts/access/Ownable.sol\":{\"keccak256\":\"0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8ed324d3920bb545059d66ab97d43e43ee85fd3bd52e03e401f020afb0b120f6\",\"dweb:/ipfs/QmfEckWLmZkDDcoWrkEvMWhms66xwTLff9DDhegYpvHo1a\"]},\"@openzeppelin/contracts/utils/Context.sol\":{\"keccak256\":\"0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12\",\"dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF\"]}},\"version\":1}"}}, "@openzeppelin/contracts/interfaces/draft-IERC6093.sol": {"IERC1155Errors": {"abi": [{"inputs": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "uint256", "name": "balance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}, {"internalType": "uint256", "name": "tokenId", "type": "uint256"}], "name": "ERC1155InsufficientBalance", "type": "error"}, {"inputs": [{"internalType": "address", "name": "approver", "type": "address"}], "name": "ERC1155InvalidApprover", "type": "error"}, {"inputs": [{"internalType": "uint256", "name": "idsLength", "type": "uint256"}, {"internalType": "uint256", "name": "valuesLength", "type": "uint256"}], "name": "ERC1155InvalidArrayLength", "type": "error"}, {"inputs": [{"internalType": "address", "name": "operator", "type": "address"}], "name": "ERC1155InvalidOperator", "type": "error"}, {"inputs": [{"internalType": "address", "name": "receiver", "type": "address"}], "name": "ERC1155InvalidReceiver", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}], "name": "ERC1155InvalidSender", "type": "error"}, {"inputs": [{"internalType": "address", "name": "operator", "type": "address"}, {"internalType": "address", "name": "owner", "type": "address"}], "name": "ERC1155MissingApprovalForAll", "type": "error"}], "evm": {"bytecode": {"functionDebugData": {}, "generatedSources": [], "linkReferences": {}, "object": "", "opcodes": "", "sourceMap": ""}, "deployedBytecode": {"functionDebugData": {}, "generatedSources": [], "immutableReferences": {}, "linkReferences": {}, "object": "", "opcodes": "", "sourceMap": ""}, "methodIdentifiers": {}}, "metadata": "{\"compiler\":{\"version\":\"0.8.24+commit.e11b9ed9\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"balance\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"needed\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"tokenId\",\"type\":\"uint256\"}],\"name\":\"ERC1155InsufficientBalance\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"approver\",\"type\":\"address\"}],\"name\":\"ERC1155InvalidApprover\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"idsLength\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"valuesLength\",\"type\":\"uint256\"}],\"name\":\"ERC1155InvalidArrayLength\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"operator\",\"type\":\"address\"}],\"name\":\"ERC1155InvalidOperator\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"}],\"name\":\"ERC1155InvalidReceiver\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"}],\"name\":\"ERC1155InvalidSender\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"operator\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"name\":\"ERC1155MissingApprovalForAll\",\"type\":\"error\"}],\"devdoc\":{\"details\":\"Standard ERC-1155 Errors Interface of the https://eips.ethereum.org/EIPS/eip-6093[ERC-6093] custom errors for ERC-1155 tokens.\",\"errors\":{\"ERC1155InsufficientBalance(address,uint256,uint256,uint256)\":[{\"details\":\"Indicates an error related to the current `balance` of a `sender`. Used in transfers.\",\"params\":{\"balance\":\"Current balance for the interacting account.\",\"needed\":\"Minimum amount required to perform a transfer.\",\"sender\":\"Address whose tokens are being transferred.\",\"tokenId\":\"Identifier number of a token.\"}}],\"ERC1155InvalidApprover(address)\":[{\"details\":\"Indicates a failure with the `approver` of a token to be approved. Used in approvals.\",\"params\":{\"approver\":\"Address initiating an approval operation.\"}}],\"ERC1155InvalidArrayLength(uint256,uint256)\":[{\"details\":\"Indicates an array length mismatch between ids and values in a safeBatchTransferFrom operation. Used in batch transfers.\",\"params\":{\"idsLength\":\"Length of the array of token identifiers\",\"valuesLength\":\"Length of the array of token amounts\"}}],\"ERC1155InvalidOperator(address)\":[{\"details\":\"Indicates a failure with the `operator` to be approved. Used in approvals.\",\"params\":{\"operator\":\"Address that may be allowed to operate on tokens without being their owner.\"}}],\"ERC1155InvalidReceiver(address)\":[{\"details\":\"Indicates a failure with the token `receiver`. Used in transfers.\",\"params\":{\"receiver\":\"Address to which tokens are being transferred.\"}}],\"ERC1155InvalidSender(address)\":[{\"details\":\"Indicates a failure with the token `sender`. Used in transfers.\",\"params\":{\"sender\":\"Address whose tokens are being transferred.\"}}],\"ERC1155MissingApprovalForAll(address,address)\":[{\"details\":\"Indicates a failure with the `operator`\\u2019s approval. Used in transfers.\",\"params\":{\"operator\":\"Address that may be allowed to operate on tokens without being their owner.\",\"owner\":\"Address of the current owner of a token.\"}}]},\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"@openzeppelin/contracts/interfaces/draft-IERC6093.sol\":\"IERC1155Errors\"},\"evmVersion\":\"paris\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[],\"viaIR\":true},\"sources\":{\"@openzeppelin/contracts/interfaces/draft-IERC6093.sol\":{\"keccak256\":\"0x19fdfb0f3b89a230e7dbd1cf416f1a6b531a3ee5db4da483f946320fc74afc0e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3490d794728f5bfecb46820431adaff71ba374141545ec20b650bb60353fac23\",\"dweb:/ipfs/QmPsfxjVpMcZbpE7BH93DzTpEaktESigEw4SmDzkXuJ4WR\"]}},\"version\":1}"}, "IERC20Errors": {"abi": [{"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "allowance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "name": "ERC20InsufficientAllowance", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "uint256", "name": "balance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "name": "ERC20InsufficientBalance", "type": "error"}, {"inputs": [{"internalType": "address", "name": "approver", "type": "address"}], "name": "ERC20InvalidApprover", "type": "error"}, {"inputs": [{"internalType": "address", "name": "receiver", "type": "address"}], "name": "ERC20InvalidReceiver", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}], "name": "ERC20InvalidSender", "type": "error"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}], "name": "ERC20InvalidSpender", "type": "error"}], "evm": {"bytecode": {"functionDebugData": {}, "generatedSources": [], "linkReferences": {}, "object": "", "opcodes": "", "sourceMap": ""}, "deployedBytecode": {"functionDebugData": {}, "generatedSources": [], "immutableReferences": {}, "linkReferences": {}, "object": "", "opcodes": "", "sourceMap": ""}, "methodIdentifiers": {}}, "metadata": "{\"compiler\":{\"version\":\"0.8.24+commit.e11b9ed9\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"allowance\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"needed\",\"type\":\"uint256\"}],\"name\":\"ERC20InsufficientAllowance\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"balance\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"needed\",\"type\":\"uint256\"}],\"name\":\"ERC20InsufficientBalance\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"approver\",\"type\":\"address\"}],\"name\":\"ERC20InvalidApprover\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"}],\"name\":\"ERC20InvalidReceiver\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"}],\"name\":\"ERC20InvalidSender\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"}],\"name\":\"ERC20InvalidSpender\",\"type\":\"error\"}],\"devdoc\":{\"details\":\"Standard ERC-20 Errors Interface of the https://eips.ethereum.org/EIPS/eip-6093[ERC-6093] custom errors for ERC-20 tokens.\",\"errors\":{\"ERC20InsufficientAllowance(address,uint256,uint256)\":[{\"details\":\"Indicates a failure with the `spender`\\u2019s `allowance`. Used in transfers.\",\"params\":{\"allowance\":\"Amount of tokens a `spender` is allowed to operate with.\",\"needed\":\"Minimum amount required to perform a transfer.\",\"spender\":\"Address that may be allowed to operate on tokens without being their owner.\"}}],\"ERC20InsufficientBalance(address,uint256,uint256)\":[{\"details\":\"Indicates an error related to the current `balance` of a `sender`. Used in transfers.\",\"params\":{\"balance\":\"Current balance for the interacting account.\",\"needed\":\"Minimum amount required to perform a transfer.\",\"sender\":\"Address whose tokens are being transferred.\"}}],\"ERC20InvalidApprover(address)\":[{\"details\":\"Indicates a failure with the `approver` of a token to be approved. Used in approvals.\",\"params\":{\"approver\":\"Address initiating an approval operation.\"}}],\"ERC20InvalidReceiver(address)\":[{\"details\":\"Indicates a failure with the token `receiver`. Used in transfers.\",\"params\":{\"receiver\":\"Address to which tokens are being transferred.\"}}],\"ERC20InvalidSender(address)\":[{\"details\":\"Indicates a failure with the token `sender`. Used in transfers.\",\"params\":{\"sender\":\"Address whose tokens are being transferred.\"}}],\"ERC20InvalidSpender(address)\":[{\"details\":\"Indicates a failure with the `spender` to be approved. Used in approvals.\",\"params\":{\"spender\":\"Address that may be allowed to operate on tokens without being their owner.\"}}]},\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"@openzeppelin/contracts/interfaces/draft-IERC6093.sol\":\"IERC20Errors\"},\"evmVersion\":\"paris\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[],\"viaIR\":true},\"sources\":{\"@openzeppelin/contracts/interfaces/draft-IERC6093.sol\":{\"keccak256\":\"0x19fdfb0f3b89a230e7dbd1cf416f1a6b531a3ee5db4da483f946320fc74afc0e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3490d794728f5bfecb46820431adaff71ba374141545ec20b650bb60353fac23\",\"dweb:/ipfs/QmPsfxjVpMcZbpE7BH93DzTpEaktESigEw4SmDzkXuJ4WR\"]}},\"version\":1}"}, "IERC721Errors": {"abi": [{"inputs": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "uint256", "name": "tokenId", "type": "uint256"}, {"internalType": "address", "name": "owner", "type": "address"}], "name": "ERC721IncorrectOwner", "type": "error"}, {"inputs": [{"internalType": "address", "name": "operator", "type": "address"}, {"internalType": "uint256", "name": "tokenId", "type": "uint256"}], "name": "ERC721InsufficientApproval", "type": "error"}, {"inputs": [{"internalType": "address", "name": "approver", "type": "address"}], "name": "ERC721InvalidApprover", "type": "error"}, {"inputs": [{"internalType": "address", "name": "operator", "type": "address"}], "name": "ERC721InvalidOperator", "type": "error"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "ERC721InvalidOwner", "type": "error"}, {"inputs": [{"internalType": "address", "name": "receiver", "type": "address"}], "name": "ERC721InvalidReceiver", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}], "name": "ERC721InvalidSender", "type": "error"}, {"inputs": [{"internalType": "uint256", "name": "tokenId", "type": "uint256"}], "name": "ERC721NonexistentToken", "type": "error"}], "evm": {"bytecode": {"functionDebugData": {}, "generatedSources": [], "linkReferences": {}, "object": "", "opcodes": "", "sourceMap": ""}, "deployedBytecode": {"functionDebugData": {}, "generatedSources": [], "immutableReferences": {}, "linkReferences": {}, "object": "", "opcodes": "", "sourceMap": ""}, "methodIdentifiers": {}}, "metadata": "{\"compiler\":{\"version\":\"0.8.24+commit.e11b9ed9\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"tokenId\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"name\":\"ERC721IncorrectOwner\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"operator\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"tokenId\",\"type\":\"uint256\"}],\"name\":\"ERC721InsufficientApproval\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"approver\",\"type\":\"address\"}],\"name\":\"ERC721InvalidApprover\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"operator\",\"type\":\"address\"}],\"name\":\"ERC721InvalidOperator\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"name\":\"ERC721InvalidOwner\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"}],\"name\":\"ERC721InvalidReceiver\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"}],\"name\":\"ERC721InvalidSender\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"tokenId\",\"type\":\"uint256\"}],\"name\":\"ERC721NonexistentToken\",\"type\":\"error\"}],\"devdoc\":{\"details\":\"Standard ERC-721 Errors Interface of the https://eips.ethereum.org/EIPS/eip-6093[ERC-6093] custom errors for ERC-721 tokens.\",\"errors\":{\"ERC721IncorrectOwner(address,uint256,address)\":[{\"details\":\"Indicates an error related to the ownership over a particular token. Used in transfers.\",\"params\":{\"owner\":\"Address of the current owner of a token.\",\"sender\":\"Address whose tokens are being transferred.\",\"tokenId\":\"Identifier number of a token.\"}}],\"ERC721InsufficientApproval(address,uint256)\":[{\"details\":\"Indicates a failure with the `operator`\\u2019s approval. Used in transfers.\",\"params\":{\"operator\":\"Address that may be allowed to operate on tokens without being their owner.\",\"tokenId\":\"Identifier number of a token.\"}}],\"ERC721InvalidApprover(address)\":[{\"details\":\"Indicates a failure with the `approver` of a token to be approved. Used in approvals.\",\"params\":{\"approver\":\"Address initiating an approval operation.\"}}],\"ERC721InvalidOperator(address)\":[{\"details\":\"Indicates a failure with the `operator` to be approved. Used in approvals.\",\"params\":{\"operator\":\"Address that may be allowed to operate on tokens without being their owner.\"}}],\"ERC721InvalidOwner(address)\":[{\"details\":\"Indicates that an address can't be an owner. For example, `address(0)` is a forbidden owner in ERC-20. Used in balance queries.\",\"params\":{\"owner\":\"Address of the current owner of a token.\"}}],\"ERC721InvalidReceiver(address)\":[{\"details\":\"Indicates a failure with the token `receiver`. Used in transfers.\",\"params\":{\"receiver\":\"Address to which tokens are being transferred.\"}}],\"ERC721InvalidSender(address)\":[{\"details\":\"Indicates a failure with the token `sender`. Used in transfers.\",\"params\":{\"sender\":\"Address whose tokens are being transferred.\"}}],\"ERC721NonexistentToken(uint256)\":[{\"details\":\"Indicates a `tokenId` whose `owner` is the zero address.\",\"params\":{\"tokenId\":\"Identifier number of a token.\"}}]},\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"@openzeppelin/contracts/interfaces/draft-IERC6093.sol\":\"IERC721Errors\"},\"evmVersion\":\"paris\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[],\"viaIR\":true},\"sources\":{\"@openzeppelin/contracts/interfaces/draft-IERC6093.sol\":{\"keccak256\":\"0x19fdfb0f3b89a230e7dbd1cf416f1a6b531a3ee5db4da483f946320fc74afc0e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3490d794728f5bfecb46820431adaff71ba374141545ec20b650bb60353fac23\",\"dweb:/ipfs/QmPsfxjVpMcZbpE7BH93DzTpEaktESigEw4SmDzkXuJ4WR\"]}},\"version\":1}"}}, "@openzeppelin/contracts/token/ERC20/ERC20.sol": {"ERC20": {"abi": [{"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "allowance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "name": "ERC20InsufficientAllowance", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "uint256", "name": "balance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "name": "ERC20InsufficientBalance", "type": "error"}, {"inputs": [{"internalType": "address", "name": "approver", "type": "address"}], "name": "ERC20InvalidApprover", "type": "error"}, {"inputs": [{"internalType": "address", "name": "receiver", "type": "address"}], "name": "ERC20InvalidReceiver", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}], "name": "ERC20InvalidSender", "type": "error"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}], "name": "ERC20InvalidSpender", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "spender", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Approval", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Transfer", "type": "event"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}], "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}], "evm": {"bytecode": {"functionDebugData": {}, "generatedSources": [], "linkReferences": {}, "object": "", "opcodes": "", "sourceMap": ""}, "deployedBytecode": {"functionDebugData": {}, "generatedSources": [], "immutableReferences": {}, "linkReferences": {}, "object": "", "opcodes": "", "sourceMap": ""}, "methodIdentifiers": {"allowance(address,address)": "dd62ed3e", "approve(address,uint256)": "095ea7b3", "balanceOf(address)": "70a08231", "decimals()": "313ce567", "name()": "06fdde03", "symbol()": "95d89b41", "totalSupply()": "18160ddd", "transfer(address,uint256)": "a9059cbb", "transferFrom(address,address,uint256)": "23b872dd"}}, "metadata": "{\"compiler\":{\"version\":\"0.8.24+commit.e11b9ed9\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"allowance\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"needed\",\"type\":\"uint256\"}],\"name\":\"ERC20InsufficientAllowance\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"balance\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"needed\",\"type\":\"uint256\"}],\"name\":\"ERC20InsufficientBalance\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"approver\",\"type\":\"address\"}],\"name\":\"ERC20InvalidApprover\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"}],\"name\":\"ERC20InvalidReceiver\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"}],\"name\":\"ERC20InvalidSender\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"}],\"name\":\"ERC20InvalidSpender\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"Approval\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"Transfer\",\"type\":\"event\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"}],\"name\":\"allowance\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"approve\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"balanceOf\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"decimals\",\"outputs\":[{\"internalType\":\"uint8\",\"name\":\"\",\"type\":\"uint8\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"name\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"symbol\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"totalSupply\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"transfer\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"transferFrom\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"details\":\"Implementation of the {IERC20} interface. This implementation is agnostic to the way tokens are created. This means that a supply mechanism has to be added in a derived contract using {_mint}. TIP: For a detailed writeup see our guide https://forum.openzeppelin.com/t/how-to-implement-erc20-supply-mechanisms/226[How to implement supply mechanisms]. The default value of {decimals} is 18. To change this, you should override this function so it returns a different value. We have followed general OpenZeppelin Contracts guidelines: functions revert instead returning `false` on failure. This behavior is nonetheless conventional and does not conflict with the expectations of ERC-20 applications.\",\"errors\":{\"ERC20InsufficientAllowance(address,uint256,uint256)\":[{\"details\":\"Indicates a failure with the `spender`\\u2019s `allowance`. Used in transfers.\",\"params\":{\"allowance\":\"Amount of tokens a `spender` is allowed to operate with.\",\"needed\":\"Minimum amount required to perform a transfer.\",\"spender\":\"Address that may be allowed to operate on tokens without being their owner.\"}}],\"ERC20InsufficientBalance(address,uint256,uint256)\":[{\"details\":\"Indicates an error related to the current `balance` of a `sender`. Used in transfers.\",\"params\":{\"balance\":\"Current balance for the interacting account.\",\"needed\":\"Minimum amount required to perform a transfer.\",\"sender\":\"Address whose tokens are being transferred.\"}}],\"ERC20InvalidApprover(address)\":[{\"details\":\"Indicates a failure with the `approver` of a token to be approved. Used in approvals.\",\"params\":{\"approver\":\"Address initiating an approval operation.\"}}],\"ERC20InvalidReceiver(address)\":[{\"details\":\"Indicates a failure with the token `receiver`. Used in transfers.\",\"params\":{\"receiver\":\"Address to which tokens are being transferred.\"}}],\"ERC20InvalidSender(address)\":[{\"details\":\"Indicates a failure with the token `sender`. Used in transfers.\",\"params\":{\"sender\":\"Address whose tokens are being transferred.\"}}],\"ERC20InvalidSpender(address)\":[{\"details\":\"Indicates a failure with the `spender` to be approved. Used in approvals.\",\"params\":{\"spender\":\"Address that may be allowed to operate on tokens without being their owner.\"}}]},\"events\":{\"Approval(address,address,uint256)\":{\"details\":\"Emitted when the allowance of a `spender` for an `owner` is set by a call to {approve}. `value` is the new allowance.\"},\"Transfer(address,address,uint256)\":{\"details\":\"Emitted when `value` tokens are moved from one account (`from`) to another (`to`). Note that `value` may be zero.\"}},\"kind\":\"dev\",\"methods\":{\"allowance(address,address)\":{\"details\":\"Returns the remaining number of tokens that `spender` will be allowed to spend on behalf of `owner` through {transferFrom}. This is zero by default. This value changes when {approve} or {transferFrom} are called.\"},\"approve(address,uint256)\":{\"details\":\"See {IERC20-approve}. NOTE: If `value` is the maximum `uint256`, the allowance is not updated on `transferFrom`. This is semantically equivalent to an infinite approval. Requirements: - `spender` cannot be the zero address.\"},\"balanceOf(address)\":{\"details\":\"Returns the value of tokens owned by `account`.\"},\"constructor\":{\"details\":\"Sets the values for {name} and {symbol}. Both values are immutable: they can only be set once during construction.\"},\"decimals()\":{\"details\":\"Returns the number of decimals used to get its user representation. For example, if `decimals` equals `2`, a balance of `505` tokens should be displayed to a user as `5.05` (`505 / 10 ** 2`). Tokens usually opt for a value of 18, imitating the relationship between Ether and Wei. This is the default value returned by this function, unless it's overridden. NOTE: This information is only used for _display_ purposes: it in no way affects any of the arithmetic of the contract, including {IERC20-balanceOf} and {IERC20-transfer}.\"},\"name()\":{\"details\":\"Returns the name of the token.\"},\"symbol()\":{\"details\":\"Returns the symbol of the token, usually a shorter version of the name.\"},\"totalSupply()\":{\"details\":\"Returns the value of tokens in existence.\"},\"transfer(address,uint256)\":{\"details\":\"See {IERC20-transfer}. Requirements: - `to` cannot be the zero address. - the caller must have a balance of at least `value`.\"},\"transferFrom(address,address,uint256)\":{\"details\":\"See {IERC20-transferFrom}. Skips emitting an {Approval} event indicating an allowance update. This is not required by the ERC. See {xref-ERC20-_approve-address-address-uint256-bool-}[_approve]. NOTE: Does not update the allowance if the current allowance is the maximum `uint256`. Requirements: - `from` and `to` cannot be the zero address. - `from` must have a balance of at least `value`. - the caller must have allowance for ``from``'s tokens of at least `value`.\"}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"@openzeppelin/contracts/token/ERC20/ERC20.sol\":\"ERC20\"},\"evmVersion\":\"paris\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[],\"viaIR\":true},\"sources\":{\"@openzeppelin/contracts/interfaces/draft-IERC6093.sol\":{\"keccak256\":\"0x19fdfb0f3b89a230e7dbd1cf416f1a6b531a3ee5db4da483f946320fc74afc0e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3490d794728f5bfecb46820431adaff71ba374141545ec20b650bb60353fac23\",\"dweb:/ipfs/QmPsfxjVpMcZbpE7BH93DzTpEaktESigEw4SmDzkXuJ4WR\"]},\"@openzeppelin/contracts/token/ERC20/ERC20.sol\":{\"keccak256\":\"0x86b7b71a6aedefdad89b607378eeab1dcc5389b9ea7d17346d08af01d7190994\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1dc2db8d94a21eac8efe03adf574c419b08536409b416057a2b5b95cb772c43c\",\"dweb:/ipfs/QmZfqJCKVU1ScuX2A7s8WZdQEaikwJbDH5JBrBdKTUT4Gu\"]},\"@openzeppelin/contracts/token/ERC20/IERC20.sol\":{\"keccak256\":\"0x74ed01eb66b923d0d0cfe3be84604ac04b76482a55f9dd655e1ef4d367f95bc2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5282825a626cfe924e504274b864a652b0023591fa66f06a067b25b51ba9b303\",\"dweb:/ipfs/QmeCfPykghhMc81VJTrHTC7sF6CRvaA1FXVq2pJhwYp1dV\"]},\"@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol\":{\"keccak256\":\"0xd6fa4088198f04eef10c5bce8a2f4d60554b7ec4b987f684393c01bf79b94d9f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f95ee0bbd4dd3ac730d066ba3e785ded4565e890dbec2fa7d3b9fe3bad9d0d6e\",\"dweb:/ipfs/QmSLr6bHkPFWT7ntj34jmwfyskpwo97T9jZUrk5sz3sdtR\"]},\"@openzeppelin/contracts/utils/Context.sol\":{\"keccak256\":\"0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12\",\"dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF\"]}},\"version\":1}"}}, "@openzeppelin/contracts/token/ERC20/IERC20.sol": {"IERC20": {"abi": [{"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "spender", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Approval", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Transfer", "type": "event"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}], "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}], "evm": {"bytecode": {"functionDebugData": {}, "generatedSources": [], "linkReferences": {}, "object": "", "opcodes": "", "sourceMap": ""}, "deployedBytecode": {"functionDebugData": {}, "generatedSources": [], "immutableReferences": {}, "linkReferences": {}, "object": "", "opcodes": "", "sourceMap": ""}, "methodIdentifiers": {"allowance(address,address)": "dd62ed3e", "approve(address,uint256)": "095ea7b3", "balanceOf(address)": "70a08231", "totalSupply()": "18160ddd", "transfer(address,uint256)": "a9059cbb", "transferFrom(address,address,uint256)": "23b872dd"}}, "metadata": "{\"compiler\":{\"version\":\"0.8.24+commit.e11b9ed9\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"Approval\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"Transfer\",\"type\":\"event\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"}],\"name\":\"allowance\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"approve\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"balanceOf\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"totalSupply\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"transfer\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"transferFrom\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"details\":\"Interface of the ERC-20 standard as defined in the ERC.\",\"events\":{\"Approval(address,address,uint256)\":{\"details\":\"Emitted when the allowance of a `spender` for an `owner` is set by a call to {approve}. `value` is the new allowance.\"},\"Transfer(address,address,uint256)\":{\"details\":\"Emitted when `value` tokens are moved from one account (`from`) to another (`to`). Note that `value` may be zero.\"}},\"kind\":\"dev\",\"methods\":{\"allowance(address,address)\":{\"details\":\"Returns the remaining number of tokens that `spender` will be allowed to spend on behalf of `owner` through {transferFrom}. This is zero by default. This value changes when {approve} or {transferFrom} are called.\"},\"approve(address,uint256)\":{\"details\":\"Sets a `value` amount of tokens as the allowance of `spender` over the caller's tokens. Returns a boolean value indicating whether the operation succeeded. IMPORTANT: Beware that changing an allowance with this method brings the risk that someone may use both the old and the new allowance by unfortunate transaction ordering. One possible solution to mitigate this race condition is to first reduce the spender's allowance to 0 and set the desired value afterwards: https://github.com/ethereum/EIPs/issues/20#issuecomment-********* Emits an {Approval} event.\"},\"balanceOf(address)\":{\"details\":\"Returns the value of tokens owned by `account`.\"},\"totalSupply()\":{\"details\":\"Returns the value of tokens in existence.\"},\"transfer(address,uint256)\":{\"details\":\"Moves a `value` amount of tokens from the caller's account to `to`. Returns a boolean value indicating whether the operation succeeded. Emits a {Transfer} event.\"},\"transferFrom(address,address,uint256)\":{\"details\":\"Moves a `value` amount of tokens from `from` to `to` using the allowance mechanism. `value` is then deducted from the caller's allowance. Returns a boolean value indicating whether the operation succeeded. Emits a {Transfer} event.\"}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"@openzeppelin/contracts/token/ERC20/IERC20.sol\":\"IERC20\"},\"evmVersion\":\"paris\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[],\"viaIR\":true},\"sources\":{\"@openzeppelin/contracts/token/ERC20/IERC20.sol\":{\"keccak256\":\"0x74ed01eb66b923d0d0cfe3be84604ac04b76482a55f9dd655e1ef4d367f95bc2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5282825a626cfe924e504274b864a652b0023591fa66f06a067b25b51ba9b303\",\"dweb:/ipfs/QmeCfPykghhMc81VJTrHTC7sF6CRvaA1FXVq2pJhwYp1dV\"]}},\"version\":1}"}}, "@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol": {"IERC20Metadata": {"abi": [{"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "spender", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Approval", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Transfer", "type": "event"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}], "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}], "evm": {"bytecode": {"functionDebugData": {}, "generatedSources": [], "linkReferences": {}, "object": "", "opcodes": "", "sourceMap": ""}, "deployedBytecode": {"functionDebugData": {}, "generatedSources": [], "immutableReferences": {}, "linkReferences": {}, "object": "", "opcodes": "", "sourceMap": ""}, "methodIdentifiers": {"allowance(address,address)": "dd62ed3e", "approve(address,uint256)": "095ea7b3", "balanceOf(address)": "70a08231", "decimals()": "313ce567", "name()": "06fdde03", "symbol()": "95d89b41", "totalSupply()": "18160ddd", "transfer(address,uint256)": "a9059cbb", "transferFrom(address,address,uint256)": "23b872dd"}}, "metadata": "{\"compiler\":{\"version\":\"0.8.24+commit.e11b9ed9\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"Approval\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"Transfer\",\"type\":\"event\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"}],\"name\":\"allowance\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"approve\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"balanceOf\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"decimals\",\"outputs\":[{\"internalType\":\"uint8\",\"name\":\"\",\"type\":\"uint8\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"name\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"symbol\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"totalSupply\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"transfer\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"transferFrom\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"details\":\"Interface for the optional metadata functions from the ERC-20 standard.\",\"events\":{\"Approval(address,address,uint256)\":{\"details\":\"Emitted when the allowance of a `spender` for an `owner` is set by a call to {approve}. `value` is the new allowance.\"},\"Transfer(address,address,uint256)\":{\"details\":\"Emitted when `value` tokens are moved from one account (`from`) to another (`to`). Note that `value` may be zero.\"}},\"kind\":\"dev\",\"methods\":{\"allowance(address,address)\":{\"details\":\"Returns the remaining number of tokens that `spender` will be allowed to spend on behalf of `owner` through {transferFrom}. This is zero by default. This value changes when {approve} or {transferFrom} are called.\"},\"approve(address,uint256)\":{\"details\":\"Sets a `value` amount of tokens as the allowance of `spender` over the caller's tokens. Returns a boolean value indicating whether the operation succeeded. IMPORTANT: Beware that changing an allowance with this method brings the risk that someone may use both the old and the new allowance by unfortunate transaction ordering. One possible solution to mitigate this race condition is to first reduce the spender's allowance to 0 and set the desired value afterwards: https://github.com/ethereum/EIPs/issues/20#issuecomment-********* Emits an {Approval} event.\"},\"balanceOf(address)\":{\"details\":\"Returns the value of tokens owned by `account`.\"},\"decimals()\":{\"details\":\"Returns the decimals places of the token.\"},\"name()\":{\"details\":\"Returns the name of the token.\"},\"symbol()\":{\"details\":\"Returns the symbol of the token.\"},\"totalSupply()\":{\"details\":\"Returns the value of tokens in existence.\"},\"transfer(address,uint256)\":{\"details\":\"Moves a `value` amount of tokens from the caller's account to `to`. Returns a boolean value indicating whether the operation succeeded. Emits a {Transfer} event.\"},\"transferFrom(address,address,uint256)\":{\"details\":\"Moves a `value` amount of tokens from `from` to `to` using the allowance mechanism. `value` is then deducted from the caller's allowance. Returns a boolean value indicating whether the operation succeeded. Emits a {Transfer} event.\"}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol\":\"IERC20Metadata\"},\"evmVersion\":\"paris\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[],\"viaIR\":true},\"sources\":{\"@openzeppelin/contracts/token/ERC20/IERC20.sol\":{\"keccak256\":\"0x74ed01eb66b923d0d0cfe3be84604ac04b76482a55f9dd655e1ef4d367f95bc2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5282825a626cfe924e504274b864a652b0023591fa66f06a067b25b51ba9b303\",\"dweb:/ipfs/QmeCfPykghhMc81VJTrHTC7sF6CRvaA1FXVq2pJhwYp1dV\"]},\"@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol\":{\"keccak256\":\"0xd6fa4088198f04eef10c5bce8a2f4d60554b7ec4b987f684393c01bf79b94d9f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f95ee0bbd4dd3ac730d066ba3e785ded4565e890dbec2fa7d3b9fe3bad9d0d6e\",\"dweb:/ipfs/QmSLr6bHkPFWT7ntj34jmwfyskpwo97T9jZUrk5sz3sdtR\"]}},\"version\":1}"}}, "@openzeppelin/contracts/utils/Context.sol": {"Context": {"abi": [], "evm": {"bytecode": {"functionDebugData": {}, "generatedSources": [], "linkReferences": {}, "object": "", "opcodes": "", "sourceMap": ""}, "deployedBytecode": {"functionDebugData": {}, "generatedSources": [], "immutableReferences": {}, "linkReferences": {}, "object": "", "opcodes": "", "sourceMap": ""}, "methodIdentifiers": {}}, "metadata": "{\"compiler\":{\"version\":\"0.8.24+commit.e11b9ed9\"},\"language\":\"Solidity\",\"output\":{\"abi\":[],\"devdoc\":{\"details\":\"Provides information about the current execution context, including the sender of the transaction and its data. While these are generally available via msg.sender and msg.data, they should not be accessed in such a direct manner, since when dealing with meta-transactions the account sending and paying for execution may not be the actual sender (as far as an application is concerned). This contract is only required for intermediate, library-like contracts.\",\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"@openzeppelin/contracts/utils/Context.sol\":\"Context\"},\"evmVersion\":\"paris\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[],\"viaIR\":true},\"sources\":{\"@openzeppelin/contracts/utils/Context.sol\":{\"keccak256\":\"0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12\",\"dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF\"]}},\"version\":1}"}}, "contracts/tokens/SimpleToken.sol": {"SimpleToken": {"abi": [{"inputs": [{"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "symbol", "type": "string"}, {"internalType": "uint256", "name": "initialSupply", "type": "uint256"}, {"internalType": "address", "name": "initialOwner", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "allowance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "name": "ERC20InsufficientAllowance", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "uint256", "name": "balance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "name": "ERC20InsufficientBalance", "type": "error"}, {"inputs": [{"internalType": "address", "name": "approver", "type": "address"}], "name": "ERC20InvalidApprover", "type": "error"}, {"inputs": [{"internalType": "address", "name": "receiver", "type": "address"}], "name": "ERC20InvalidReceiver", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}], "name": "ERC20InvalidSender", "type": "error"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}], "name": "ERC20InvalidSpender", "type": "error"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "OwnableInvalidOwner", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "OwnableUnauthorizedAccount", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "spender", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Approval", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Transfer", "type": "event"}, {"inputs": [], "name": "MAX_SUPPLY", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}], "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "burn", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "mint", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "evm": {"bytecode": {"functionDebugData": {"abi_decode_string_fromMemory": {"entryPoint": 1151, "id": null, "parameterSlots": 2, "returnSlots": 1}, "allocate_memory": {"entryPoint": 1113, "id": null, "parameterSlots": 1, "returnSlots": 1}}, "generatedSources": [], "linkReferences": {}, "object": "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", "opcodes": "PUSH1 0x40 PUSH1 0x80 DUP2 MSTORE CALLVALUE PUSH3 0x454 JUMPI PUSH3 0xF23 DUP1 CODESIZE SUB DUP1 PUSH3 0x1E DUP2 PUSH3 0x459 JUMP JUMPDEST SWAP3 DUP4 CODECOPY DUP2 ADD PUSH1 0x80 DUP3 DUP3 SUB SLT PUSH3 0x454 JUMPI DUP2 MLOAD PUSH1 0x1 PUSH1 0x1 PUSH1 0x40 SHL SUB SWAP3 SWAP1 DUP4 DUP2 GT PUSH3 0x454 JUMPI DUP3 PUSH3 0x50 SWAP2 DUP4 ADD PUSH3 0x47F JUMP JUMPDEST SWAP3 PUSH1 0x20 SWAP3 DUP4 DUP4 ADD MLOAD SWAP1 DUP3 DUP3 GT PUSH3 0x454 JUMPI PUSH3 0x6E SWAP2 DUP5 ADD PUSH3 0x47F JUMP JUMPDEST SWAP1 PUSH1 0x60 DUP7 DUP5 ADD MLOAD SWAP4 ADD MLOAD SWAP5 PUSH1 0x1 DUP1 PUSH1 0xA0 SHL SUB SWAP2 DUP3 DUP8 AND DUP1 SWAP8 SUB PUSH3 0x454 JUMPI DUP2 MLOAD DUP2 DUP2 GT PUSH3 0x354 JUMPI PUSH1 0x3 SWAP1 DUP2 SLOAD SWAP1 PUSH1 0x1 SWAP5 DUP6 DUP4 DUP2 SHR SWAP4 AND DUP1 ISZERO PUSH3 0x449 JUMPI JUMPDEST DUP11 DUP5 LT EQ PUSH3 0x433 JUMPI DUP2 SWAP1 PUSH1 0x1F SWAP4 DUP5 DUP2 GT PUSH3 0x3DD JUMPI JUMPDEST POP DUP11 SWAP1 DUP5 DUP4 GT PUSH1 0x1 EQ PUSH3 0x376 JUMPI PUSH1 0x0 SWAP3 PUSH3 0x36A JUMPI JUMPDEST POP POP PUSH1 0x0 NOT DUP3 DUP6 SHL SHR NOT AND SWAP1 DUP6 SHL OR DUP3 SSTORE JUMPDEST DUP6 MLOAD SWAP3 DUP4 GT PUSH3 0x354 JUMPI PUSH1 0x4 SWAP6 DUP7 SLOAD DUP6 DUP2 DUP2 SHR SWAP2 AND DUP1 ISZERO PUSH3 0x349 JUMPI JUMPDEST DUP11 DUP3 LT EQ PUSH3 0x334 JUMPI DUP3 DUP2 GT PUSH3 0x2E9 JUMPI JUMPDEST POP DUP9 SWAP2 DUP5 GT PUSH1 0x1 EQ PUSH3 0x27E JUMPI SWAP4 DUP4 SWAP5 SWAP2 DUP5 SWAP3 PUSH1 0x0 SWAP6 PUSH3 0x272 JUMPI JUMPDEST POP POP SHL SWAP3 PUSH1 0x0 NOT SWAP2 SHL SHR NOT AND OR DUP3 SSTORE JUMPDEST DUP5 ISZERO PUSH3 0x25B JUMPI PUSH1 0x5 DUP1 SLOAD PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB NOT DUP2 AND DUP8 OR SWAP1 SWAP2 SSTORE DUP7 MLOAD SWAP2 DUP7 SWAP2 AND PUSH32 0x8BE0079C531659141344CD1FD0A4F28419497F9722A3DAAFE3B4186F6B6457E0 PUSH1 0x0 DUP1 LOG3 PUSH11 0x52B7D2DCC80CD2E4000000 DUP4 GT PUSH3 0x212 JUMPI POP PUSH1 0x2 SLOAD SWAP1 DUP3 DUP3 ADD DUP1 SWAP3 GT PUSH3 0x1FD JUMPI POP PUSH1 0x2 SSTORE PUSH1 0x0 DUP4 DUP2 MSTORE DUP1 DUP4 MSTORE DUP5 DUP2 KECCAK256 DUP1 SLOAD DUP4 ADD SWAP1 SSTORE DUP5 MLOAD SWAP2 DUP3 MSTORE SWAP2 PUSH32 0xDDF252AD1BE2C89B69C2B068FC378DAA952BA7F163C4A11628F55A4DF523B3EF SWAP2 LOG3 MLOAD PUSH2 0xA31 SWAP1 DUP2 PUSH3 0x4F2 DUP3 CODECOPY RETURN JUMPDEST PUSH1 0x11 SWAP1 PUSH4 0x4E487B71 PUSH1 0xE0 SHL PUSH1 0x0 MSTORE MSTORE PUSH1 0x24 PUSH1 0x0 REVERT JUMPDEST DUP4 PUSH1 0x84 SWAP3 PUSH3 0x461BCD PUSH1 0xE5 SHL DUP4 MSTORE DUP3 ADD MSTORE PUSH1 0x21 PUSH1 0x24 DUP3 ADD MSTORE PUSH32 0x496E697469616C20737570706C792065786365656473206D617820737570706C PUSH1 0x44 DUP3 ADD MSTORE PUSH1 0x79 PUSH1 0xF8 SHL PUSH1 0x64 DUP3 ADD MSTORE REVERT JUMPDEST DUP6 MLOAD PUSH4 0x1E4FBDF7 PUSH1 0xE0 SHL DUP2 MSTORE PUSH1 0x0 DUP2 DUP5 ADD MSTORE PUSH1 0x24 SWAP1 REVERT JUMPDEST ADD MLOAD SWAP4 POP CODESIZE DUP1 PUSH3 0x138 JUMP JUMPDEST SWAP2 SWAP1 PUSH1 0x1F NOT DUP5 AND SWAP3 DUP8 PUSH1 0x0 MSTORE DUP5 DUP11 PUSH1 0x0 KECCAK256 SWAP5 PUSH1 0x0 JUMPDEST DUP13 DUP10 DUP4 DUP4 LT PUSH3 0x2D1 JUMPI POP POP POP LT PUSH3 0x2B6 JUMPI JUMPDEST POP POP POP POP DUP2 SHL ADD DUP3 SSTORE PUSH3 0x148 JUMP JUMPDEST ADD MLOAD SWAP1 PUSH1 0xF8 DUP5 PUSH1 0x0 NOT SWAP3 SHL AND SHR NOT AND SWAP1 SSTORE CODESIZE DUP1 DUP1 DUP1 PUSH3 0x2A7 JUMP JUMPDEST DUP7 DUP7 ADD MLOAD DUP10 SSTORE SWAP1 SWAP8 ADD SWAP7 SWAP5 DUP6 ADD SWAP5 DUP9 SWAP4 POP ADD PUSH3 0x293 JUMP JUMPDEST DUP8 PUSH1 0x0 MSTORE DUP10 PUSH1 0x0 KECCAK256 DUP4 DUP1 DUP8 ADD PUSH1 0x5 SHR DUP3 ADD SWAP3 DUP13 DUP9 LT PUSH3 0x32A JUMPI JUMPDEST ADD PUSH1 0x5 SHR ADD SWAP1 DUP7 SWAP1 JUMPDEST DUP3 DUP2 LT PUSH3 0x31D JUMPI POP POP PUSH3 0x11C JUMP JUMPDEST PUSH1 0x0 DUP2 SSTORE ADD DUP7 SWAP1 PUSH3 0x30D JUMP JUMPDEST SWAP3 POP DUP2 SWAP3 PUSH3 0x304 JUMP JUMPDEST PUSH1 0x22 DUP9 PUSH4 0x4E487B71 PUSH1 0xE0 SHL PUSH1 0x0 MSTORE MSTORE PUSH1 0x24 PUSH1 0x0 REVERT JUMPDEST SWAP1 PUSH1 0x7F AND SWAP1 PUSH3 0x10A JUMP JUMPDEST PUSH4 0x4E487B71 PUSH1 0xE0 SHL PUSH1 0x0 MSTORE PUSH1 0x41 PUSH1 0x4 MSTORE PUSH1 0x24 PUSH1 0x0 REVERT JUMPDEST ADD MLOAD SWAP1 POP CODESIZE DUP1 PUSH3 0xDB JUMP JUMPDEST PUSH1 0x0 DUP7 DUP2 MSTORE DUP13 DUP2 KECCAK256 DUP10 SWAP6 POP SWAP3 SWAP2 SWAP1 PUSH1 0x1F NOT DUP6 AND SWAP1 DUP15 JUMPDEST DUP3 DUP3 LT PUSH3 0x3C5 JUMPI POP POP DUP5 GT PUSH3 0x3AC JUMPI JUMPDEST POP POP POP DUP2 SHL ADD DUP3 SSTORE PUSH3 0xED JUMP JUMPDEST ADD MLOAD PUSH1 0x0 NOT DUP4 DUP8 SHL PUSH1 0xF8 AND SHR NOT AND SWAP1 SSTORE CODESIZE DUP1 DUP1 PUSH3 0x39E JUMP JUMPDEST DUP4 DUP6 ADD MLOAD DUP7 SSTORE DUP12 SWAP8 SWAP1 SWAP6 ADD SWAP5 SWAP4 DUP5 ADD SWAP4 ADD DUP15 PUSH3 0x38C JUMP JUMPDEST SWAP1 SWAP2 POP DUP5 PUSH1 0x0 MSTORE DUP11 PUSH1 0x0 KECCAK256 DUP5 DUP1 DUP6 ADD PUSH1 0x5 SHR DUP3 ADD SWAP3 DUP14 DUP7 LT PUSH3 0x429 JUMPI JUMPDEST SWAP2 DUP10 SWAP2 DUP7 SWAP6 SWAP5 SWAP4 ADD PUSH1 0x5 SHR ADD SWAP2 JUMPDEST DUP3 DUP2 LT PUSH3 0x419 JUMPI POP POP PUSH3 0xC4 JUMP JUMPDEST PUSH1 0x0 DUP2 SSTORE DUP6 SWAP5 POP DUP10 SWAP2 ADD PUSH3 0x409 JUMP JUMPDEST SWAP3 POP DUP2 SWAP3 PUSH3 0x3FB JUMP JUMPDEST PUSH4 0x4E487B71 PUSH1 0xE0 SHL PUSH1 0x0 MSTORE PUSH1 0x22 PUSH1 0x4 MSTORE PUSH1 0x24 PUSH1 0x0 REVERT JUMPDEST SWAP3 PUSH1 0x7F AND SWAP3 PUSH3 0xAD JUMP JUMPDEST PUSH1 0x0 DUP1 REVERT JUMPDEST PUSH1 0x40 MLOAD SWAP2 SWAP1 PUSH1 0x1F ADD PUSH1 0x1F NOT AND DUP3 ADD PUSH1 0x1 PUSH1 0x1 PUSH1 0x40 SHL SUB DUP2 GT DUP4 DUP3 LT OR PUSH3 0x354 JUMPI PUSH1 0x40 MSTORE JUMP JUMPDEST SWAP2 SWAP1 DUP1 PUSH1 0x1F DUP5 ADD SLT ISZERO PUSH3 0x454 JUMPI DUP3 MLOAD PUSH1 0x1 PUSH1 0x1 PUSH1 0x40 SHL SUB DUP2 GT PUSH3 0x354 JUMPI PUSH1 0x20 SWAP1 PUSH3 0x4B5 PUSH1 0x1F DUP3 ADD PUSH1 0x1F NOT AND DUP4 ADD PUSH3 0x459 JUMP JUMPDEST SWAP3 DUP2 DUP5 MSTORE DUP3 DUP3 DUP8 ADD ADD GT PUSH3 0x454 JUMPI PUSH1 0x0 JUMPDEST DUP2 DUP2 LT PUSH3 0x4DD JUMPI POP DUP3 PUSH1 0x0 SWAP4 SWAP5 SWAP6 POP ADD ADD MSTORE SWAP1 JUMP JUMPDEST DUP6 DUP2 ADD DUP4 ADD MLOAD DUP5 DUP3 ADD DUP5 ADD MSTORE DUP3 ADD PUSH3 0x4C7 JUMP INVALID PUSH1 0x80 PUSH1 0x40 DUP2 DUP2 MSTORE PUSH1 0x4 DUP1 CALLDATASIZE LT ISZERO PUSH2 0x15 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST PUSH1 0x0 SWAP3 DUP4 CALLDATALOAD PUSH1 0xE0 SHR SWAP1 DUP2 PUSH4 0x6FDDE03 EQ PUSH2 0x75D JUMPI POP DUP1 PUSH4 0x95EA7B3 EQ PUSH2 0x6B4 JUMPI DUP1 PUSH4 0x18160DDD EQ PUSH2 0x695 JUMPI DUP1 PUSH4 0x23B872DD EQ PUSH2 0x5A2 JUMPI DUP1 PUSH4 0x313CE567 EQ PUSH2 0x586 JUMPI DUP1 PUSH4 0x32CB6B0C EQ PUSH2 0x560 JUMPI DUP1 PUSH4 0x40C10F19 EQ PUSH2 0x470 JUMPI DUP1 PUSH4 0x42966C68 EQ PUSH2 0x3BA JUMPI DUP1 PUSH4 0x70A08231 EQ PUSH2 0x383 JUMPI DUP1 PUSH4 0x715018A6 EQ PUSH2 0x323 JUMPI DUP1 PUSH4 0x8DA5CB5B EQ PUSH2 0x2FA JUMPI DUP1 PUSH4 0x95D89B41 EQ PUSH2 0x1D8 JUMPI DUP1 PUSH4 0xA9059CBB EQ PUSH2 0x1A7 JUMPI DUP1 PUSH4 0xDD62ED3E EQ PUSH2 0x15A JUMPI PUSH4 0xF2FDE38B EQ PUSH2 0xC8 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST CALLVALUE PUSH2 0x156 JUMPI PUSH1 0x20 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x156 JUMPI PUSH2 0xE1 PUSH2 0x89D JUMP JUMPDEST SWAP1 PUSH2 0xEA PUSH2 0x9CF JUMP JUMPDEST PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB SWAP2 DUP3 AND SWAP3 DUP4 ISZERO PUSH2 0x140 JUMPI POP POP PUSH1 0x5 SLOAD DUP3 PUSH12 0xFFFFFFFFFFFFFFFFFFFFFFFF PUSH1 0xA0 SHL DUP3 AND OR PUSH1 0x5 SSTORE AND PUSH32 0x8BE0079C531659141344CD1FD0A4F28419497F9722A3DAAFE3B4186F6B6457E0 DUP4 DUP1 LOG3 DUP1 RETURN JUMPDEST MLOAD PUSH4 0x1E4FBDF7 PUSH1 0xE0 SHL DUP2 MSTORE SWAP1 DUP2 ADD DUP5 SWAP1 MSTORE PUSH1 0x24 SWAP1 REVERT JUMPDEST DUP3 DUP1 REVERT JUMPDEST POP POP CALLVALUE PUSH2 0x1A3 JUMPI DUP1 PUSH1 0x3 NOT CALLDATASIZE ADD SLT PUSH2 0x1A3 JUMPI DUP1 PUSH1 0x20 SWAP3 PUSH2 0x178 PUSH2 0x89D JUMP JUMPDEST PUSH2 0x180 PUSH2 0x8B8 JUMP JUMPDEST PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB SWAP2 DUP3 AND DUP4 MSTORE PUSH1 0x1 DUP7 MSTORE DUP4 DUP4 KECCAK256 SWAP2 AND DUP3 MSTORE DUP5 MSTORE KECCAK256 SLOAD SWAP1 MLOAD SWAP1 DUP2 MSTORE RETURN JUMPDEST POP DUP1 REVERT JUMPDEST POP POP CALLVALUE PUSH2 0x1A3 JUMPI DUP1 PUSH1 0x3 NOT CALLDATASIZE ADD SLT PUSH2 0x1A3 JUMPI PUSH1 0x20 SWAP1 PUSH2 0x1D1 PUSH2 0x1C7 PUSH2 0x89D JUMP JUMPDEST PUSH1 0x24 CALLDATALOAD SWAP1 CALLER PUSH2 0x8F1 JUMP JUMPDEST MLOAD PUSH1 0x1 DUP2 MSTORE RETURN JUMPDEST POP SWAP2 SWAP1 CALLVALUE PUSH2 0x1A3 JUMPI DUP2 PUSH1 0x3 NOT CALLDATASIZE ADD SLT PUSH2 0x1A3 JUMPI DUP1 MLOAD SWAP1 DUP3 DUP5 SLOAD PUSH1 0x1 DUP2 PUSH1 0x1 SHR SWAP1 PUSH1 0x1 DUP4 AND SWAP3 DUP4 ISZERO PUSH2 0x2F0 JUMPI JUMPDEST PUSH1 0x20 SWAP4 DUP5 DUP5 LT DUP2 EQ PUSH2 0x2DD JUMPI DUP4 DUP9 MSTORE SWAP1 DUP2 ISZERO PUSH2 0x2C1 JUMPI POP PUSH1 0x1 EQ PUSH2 0x26C JUMPI JUMPDEST POP POP POP DUP3 SWAP1 SUB PUSH1 0x1F ADD PUSH1 0x1F NOT AND DUP3 ADD SWAP3 PUSH8 0xFFFFFFFFFFFFFFFF DUP5 GT DUP4 DUP6 LT OR PUSH2 0x259 JUMPI POP DUP3 SWAP2 DUP3 PUSH2 0x255 SWAP3 MSTORE DUP3 PUSH2 0x854 JUMP JUMPDEST SUB SWAP1 RETURN JUMPDEST PUSH4 0x4E487B71 PUSH1 0xE0 SHL DUP2 MSTORE PUSH1 0x41 DUP6 MSTORE PUSH1 0x24 SWAP1 REVERT JUMPDEST DUP8 DUP8 MSTORE SWAP2 SWAP3 POP DUP6 SWAP2 DUP4 PUSH32 0x8A35ACFBC15FF81A39AE7D344FD709F28E8600B4AA8C65C6B64BFE7FE36BD19B JUMPDEST DUP4 DUP6 LT PUSH2 0x2AD JUMPI POP POP POP POP DUP4 ADD ADD CODESIZE DUP1 DUP1 PUSH2 0x223 JUMP JUMPDEST DUP1 SLOAD DUP9 DUP7 ADD DUP4 ADD MSTORE SWAP4 ADD SWAP3 DUP5 SWAP1 DUP3 ADD PUSH2 0x297 JUMP JUMPDEST PUSH1 0xFF NOT AND DUP8 DUP6 ADD MSTORE POP POP ISZERO ISZERO PUSH1 0x5 SHL DUP5 ADD ADD SWAP1 POP CODESIZE DUP1 DUP1 PUSH2 0x223 JUMP JUMPDEST PUSH4 0x4E487B71 PUSH1 0xE0 SHL DUP10 MSTORE PUSH1 0x22 DUP11 MSTORE PUSH1 0x24 DUP10 REVERT JUMPDEST SWAP2 PUSH1 0x7F AND SWAP2 PUSH2 0x204 JUMP JUMPDEST POP POP CALLVALUE PUSH2 0x1A3 JUMPI DUP2 PUSH1 0x3 NOT CALLDATASIZE ADD SLT PUSH2 0x1A3 JUMPI PUSH1 0x5 SLOAD SWAP1 MLOAD PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB SWAP1 SWAP2 AND DUP2 MSTORE PUSH1 0x20 SWAP1 RETURN JUMPDEST DUP4 CALLVALUE PUSH2 0x380 JUMPI DUP1 PUSH1 0x3 NOT CALLDATASIZE ADD SLT PUSH2 0x380 JUMPI PUSH2 0x33C PUSH2 0x9CF JUMP JUMPDEST PUSH1 0x5 DUP1 SLOAD PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB NOT DUP2 AND SWAP1 SWAP2 SSTORE DUP2 SWAP1 PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB AND PUSH32 0x8BE0079C531659141344CD1FD0A4F28419497F9722A3DAAFE3B4186F6B6457E0 DUP3 DUP1 LOG3 DUP1 RETURN JUMPDEST DUP1 REVERT JUMPDEST POP POP CALLVALUE PUSH2 0x1A3 JUMPI PUSH1 0x20 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x1A3 JUMPI PUSH1 0x20 SWAP2 DUP2 SWAP1 PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB PUSH2 0x3AB PUSH2 0x89D JUMP JUMPDEST AND DUP2 MSTORE DUP1 DUP5 MSTORE KECCAK256 SLOAD SWAP1 MLOAD SWAP1 DUP2 MSTORE RETURN JUMPDEST POP SWAP2 SWAP1 CALLVALUE PUSH2 0x1A3 JUMPI PUSH1 0x20 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x1A3 JUMPI DUP3 CALLDATALOAD SWAP1 CALLER ISZERO PUSH2 0x45A JUMPI CALLER DUP4 MSTORE DUP3 PUSH1 0x20 MSTORE DUP1 DUP4 KECCAK256 SLOAD SWAP4 DUP3 DUP6 LT PUSH2 0x42F JUMPI POP DUP2 DUP4 SWAP5 CALLER DUP6 MSTORE DUP5 PUSH1 0x20 MSTORE SUB DUP2 DUP5 KECCAK256 SSTORE DUP2 PUSH1 0x2 SLOAD SUB PUSH1 0x2 SSTORE MLOAD SWAP1 DUP2 MSTORE PUSH32 0xDDF252AD1BE2C89B69C2B068FC378DAA952BA7F163C4A11628F55A4DF523B3EF PUSH1 0x20 CALLER SWAP3 LOG3 DUP1 RETURN JUMPDEST SWAP1 MLOAD PUSH4 0x391434E3 PUSH1 0xE2 SHL DUP2 MSTORE CALLER SWAP2 DUP2 ADD SWAP2 DUP3 MSTORE PUSH1 0x20 DUP3 ADD DUP6 SWAP1 MSTORE PUSH1 0x40 DUP3 ADD DUP4 SWAP1 MSTORE SWAP1 DUP2 SWAP1 PUSH1 0x60 ADD SUB SWAP1 REVERT JUMPDEST MLOAD PUSH4 0x4B637E8F PUSH1 0xE1 SHL DUP2 MSTORE DUP1 DUP5 ADD DUP4 SWAP1 MSTORE PUSH1 0x24 SWAP1 REVERT JUMPDEST POP SWAP1 CALLVALUE PUSH2 0x156 JUMPI DUP1 PUSH1 0x3 NOT CALLDATASIZE ADD SLT PUSH2 0x156 JUMPI PUSH2 0x48A PUSH2 0x89D JUMP JUMPDEST SWAP1 PUSH1 0x24 CALLDATALOAD SWAP2 PUSH2 0x497 PUSH2 0x9CF JUMP JUMPDEST PUSH1 0x2 SLOAD SWAP1 PUSH11 0x52B7D2DCC80CD2E4000000 PUSH2 0x4B1 DUP6 DUP5 PUSH2 0x8CE JUMP JUMPDEST GT PUSH2 0x528 JUMPI PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB AND SWAP4 DUP5 ISZERO PUSH2 0x511 JUMPI POP DUP3 PUSH32 0xDDF252AD1BE2C89B69C2B068FC378DAA952BA7F163C4A11628F55A4DF523B3EF SWAP3 PUSH2 0x4F7 DUP8 SWAP6 PUSH1 0x20 SWAP5 PUSH2 0x8CE JUMP JUMPDEST PUSH1 0x2 SSTORE DUP6 DUP6 MSTORE DUP5 DUP4 MSTORE DUP1 DUP6 KECCAK256 DUP3 DUP2 SLOAD ADD SWAP1 SSTORE MLOAD SWAP1 DUP2 MSTORE LOG3 DUP1 RETURN JUMPDEST DUP3 MLOAD PUSH4 0xEC442F05 PUSH1 0xE0 SHL DUP2 MSTORE SWAP1 DUP2 ADD DUP7 SWAP1 MSTORE PUSH1 0x24 SWAP1 REVERT JUMPDEST DUP3 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE PUSH1 0x20 DUP2 DUP8 ADD MSTORE PUSH1 0x12 PUSH1 0x24 DUP3 ADD MSTORE PUSH18 0x45786365656473206D617820737570706C79 PUSH1 0x70 SHL PUSH1 0x44 DUP3 ADD MSTORE PUSH1 0x64 SWAP1 REVERT JUMPDEST POP POP CALLVALUE PUSH2 0x1A3 JUMPI DUP2 PUSH1 0x3 NOT CALLDATASIZE ADD SLT PUSH2 0x1A3 JUMPI PUSH1 0x20 SWAP1 MLOAD PUSH11 0x52B7D2DCC80CD2E4000000 DUP2 MSTORE RETURN JUMPDEST POP POP CALLVALUE PUSH2 0x1A3 JUMPI DUP2 PUSH1 0x3 NOT CALLDATASIZE ADD SLT PUSH2 0x1A3 JUMPI PUSH1 0x20 SWAP1 MLOAD PUSH1 0x12 DUP2 MSTORE RETURN JUMPDEST POP DUP3 CALLVALUE PUSH2 0x380 JUMPI PUSH1 0x60 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x380 JUMPI PUSH2 0x5BD PUSH2 0x89D JUMP JUMPDEST PUSH2 0x5C5 PUSH2 0x8B8 JUMP JUMPDEST SWAP2 PUSH1 0x44 CALLDATALOAD SWAP4 PUSH1 0x1 DUP1 PUSH1 0xA0 SHL SUB DUP4 AND DUP1 DUP4 MSTORE PUSH1 0x1 PUSH1 0x20 MSTORE DUP7 DUP4 KECCAK256 CALLER DUP5 MSTORE PUSH1 0x20 MSTORE DUP7 DUP4 KECCAK256 SLOAD SWAP2 PUSH1 0x0 NOT DUP4 LT PUSH2 0x601 JUMPI JUMPDEST PUSH1 0x20 DUP9 PUSH2 0x1D1 DUP10 DUP10 DUP10 PUSH2 0x8F1 JUMP JUMPDEST DUP7 DUP4 LT PUSH2 0x669 JUMPI DUP2 ISZERO PUSH2 0x652 JUMPI CALLER ISZERO PUSH2 0x63B JUMPI POP DUP3 MSTORE PUSH1 0x1 PUSH1 0x20 SWAP1 DUP2 MSTORE DUP7 DUP4 KECCAK256 CALLER DUP5 MSTORE DUP2 MSTORE SWAP2 DUP7 SWAP1 KECCAK256 SWAP1 DUP6 SWAP1 SUB SWAP1 SSTORE DUP3 SWAP1 PUSH2 0x1D1 DUP8 PUSH2 0x5F3 JUMP JUMPDEST DUP8 MLOAD PUSH4 0x4A1406B1 PUSH1 0xE1 SHL DUP2 MSTORE SWAP1 DUP2 ADD DUP5 SWAP1 MSTORE PUSH1 0x24 SWAP1 REVERT JUMPDEST DUP8 MLOAD PUSH4 0xE602DF05 PUSH1 0xE0 SHL DUP2 MSTORE SWAP1 DUP2 ADD DUP5 SWAP1 MSTORE PUSH1 0x24 SWAP1 REVERT JUMPDEST DUP8 MLOAD PUSH4 0x7DC7A0D9 PUSH1 0xE1 SHL DUP2 MSTORE CALLER SWAP2 DUP2 ADD SWAP2 DUP3 MSTORE PUSH1 0x20 DUP3 ADD SWAP4 SWAP1 SWAP4 MSTORE PUSH1 0x40 DUP2 ADD DUP8 SWAP1 MSTORE DUP3 SWAP2 POP PUSH1 0x60 ADD SUB SWAP1 REVERT JUMPDEST POP POP CALLVALUE PUSH2 0x1A3 JUMPI DUP2 PUSH1 0x3 NOT CALLDATASIZE ADD SLT PUSH2 0x1A3 JUMPI PUSH1 0x20 SWAP1 PUSH1 0x2 SLOAD SWAP1 MLOAD SWAP1 DUP2 MSTORE RETURN JUMPDEST POP CALLVALUE PUSH2 0x156 JUMPI DUP2 PUSH1 0x3 NOT CALLDATASIZE ADD SLT PUSH2 0x156 JUMPI PUSH2 0x6CD PUSH2 0x89D JUMP JUMPDEST PUSH1 0x24 CALLDATALOAD SWAP1 CALLER ISZERO PUSH2 0x746 JUMPI PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB AND SWAP2 DUP3 ISZERO PUSH2 0x72F JUMPI POP DUP1 DUP4 PUSH1 0x20 SWAP6 CALLER DUP2 MSTORE PUSH1 0x1 DUP8 MSTORE DUP2 DUP2 KECCAK256 DUP6 DUP3 MSTORE DUP8 MSTORE KECCAK256 SSTORE DUP3 MLOAD SWAP1 DUP2 MSTORE PUSH32 0x8C5BE1E5EBEC7D5BD14F71427D1E84F3DD0314C0F7B2291E5B200AC8C7C3B925 DUP5 CALLER SWAP3 LOG3 MLOAD PUSH1 0x1 DUP2 MSTORE RETURN JUMPDEST DUP4 MLOAD PUSH4 0x4A1406B1 PUSH1 0xE1 SHL DUP2 MSTORE SWAP1 DUP2 ADD DUP6 SWAP1 MSTORE PUSH1 0x24 SWAP1 REVERT JUMPDEST DUP4 MLOAD PUSH4 0xE602DF05 PUSH1 0xE0 SHL DUP2 MSTORE DUP1 DUP5 ADD DUP7 SWAP1 MSTORE PUSH1 0x24 SWAP1 REVERT JUMPDEST DUP5 SWAP2 POP DUP4 CALLVALUE PUSH2 0x156 JUMPI DUP3 PUSH1 0x3 NOT CALLDATASIZE ADD SLT PUSH2 0x156 JUMPI DUP3 PUSH1 0x3 SLOAD PUSH1 0x1 DUP2 PUSH1 0x1 SHR SWAP1 PUSH1 0x1 DUP4 AND SWAP3 DUP4 ISZERO PUSH2 0x84A JUMPI JUMPDEST PUSH1 0x20 SWAP4 DUP5 DUP5 LT DUP2 EQ PUSH2 0x2DD JUMPI DUP4 DUP9 MSTORE SWAP1 DUP2 ISZERO PUSH2 0x82E JUMPI POP PUSH1 0x1 EQ PUSH2 0x7D8 JUMPI POP POP POP DUP3 SWAP1 SUB PUSH1 0x1F ADD PUSH1 0x1F NOT AND DUP3 ADD SWAP3 PUSH8 0xFFFFFFFFFFFFFFFF DUP5 GT DUP4 DUP6 LT OR PUSH2 0x259 JUMPI POP DUP3 SWAP2 DUP3 PUSH2 0x255 SWAP3 MSTORE DUP3 PUSH2 0x854 JUMP JUMPDEST PUSH1 0x3 DUP8 MSTORE SWAP2 SWAP3 POP DUP6 SWAP2 DUP4 PUSH32 0xC2575A0E9E593C00F959F8C92F12DB2869C3395A3B0502D05E2516446F71F85B JUMPDEST DUP4 DUP6 LT PUSH2 0x81A JUMPI POP POP POP POP DUP4 ADD ADD DUP6 DUP1 DUP1 PUSH2 0x223 JUMP JUMPDEST DUP1 SLOAD DUP9 DUP7 ADD DUP4 ADD MSTORE SWAP4 ADD SWAP3 DUP5 SWAP1 DUP3 ADD PUSH2 0x804 JUMP JUMPDEST PUSH1 0xFF NOT AND DUP8 DUP6 ADD MSTORE POP POP ISZERO ISZERO PUSH1 0x5 SHL DUP5 ADD ADD SWAP1 POP DUP6 DUP1 DUP1 PUSH2 0x223 JUMP JUMPDEST SWAP2 PUSH1 0x7F AND SWAP2 PUSH2 0x788 JUMP JUMPDEST PUSH1 0x20 DUP1 DUP3 MSTORE DUP3 MLOAD DUP2 DUP4 ADD DUP2 SWAP1 MSTORE SWAP1 SWAP4 SWAP3 PUSH1 0x0 JUMPDEST DUP3 DUP2 LT PUSH2 0x889 JUMPI POP POP PUSH1 0x40 SWAP3 SWAP4 POP PUSH1 0x0 DUP4 DUP3 DUP5 ADD ADD MSTORE PUSH1 0x1F DUP1 NOT SWAP2 ADD AND ADD ADD SWAP1 JUMP JUMPDEST DUP2 DUP2 ADD DUP7 ADD MLOAD DUP5 DUP3 ADD PUSH1 0x40 ADD MSTORE DUP6 ADD PUSH2 0x867 JUMP JUMPDEST PUSH1 0x4 CALLDATALOAD SWAP1 PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB DUP3 AND DUP3 SUB PUSH2 0x8B3 JUMPI JUMP JUMPDEST PUSH1 0x0 DUP1 REVERT JUMPDEST PUSH1 0x24 CALLDATALOAD SWAP1 PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB DUP3 AND DUP3 SUB PUSH2 0x8B3 JUMPI JUMP JUMPDEST SWAP2 SWAP1 DUP3 ADD DUP1 SWAP3 GT PUSH2 0x8DB JUMPI JUMP JUMPDEST PUSH4 0x4E487B71 PUSH1 0xE0 SHL PUSH1 0x0 MSTORE PUSH1 0x11 PUSH1 0x4 MSTORE PUSH1 0x24 PUSH1 0x0 REVERT JUMPDEST SWAP2 PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB DUP1 DUP5 AND SWAP3 DUP4 ISZERO PUSH2 0x9B6 JUMPI AND SWAP3 DUP4 ISZERO PUSH2 0x99D JUMPI PUSH1 0x0 SWAP1 DUP4 DUP3 MSTORE DUP2 PUSH1 0x20 MSTORE PUSH1 0x40 DUP3 KECCAK256 SLOAD SWAP1 DUP4 DUP3 LT PUSH2 0x96B JUMPI POP SWAP2 PUSH1 0x40 DUP3 DUP3 PUSH32 0xDDF252AD1BE2C89B69C2B068FC378DAA952BA7F163C4A11628F55A4DF523B3EF SWAP6 DUP8 PUSH1 0x20 SWAP7 MSTORE DUP3 DUP7 MSTORE SUB DUP3 DUP3 KECCAK256 SSTORE DUP7 DUP2 MSTORE KECCAK256 DUP2 DUP2 SLOAD ADD SWAP1 SSTORE PUSH1 0x40 MLOAD SWAP1 DUP2 MSTORE LOG3 JUMP JUMPDEST PUSH1 0x40 MLOAD PUSH4 0x391434E3 PUSH1 0xE2 SHL DUP2 MSTORE PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB SWAP2 SWAP1 SWAP2 AND PUSH1 0x4 DUP3 ADD MSTORE PUSH1 0x24 DUP2 ADD SWAP2 SWAP1 SWAP2 MSTORE PUSH1 0x44 DUP2 ADD DUP4 SWAP1 MSTORE PUSH1 0x64 SWAP1 REVERT JUMPDEST PUSH1 0x40 MLOAD PUSH4 0xEC442F05 PUSH1 0xE0 SHL DUP2 MSTORE PUSH1 0x0 PUSH1 0x4 DUP3 ADD MSTORE PUSH1 0x24 SWAP1 REVERT JUMPDEST PUSH1 0x40 MLOAD PUSH4 0x4B637E8F PUSH1 0xE1 SHL DUP2 MSTORE PUSH1 0x0 PUSH1 0x4 DUP3 ADD MSTORE PUSH1 0x24 SWAP1 REVERT JUMPDEST PUSH1 0x5 SLOAD PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB AND CALLER SUB PUSH2 0x9E3 JUMPI JUMP JUMPDEST PUSH1 0x40 MLOAD PUSH4 0x118CDAA7 PUSH1 0xE0 SHL DUP2 MSTORE CALLER PUSH1 0x4 DUP3 ADD MSTORE PUSH1 0x24 SWAP1 REVERT INVALID LOG2 PUSH5 0x6970667358 0x22 SLT KECCAK256 PUSH28 0x3B82A03B64588B9EAC3297C67004E117126AE04F6CBF2C3435D3586C DUP2 SWAP7 0x4B PUSH5 0x736F6C6343 STOP ADDMOD XOR STOP CALLER ", "sourceMap": "236:724:6:-:0;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;-1:-1:-1;;;;;236:724:6;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1648:13:2;236:724:6;;;;;;;;;;;;;;;;-1:-1:-1;236:724:6;;;;;;;;;;;;;;;-1:-1:-1;236:724:6;;;;;;;;;;-1:-1:-1;236:724:6;;;;-1:-1:-1;;;;236:724:6;;;;;;;;;;;;;;;;;;;;1671:17:2;236:724:6;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;236:724:6;;;;;;;;;;;;;;;;;;;1273:26:0;;1269:95;;3004:6;236:724:6;;-1:-1:-1;;;;;;236:724:6;;;;;;;;;;;;;3052:40:0;-1:-1:-1;;3052:40:0;323:20:6;573:27;;323:20;;236:724;6137:21:2;236:724:6;;;;;;;;;;-1:-1:-1;6137:21:2;236:724:6;-1:-1:-1;236:724:6;;;;;;;;;;;;;;;;;;;;-1:-1:-1;6987:25:2;;;236:724:6;;;;;;;;;;;;;;-1:-1:-1;236:724:6;;;-1:-1:-1;236:724:6;323:20;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;323:20:6;;;;;1269:95:0;236:724:6;;-1:-1:-1;;;1322:31:0;;-1:-1:-1;1322:31:0;;;236:724:6;;;1322:31:0;236:724:6;;;;-1:-1:-1;236:724:6;;;;;;;;;;;;;-1:-1:-1;236:724:6;;;-1:-1:-1;236:724:6;;-1:-1:-1;236:724:6;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;236:724:6;;;;;-1:-1:-1;236:724:6;;-1:-1:-1;236:724:6;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;236:724:6;;;;;;;;;;;;;;;;;;;;-1:-1:-1;236:724:6;;;-1:-1:-1;236:724:6;;;;;;;;;;;;-1:-1:-1;236:724:6;;1671:17:2;236:724:6;;-1:-1:-1;236:724:6;;;;;-1:-1:-1;236:724:6;;;;;-1:-1:-1;236:724:6;;;;;;;;-1:-1:-1;236:724:6;;;-1:-1:-1;;236:724:6;;;;;;;;;;;;;-1:-1:-1;236:724:6;;;;;;;;;;;;;;;;-1:-1:-1;;236:724:6;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;236:724:6;;-1:-1:-1;236:724:6;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;236:724:6;;;;-1:-1:-1;236:724:6;;;;;;;;;;;;;;;;-1:-1:-1;236:724:6;;;;;-1:-1:-1;236:724:6;;;;;;;;;-1:-1:-1;236:724:6;;;;;;;;;-1:-1:-1;;236:724:6;;;-1:-1:-1;;;;;236:724:6;;;;;;;;;;:::o;:::-;;;;;;;;;;;;;-1:-1:-1;;;;;236:724:6;;;;;;;;;;-1:-1:-1;;236:724:6;;;;:::i;:::-;;;;;;;;;;;;;-1:-1:-1;236:724:6;;;;;;;;-1:-1:-1;236:724:6;;;;;;;;:::o;:::-;;;;;;;;;;;;;;;;"}, "deployedBytecode": {"functionDebugData": {"abi_decode_address": {"entryPoint": 2205, "id": null, "parameterSlots": 0, "returnSlots": 1}, "abi_decode_address_4687": {"entryPoint": 2232, "id": null, "parameterSlots": 0, "returnSlots": 1}, "abi_encode_address_uint256_uint256": {"entryPoint": null, "id": null, "parameterSlots": 4, "returnSlots": 1}, "abi_encode_string": {"entryPoint": 2132, "id": null, "parameterSlots": 2, "returnSlots": 1}, "checked_add_uint256": {"entryPoint": 2254, "id": null, "parameterSlots": 2, "returnSlots": 1}, "fun_checkOwner": {"entryPoint": 2511, "id": 84, "parameterSlots": 0, "returnSlots": 0}, "fun_transfer": {"entryPoint": 2289, "id": 529, "parameterSlots": 3, "returnSlots": 0}}, "generatedSources": [], "immutableReferences": {}, "linkReferences": {}, "object": "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", "opcodes": "PUSH1 0x80 PUSH1 0x40 DUP2 DUP2 MSTORE PUSH1 0x4 DUP1 CALLDATASIZE LT ISZERO PUSH2 0x15 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST PUSH1 0x0 SWAP3 DUP4 CALLDATALOAD PUSH1 0xE0 SHR SWAP1 DUP2 PUSH4 0x6FDDE03 EQ PUSH2 0x75D JUMPI POP DUP1 PUSH4 0x95EA7B3 EQ PUSH2 0x6B4 JUMPI DUP1 PUSH4 0x18160DDD EQ PUSH2 0x695 JUMPI DUP1 PUSH4 0x23B872DD EQ PUSH2 0x5A2 JUMPI DUP1 PUSH4 0x313CE567 EQ PUSH2 0x586 JUMPI DUP1 PUSH4 0x32CB6B0C EQ PUSH2 0x560 JUMPI DUP1 PUSH4 0x40C10F19 EQ PUSH2 0x470 JUMPI DUP1 PUSH4 0x42966C68 EQ PUSH2 0x3BA JUMPI DUP1 PUSH4 0x70A08231 EQ PUSH2 0x383 JUMPI DUP1 PUSH4 0x715018A6 EQ PUSH2 0x323 JUMPI DUP1 PUSH4 0x8DA5CB5B EQ PUSH2 0x2FA JUMPI DUP1 PUSH4 0x95D89B41 EQ PUSH2 0x1D8 JUMPI DUP1 PUSH4 0xA9059CBB EQ PUSH2 0x1A7 JUMPI DUP1 PUSH4 0xDD62ED3E EQ PUSH2 0x15A JUMPI PUSH4 0xF2FDE38B EQ PUSH2 0xC8 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST CALLVALUE PUSH2 0x156 JUMPI PUSH1 0x20 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x156 JUMPI PUSH2 0xE1 PUSH2 0x89D JUMP JUMPDEST SWAP1 PUSH2 0xEA PUSH2 0x9CF JUMP JUMPDEST PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB SWAP2 DUP3 AND SWAP3 DUP4 ISZERO PUSH2 0x140 JUMPI POP POP PUSH1 0x5 SLOAD DUP3 PUSH12 0xFFFFFFFFFFFFFFFFFFFFFFFF PUSH1 0xA0 SHL DUP3 AND OR PUSH1 0x5 SSTORE AND PUSH32 0x8BE0079C531659141344CD1FD0A4F28419497F9722A3DAAFE3B4186F6B6457E0 DUP4 DUP1 LOG3 DUP1 RETURN JUMPDEST MLOAD PUSH4 0x1E4FBDF7 PUSH1 0xE0 SHL DUP2 MSTORE SWAP1 DUP2 ADD DUP5 SWAP1 MSTORE PUSH1 0x24 SWAP1 REVERT JUMPDEST DUP3 DUP1 REVERT JUMPDEST POP POP CALLVALUE PUSH2 0x1A3 JUMPI DUP1 PUSH1 0x3 NOT CALLDATASIZE ADD SLT PUSH2 0x1A3 JUMPI DUP1 PUSH1 0x20 SWAP3 PUSH2 0x178 PUSH2 0x89D JUMP JUMPDEST PUSH2 0x180 PUSH2 0x8B8 JUMP JUMPDEST PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB SWAP2 DUP3 AND DUP4 MSTORE PUSH1 0x1 DUP7 MSTORE DUP4 DUP4 KECCAK256 SWAP2 AND DUP3 MSTORE DUP5 MSTORE KECCAK256 SLOAD SWAP1 MLOAD SWAP1 DUP2 MSTORE RETURN JUMPDEST POP DUP1 REVERT JUMPDEST POP POP CALLVALUE PUSH2 0x1A3 JUMPI DUP1 PUSH1 0x3 NOT CALLDATASIZE ADD SLT PUSH2 0x1A3 JUMPI PUSH1 0x20 SWAP1 PUSH2 0x1D1 PUSH2 0x1C7 PUSH2 0x89D JUMP JUMPDEST PUSH1 0x24 CALLDATALOAD SWAP1 CALLER PUSH2 0x8F1 JUMP JUMPDEST MLOAD PUSH1 0x1 DUP2 MSTORE RETURN JUMPDEST POP SWAP2 SWAP1 CALLVALUE PUSH2 0x1A3 JUMPI DUP2 PUSH1 0x3 NOT CALLDATASIZE ADD SLT PUSH2 0x1A3 JUMPI DUP1 MLOAD SWAP1 DUP3 DUP5 SLOAD PUSH1 0x1 DUP2 PUSH1 0x1 SHR SWAP1 PUSH1 0x1 DUP4 AND SWAP3 DUP4 ISZERO PUSH2 0x2F0 JUMPI JUMPDEST PUSH1 0x20 SWAP4 DUP5 DUP5 LT DUP2 EQ PUSH2 0x2DD JUMPI DUP4 DUP9 MSTORE SWAP1 DUP2 ISZERO PUSH2 0x2C1 JUMPI POP PUSH1 0x1 EQ PUSH2 0x26C JUMPI JUMPDEST POP POP POP DUP3 SWAP1 SUB PUSH1 0x1F ADD PUSH1 0x1F NOT AND DUP3 ADD SWAP3 PUSH8 0xFFFFFFFFFFFFFFFF DUP5 GT DUP4 DUP6 LT OR PUSH2 0x259 JUMPI POP DUP3 SWAP2 DUP3 PUSH2 0x255 SWAP3 MSTORE DUP3 PUSH2 0x854 JUMP JUMPDEST SUB SWAP1 RETURN JUMPDEST PUSH4 0x4E487B71 PUSH1 0xE0 SHL DUP2 MSTORE PUSH1 0x41 DUP6 MSTORE PUSH1 0x24 SWAP1 REVERT JUMPDEST DUP8 DUP8 MSTORE SWAP2 SWAP3 POP DUP6 SWAP2 DUP4 PUSH32 0x8A35ACFBC15FF81A39AE7D344FD709F28E8600B4AA8C65C6B64BFE7FE36BD19B JUMPDEST DUP4 DUP6 LT PUSH2 0x2AD JUMPI POP POP POP POP DUP4 ADD ADD CODESIZE DUP1 DUP1 PUSH2 0x223 JUMP JUMPDEST DUP1 SLOAD DUP9 DUP7 ADD DUP4 ADD MSTORE SWAP4 ADD SWAP3 DUP5 SWAP1 DUP3 ADD PUSH2 0x297 JUMP JUMPDEST PUSH1 0xFF NOT AND DUP8 DUP6 ADD MSTORE POP POP ISZERO ISZERO PUSH1 0x5 SHL DUP5 ADD ADD SWAP1 POP CODESIZE DUP1 DUP1 PUSH2 0x223 JUMP JUMPDEST PUSH4 0x4E487B71 PUSH1 0xE0 SHL DUP10 MSTORE PUSH1 0x22 DUP11 MSTORE PUSH1 0x24 DUP10 REVERT JUMPDEST SWAP2 PUSH1 0x7F AND SWAP2 PUSH2 0x204 JUMP JUMPDEST POP POP CALLVALUE PUSH2 0x1A3 JUMPI DUP2 PUSH1 0x3 NOT CALLDATASIZE ADD SLT PUSH2 0x1A3 JUMPI PUSH1 0x5 SLOAD SWAP1 MLOAD PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB SWAP1 SWAP2 AND DUP2 MSTORE PUSH1 0x20 SWAP1 RETURN JUMPDEST DUP4 CALLVALUE PUSH2 0x380 JUMPI DUP1 PUSH1 0x3 NOT CALLDATASIZE ADD SLT PUSH2 0x380 JUMPI PUSH2 0x33C PUSH2 0x9CF JUMP JUMPDEST PUSH1 0x5 DUP1 SLOAD PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB NOT DUP2 AND SWAP1 SWAP2 SSTORE DUP2 SWAP1 PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB AND PUSH32 0x8BE0079C531659141344CD1FD0A4F28419497F9722A3DAAFE3B4186F6B6457E0 DUP3 DUP1 LOG3 DUP1 RETURN JUMPDEST DUP1 REVERT JUMPDEST POP POP CALLVALUE PUSH2 0x1A3 JUMPI PUSH1 0x20 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x1A3 JUMPI PUSH1 0x20 SWAP2 DUP2 SWAP1 PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB PUSH2 0x3AB PUSH2 0x89D JUMP JUMPDEST AND DUP2 MSTORE DUP1 DUP5 MSTORE KECCAK256 SLOAD SWAP1 MLOAD SWAP1 DUP2 MSTORE RETURN JUMPDEST POP SWAP2 SWAP1 CALLVALUE PUSH2 0x1A3 JUMPI PUSH1 0x20 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x1A3 JUMPI DUP3 CALLDATALOAD SWAP1 CALLER ISZERO PUSH2 0x45A JUMPI CALLER DUP4 MSTORE DUP3 PUSH1 0x20 MSTORE DUP1 DUP4 KECCAK256 SLOAD SWAP4 DUP3 DUP6 LT PUSH2 0x42F JUMPI POP DUP2 DUP4 SWAP5 CALLER DUP6 MSTORE DUP5 PUSH1 0x20 MSTORE SUB DUP2 DUP5 KECCAK256 SSTORE DUP2 PUSH1 0x2 SLOAD SUB PUSH1 0x2 SSTORE MLOAD SWAP1 DUP2 MSTORE PUSH32 0xDDF252AD1BE2C89B69C2B068FC378DAA952BA7F163C4A11628F55A4DF523B3EF PUSH1 0x20 CALLER SWAP3 LOG3 DUP1 RETURN JUMPDEST SWAP1 MLOAD PUSH4 0x391434E3 PUSH1 0xE2 SHL DUP2 MSTORE CALLER SWAP2 DUP2 ADD SWAP2 DUP3 MSTORE PUSH1 0x20 DUP3 ADD DUP6 SWAP1 MSTORE PUSH1 0x40 DUP3 ADD DUP4 SWAP1 MSTORE SWAP1 DUP2 SWAP1 PUSH1 0x60 ADD SUB SWAP1 REVERT JUMPDEST MLOAD PUSH4 0x4B637E8F PUSH1 0xE1 SHL DUP2 MSTORE DUP1 DUP5 ADD DUP4 SWAP1 MSTORE PUSH1 0x24 SWAP1 REVERT JUMPDEST POP SWAP1 CALLVALUE PUSH2 0x156 JUMPI DUP1 PUSH1 0x3 NOT CALLDATASIZE ADD SLT PUSH2 0x156 JUMPI PUSH2 0x48A PUSH2 0x89D JUMP JUMPDEST SWAP1 PUSH1 0x24 CALLDATALOAD SWAP2 PUSH2 0x497 PUSH2 0x9CF JUMP JUMPDEST PUSH1 0x2 SLOAD SWAP1 PUSH11 0x52B7D2DCC80CD2E4000000 PUSH2 0x4B1 DUP6 DUP5 PUSH2 0x8CE JUMP JUMPDEST GT PUSH2 0x528 JUMPI PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB AND SWAP4 DUP5 ISZERO PUSH2 0x511 JUMPI POP DUP3 PUSH32 0xDDF252AD1BE2C89B69C2B068FC378DAA952BA7F163C4A11628F55A4DF523B3EF SWAP3 PUSH2 0x4F7 DUP8 SWAP6 PUSH1 0x20 SWAP5 PUSH2 0x8CE JUMP JUMPDEST PUSH1 0x2 SSTORE DUP6 DUP6 MSTORE DUP5 DUP4 MSTORE DUP1 DUP6 KECCAK256 DUP3 DUP2 SLOAD ADD SWAP1 SSTORE MLOAD SWAP1 DUP2 MSTORE LOG3 DUP1 RETURN JUMPDEST DUP3 MLOAD PUSH4 0xEC442F05 PUSH1 0xE0 SHL DUP2 MSTORE SWAP1 DUP2 ADD DUP7 SWAP1 MSTORE PUSH1 0x24 SWAP1 REVERT JUMPDEST DUP3 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE PUSH1 0x20 DUP2 DUP8 ADD MSTORE PUSH1 0x12 PUSH1 0x24 DUP3 ADD MSTORE PUSH18 0x45786365656473206D617820737570706C79 PUSH1 0x70 SHL PUSH1 0x44 DUP3 ADD MSTORE PUSH1 0x64 SWAP1 REVERT JUMPDEST POP POP CALLVALUE PUSH2 0x1A3 JUMPI DUP2 PUSH1 0x3 NOT CALLDATASIZE ADD SLT PUSH2 0x1A3 JUMPI PUSH1 0x20 SWAP1 MLOAD PUSH11 0x52B7D2DCC80CD2E4000000 DUP2 MSTORE RETURN JUMPDEST POP POP CALLVALUE PUSH2 0x1A3 JUMPI DUP2 PUSH1 0x3 NOT CALLDATASIZE ADD SLT PUSH2 0x1A3 JUMPI PUSH1 0x20 SWAP1 MLOAD PUSH1 0x12 DUP2 MSTORE RETURN JUMPDEST POP DUP3 CALLVALUE PUSH2 0x380 JUMPI PUSH1 0x60 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x380 JUMPI PUSH2 0x5BD PUSH2 0x89D JUMP JUMPDEST PUSH2 0x5C5 PUSH2 0x8B8 JUMP JUMPDEST SWAP2 PUSH1 0x44 CALLDATALOAD SWAP4 PUSH1 0x1 DUP1 PUSH1 0xA0 SHL SUB DUP4 AND DUP1 DUP4 MSTORE PUSH1 0x1 PUSH1 0x20 MSTORE DUP7 DUP4 KECCAK256 CALLER DUP5 MSTORE PUSH1 0x20 MSTORE DUP7 DUP4 KECCAK256 SLOAD SWAP2 PUSH1 0x0 NOT DUP4 LT PUSH2 0x601 JUMPI JUMPDEST PUSH1 0x20 DUP9 PUSH2 0x1D1 DUP10 DUP10 DUP10 PUSH2 0x8F1 JUMP JUMPDEST DUP7 DUP4 LT PUSH2 0x669 JUMPI DUP2 ISZERO PUSH2 0x652 JUMPI CALLER ISZERO PUSH2 0x63B JUMPI POP DUP3 MSTORE PUSH1 0x1 PUSH1 0x20 SWAP1 DUP2 MSTORE DUP7 DUP4 KECCAK256 CALLER DUP5 MSTORE DUP2 MSTORE SWAP2 DUP7 SWAP1 KECCAK256 SWAP1 DUP6 SWAP1 SUB SWAP1 SSTORE DUP3 SWAP1 PUSH2 0x1D1 DUP8 PUSH2 0x5F3 JUMP JUMPDEST DUP8 MLOAD PUSH4 0x4A1406B1 PUSH1 0xE1 SHL DUP2 MSTORE SWAP1 DUP2 ADD DUP5 SWAP1 MSTORE PUSH1 0x24 SWAP1 REVERT JUMPDEST DUP8 MLOAD PUSH4 0xE602DF05 PUSH1 0xE0 SHL DUP2 MSTORE SWAP1 DUP2 ADD DUP5 SWAP1 MSTORE PUSH1 0x24 SWAP1 REVERT JUMPDEST DUP8 MLOAD PUSH4 0x7DC7A0D9 PUSH1 0xE1 SHL DUP2 MSTORE CALLER SWAP2 DUP2 ADD SWAP2 DUP3 MSTORE PUSH1 0x20 DUP3 ADD SWAP4 SWAP1 SWAP4 MSTORE PUSH1 0x40 DUP2 ADD DUP8 SWAP1 MSTORE DUP3 SWAP2 POP PUSH1 0x60 ADD SUB SWAP1 REVERT JUMPDEST POP POP CALLVALUE PUSH2 0x1A3 JUMPI DUP2 PUSH1 0x3 NOT CALLDATASIZE ADD SLT PUSH2 0x1A3 JUMPI PUSH1 0x20 SWAP1 PUSH1 0x2 SLOAD SWAP1 MLOAD SWAP1 DUP2 MSTORE RETURN JUMPDEST POP CALLVALUE PUSH2 0x156 JUMPI DUP2 PUSH1 0x3 NOT CALLDATASIZE ADD SLT PUSH2 0x156 JUMPI PUSH2 0x6CD PUSH2 0x89D JUMP JUMPDEST PUSH1 0x24 CALLDATALOAD SWAP1 CALLER ISZERO PUSH2 0x746 JUMPI PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB AND SWAP2 DUP3 ISZERO PUSH2 0x72F JUMPI POP DUP1 DUP4 PUSH1 0x20 SWAP6 CALLER DUP2 MSTORE PUSH1 0x1 DUP8 MSTORE DUP2 DUP2 KECCAK256 DUP6 DUP3 MSTORE DUP8 MSTORE KECCAK256 SSTORE DUP3 MLOAD SWAP1 DUP2 MSTORE PUSH32 0x8C5BE1E5EBEC7D5BD14F71427D1E84F3DD0314C0F7B2291E5B200AC8C7C3B925 DUP5 CALLER SWAP3 LOG3 MLOAD PUSH1 0x1 DUP2 MSTORE RETURN JUMPDEST DUP4 MLOAD PUSH4 0x4A1406B1 PUSH1 0xE1 SHL DUP2 MSTORE SWAP1 DUP2 ADD DUP6 SWAP1 MSTORE PUSH1 0x24 SWAP1 REVERT JUMPDEST DUP4 MLOAD PUSH4 0xE602DF05 PUSH1 0xE0 SHL DUP2 MSTORE DUP1 DUP5 ADD DUP7 SWAP1 MSTORE PUSH1 0x24 SWAP1 REVERT JUMPDEST DUP5 SWAP2 POP DUP4 CALLVALUE PUSH2 0x156 JUMPI DUP3 PUSH1 0x3 NOT CALLDATASIZE ADD SLT PUSH2 0x156 JUMPI DUP3 PUSH1 0x3 SLOAD PUSH1 0x1 DUP2 PUSH1 0x1 SHR SWAP1 PUSH1 0x1 DUP4 AND SWAP3 DUP4 ISZERO PUSH2 0x84A JUMPI JUMPDEST PUSH1 0x20 SWAP4 DUP5 DUP5 LT DUP2 EQ PUSH2 0x2DD JUMPI DUP4 DUP9 MSTORE SWAP1 DUP2 ISZERO PUSH2 0x82E JUMPI POP PUSH1 0x1 EQ PUSH2 0x7D8 JUMPI POP POP POP DUP3 SWAP1 SUB PUSH1 0x1F ADD PUSH1 0x1F NOT AND DUP3 ADD SWAP3 PUSH8 0xFFFFFFFFFFFFFFFF DUP5 GT DUP4 DUP6 LT OR PUSH2 0x259 JUMPI POP DUP3 SWAP2 DUP3 PUSH2 0x255 SWAP3 MSTORE DUP3 PUSH2 0x854 JUMP JUMPDEST PUSH1 0x3 DUP8 MSTORE SWAP2 SWAP3 POP DUP6 SWAP2 DUP4 PUSH32 0xC2575A0E9E593C00F959F8C92F12DB2869C3395A3B0502D05E2516446F71F85B JUMPDEST DUP4 DUP6 LT PUSH2 0x81A JUMPI POP POP POP POP DUP4 ADD ADD DUP6 DUP1 DUP1 PUSH2 0x223 JUMP JUMPDEST DUP1 SLOAD DUP9 DUP7 ADD DUP4 ADD MSTORE SWAP4 ADD SWAP3 DUP5 SWAP1 DUP3 ADD PUSH2 0x804 JUMP JUMPDEST PUSH1 0xFF NOT AND DUP8 DUP6 ADD MSTORE POP POP ISZERO ISZERO PUSH1 0x5 SHL DUP5 ADD ADD SWAP1 POP DUP6 DUP1 DUP1 PUSH2 0x223 JUMP JUMPDEST SWAP2 PUSH1 0x7F AND SWAP2 PUSH2 0x788 JUMP JUMPDEST PUSH1 0x20 DUP1 DUP3 MSTORE DUP3 MLOAD DUP2 DUP4 ADD DUP2 SWAP1 MSTORE SWAP1 SWAP4 SWAP3 PUSH1 0x0 JUMPDEST DUP3 DUP2 LT PUSH2 0x889 JUMPI POP POP PUSH1 0x40 SWAP3 SWAP4 POP PUSH1 0x0 DUP4 DUP3 DUP5 ADD ADD MSTORE PUSH1 0x1F DUP1 NOT SWAP2 ADD AND ADD ADD SWAP1 JUMP JUMPDEST DUP2 DUP2 ADD DUP7 ADD MLOAD DUP5 DUP3 ADD PUSH1 0x40 ADD MSTORE DUP6 ADD PUSH2 0x867 JUMP JUMPDEST PUSH1 0x4 CALLDATALOAD SWAP1 PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB DUP3 AND DUP3 SUB PUSH2 0x8B3 JUMPI JUMP JUMPDEST PUSH1 0x0 DUP1 REVERT JUMPDEST PUSH1 0x24 CALLDATALOAD SWAP1 PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB DUP3 AND DUP3 SUB PUSH2 0x8B3 JUMPI JUMP JUMPDEST SWAP2 SWAP1 DUP3 ADD DUP1 SWAP3 GT PUSH2 0x8DB JUMPI JUMP JUMPDEST PUSH4 0x4E487B71 PUSH1 0xE0 SHL PUSH1 0x0 MSTORE PUSH1 0x11 PUSH1 0x4 MSTORE PUSH1 0x24 PUSH1 0x0 REVERT JUMPDEST SWAP2 PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB DUP1 DUP5 AND SWAP3 DUP4 ISZERO PUSH2 0x9B6 JUMPI AND SWAP3 DUP4 ISZERO PUSH2 0x99D JUMPI PUSH1 0x0 SWAP1 DUP4 DUP3 MSTORE DUP2 PUSH1 0x20 MSTORE PUSH1 0x40 DUP3 KECCAK256 SLOAD SWAP1 DUP4 DUP3 LT PUSH2 0x96B JUMPI POP SWAP2 PUSH1 0x40 DUP3 DUP3 PUSH32 0xDDF252AD1BE2C89B69C2B068FC378DAA952BA7F163C4A11628F55A4DF523B3EF SWAP6 DUP8 PUSH1 0x20 SWAP7 MSTORE DUP3 DUP7 MSTORE SUB DUP3 DUP3 KECCAK256 SSTORE DUP7 DUP2 MSTORE KECCAK256 DUP2 DUP2 SLOAD ADD SWAP1 SSTORE PUSH1 0x40 MLOAD SWAP1 DUP2 MSTORE LOG3 JUMP JUMPDEST PUSH1 0x40 MLOAD PUSH4 0x391434E3 PUSH1 0xE2 SHL DUP2 MSTORE PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB SWAP2 SWAP1 SWAP2 AND PUSH1 0x4 DUP3 ADD MSTORE PUSH1 0x24 DUP2 ADD SWAP2 SWAP1 SWAP2 MSTORE PUSH1 0x44 DUP2 ADD DUP4 SWAP1 MSTORE PUSH1 0x64 SWAP1 REVERT JUMPDEST PUSH1 0x40 MLOAD PUSH4 0xEC442F05 PUSH1 0xE0 SHL DUP2 MSTORE PUSH1 0x0 PUSH1 0x4 DUP3 ADD MSTORE PUSH1 0x24 SWAP1 REVERT JUMPDEST PUSH1 0x40 MLOAD PUSH4 0x4B637E8F PUSH1 0xE1 SHL DUP2 MSTORE PUSH1 0x0 PUSH1 0x4 DUP3 ADD MSTORE PUSH1 0x24 SWAP1 REVERT JUMPDEST PUSH1 0x5 SLOAD PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB AND CALLER SUB PUSH2 0x9E3 JUMPI JUMP JUMPDEST PUSH1 0x40 MLOAD PUSH4 0x118CDAA7 PUSH1 0xE0 SHL DUP2 MSTORE CALLER PUSH1 0x4 DUP3 ADD MSTORE PUSH1 0x24 SWAP1 REVERT INVALID LOG2 PUSH5 0x6970667358 0x22 SLT KECCAK256 PUSH28 0x3B82A03B64588B9EAC3297C67004E117126AE04F6CBF2C3435D3586C DUP2 SWAP7 0x4B PUSH5 0x736F6C6343 STOP ADDMOD XOR STOP CALLER ", "sourceMap": "236:724:6:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;236:724:6;;;;;;:::i;:::-;1500:62:0;;;:::i;:::-;-1:-1:-1;;;;;236:724:6;;;;2627:22:0;;2623:91;;236:724:6;;3004:6:0;236:724:6;;;;;;;;3004:6:0;236:724:6;;3052:40:0;;;;236:724:6;;2623:91:0;236:724:6;-1:-1:-1;;;2672:31:0;;;;;236:724:6;;;;;2672:31:0;236:724:6;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;:::i;:::-;-1:-1:-1;;;;;236:724:6;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3388:5:2;236:724:6;;:::i;:::-;;;735:10:5;;3388:5:2;:::i;:::-;236:724:6;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;236:724:6;;;;;-1:-1:-1;;236:724:6;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;-1:-1:-1;;;236:724:6;;;;;;;;;;;;;;-1:-1:-1;236:724:6;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;236:724:6;;;;;-1:-1:-1;;236:724:6;;;;;;;;-1:-1:-1;236:724:6;;;;;;-1:-1:-1;;;236:724:6;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1710:6:0;236:724:6;;;-1:-1:-1;;;;;236:724:6;;;;;;;;;;;;;;;;;;;;;1500:62:0;;:::i;:::-;3004:6;236:724:6;;-1:-1:-1;;;;;;236:724:6;;;;;;;-1:-1:-1;;;;;236:724:6;3052:40:0;236:724:6;;3052:40:0;236:724:6;;;;;;;;;;;;;-1:-1:-1;;236:724:6;;;;;;;;-1:-1:-1;;;;;236:724:6;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;236:724:6;;;;;;932:10;;7958:21:2;7954:89;;932:10:6;236:724;;;;;;;;;6244:19:2;;;;6240:115;;932:10:6;;;;;236:724;;;;;;;;;;;6714:21:2;236:724:6;;6714:21:2;236:724:6;;;;;6987:25:2;236:724:6;932:10;6987:25:2;;236:724:6;;6240:115:2;236:724:6;;-1:-1:-1;;;6290:50:2;;932:10:6;6290:50:2;;;236:724:6;;;;;;;;;;;;;;;;;;;;6290:50:2;;;7954:89;236:724:6;-1:-1:-1;;;8002:30:2;;;;;236:724:6;;;;;8002:30:2;236:724:6;;;;;;;;;;;;;;;;:::i;:::-;;;;1500:62:0;;;:::i;:::-;2881:12:2;236:724:6;776:22;323:20;776:22;;;;:::i;:::-;:36;236:724;;-1:-1:-1;;;;;236:724:6;;7432:21:2;;7428:91;;6137:21;;6987:25;6137:21;;;;236:724:6;6137:21:2;;:::i;:::-;2881:12;236:724:6;;;;;;;;;;;;;;;;;;;;6987:25:2;236:724:6;;7428:91:2;236:724:6;;-1:-1:-1;;;7476:32:2;;;;;236:724:6;;;;;7476:32:2;236:724:6;;;-1:-1:-1;;;236:724:6;;;;;;;;;;;;-1:-1:-1;;;236:724:6;;;;;;;;;;;;;;;;;;;;;;;;323:20;236:724;;;;;;;;;;;;;;;;;;;;2761:2:2;236:724:6;;;;;;;;;;;-1:-1:-1;;236:724:6;;;;;;:::i;:::-;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;735:10:5;236:724:6;;;;;;;;10503:17:2;;;10484:36;;10480:309;;236:724:6;;4890:5:2;;;;;;:::i;10480:309::-;10540:24;;;10536:130;;9717:19;;9713:89;;735:10:5;9815:21:2;9811:90;;-1:-1:-1;236:724:6;;;;;;;;;;735:10:5;236:724:6;;;;;;;;;;;;;;;;4890:5:2;10480:309;;;9811:90;236:724:6;;-1:-1:-1;;;9859:31:2;;;;;236:724:6;;;;;9859:31:2;9713:89;236:724:6;;-1:-1:-1;;;9759:32:2;;;;;236:724:6;;;;;9759:32:2;10536:130;236:724:6;;-1:-1:-1;;;10591:60:2;;735:10:5;10591:60:2;;;236:724:6;;;;;;;;;;;;;;;;;;-1:-1:-1;236:724:6;;6290:50:2;;;236:724:6;;;;;;;;;;;;;;;;2881:12:2;236:724:6;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;735:10:5;;9717:19:2;9713:89;;-1:-1:-1;;;;;236:724:6;;9815:21:2;;9811:90;;735:10:5;;;236:724:6;735:10:5;;236:724:6;;8746:4:2;236:724:6;;;;;;;;;;;;;;;;;9989:31:2;735:10:5;;9989:31:2;;236:724:6;8746:4:2;236:724:6;;;9811:90:2;236:724:6;;-1:-1:-1;;;9859:31:2;;;;;236:724:6;;;;;9859:31:2;9713:89;236:724:6;;-1:-1:-1;;;9759:32:2;;;;;236:724:6;;;;;9759:32:2;236:724:6;;;;;;;;;;;;;;;;;1837:5:2;236:724:6;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;236:724:6;;;;;-1:-1:-1;;236:724:6;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;1837:5:2;236:724:6;;;;-1:-1:-1;236:724:6;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;236:724:6;;;;;-1:-1:-1;;236:724:6;;;;;;;;-1:-1:-1;236:724:6;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;236:724:6;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;236:724:6;;;;;;:::o;:::-;;;;;;;;-1:-1:-1;;;;;236:724:6;;;;;;:::o;:::-;;;;;;;;;;:::o;:::-;;;;;;;;;;;;5297:300:2;;-1:-1:-1;;;;;236:724:6;;;;5380:18:2;;5376:86;;236:724:6;5475:16:2;;;5471:86;;5997:540;236:724:6;;;;;;;;;;;6244:19:2;;;;6240:115;;236:724:6;;;;;6987:25:2;236:724:6;;;;;;;;;;;;;;;;;;;;;;;;;;;;6987:25:2;5297:300::o;6240:115::-;236:724:6;;-1:-1:-1;;;6290:50:2;;-1:-1:-1;;;;;236:724:6;;;;6290:50:2;;;236:724:6;;;;;;;;;;;;;;;;6290:50:2;5471:86;236:724:6;;-1:-1:-1;;;5514:32:2;;5396:1;5514:32;;;236:724:6;;;5514:32:2;5376:86;236:724:6;;-1:-1:-1;;;5421:30:2;;5396:1;5421:30;;;236:724:6;;;5421:30:2;1796:162:0;1710:6;236:724:6;-1:-1:-1;;;;;236:724:6;735:10:5;1855:23:0;1851:101;;1796:162::o;1851:101::-;236:724:6;;-1:-1:-1;;;1901:40:0;;735:10:5;1901:40:0;;;236:724:6;;;1901:40:0"}, "methodIdentifiers": {"MAX_SUPPLY()": "32cb6b0c", "allowance(address,address)": "dd62ed3e", "approve(address,uint256)": "095ea7b3", "balanceOf(address)": "70a08231", "burn(uint256)": "42966c68", "decimals()": "313ce567", "mint(address,uint256)": "40c10f19", "name()": "06fdde03", "owner()": "8da5cb5b", "renounceOwnership()": "715018a6", "symbol()": "95d89b41", "totalSupply()": "18160ddd", "transfer(address,uint256)": "a9059cbb", "transferFrom(address,address,uint256)": "23b872dd", "transferOwnership(address)": "f2fde38b"}}, "metadata": "{\"compiler\":{\"version\":\"0.8.24+commit.e11b9ed9\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"string\",\"name\":\"name\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"symbol\",\"type\":\"string\"},{\"internalType\":\"uint256\",\"name\":\"initialSupply\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"initialOwner\",\"type\":\"address\"}],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"allowance\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"needed\",\"type\":\"uint256\"}],\"name\":\"ERC20InsufficientAllowance\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"balance\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"needed\",\"type\":\"uint256\"}],\"name\":\"ERC20InsufficientBalance\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"approver\",\"type\":\"address\"}],\"name\":\"ERC20InvalidApprover\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"}],\"name\":\"ERC20InvalidReceiver\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"}],\"name\":\"ERC20InvalidSender\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"}],\"name\":\"ERC20InvalidSpender\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"name\":\"OwnableInvalidOwner\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"OwnableUnauthorizedAccount\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"Approval\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"previousOwner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"OwnershipTransferred\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"Transfer\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"MAX_SUPPLY\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"}],\"name\":\"allowance\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"approve\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"balanceOf\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"burn\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"decimals\",\"outputs\":[{\"internalType\":\"uint8\",\"name\":\"\",\"type\":\"uint8\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"mint\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"name\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"owner\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"renounceOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"symbol\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"totalSupply\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"transfer\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"transferFrom\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"transferOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"details\":\"\\u7b80\\u5316\\u7684 ERC-20 \\u4ee3\\u5e01\\u5b9e\\u73b0\",\"errors\":{\"ERC20InsufficientAllowance(address,uint256,uint256)\":[{\"details\":\"Indicates a failure with the `spender`\\u2019s `allowance`. Used in transfers.\",\"params\":{\"allowance\":\"Amount of tokens a `spender` is allowed to operate with.\",\"needed\":\"Minimum amount required to perform a transfer.\",\"spender\":\"Address that may be allowed to operate on tokens without being their owner.\"}}],\"ERC20InsufficientBalance(address,uint256,uint256)\":[{\"details\":\"Indicates an error related to the current `balance` of a `sender`. Used in transfers.\",\"params\":{\"balance\":\"Current balance for the interacting account.\",\"needed\":\"Minimum amount required to perform a transfer.\",\"sender\":\"Address whose tokens are being transferred.\"}}],\"ERC20InvalidApprover(address)\":[{\"details\":\"Indicates a failure with the `approver` of a token to be approved. Used in approvals.\",\"params\":{\"approver\":\"Address initiating an approval operation.\"}}],\"ERC20InvalidReceiver(address)\":[{\"details\":\"Indicates a failure with the token `receiver`. Used in transfers.\",\"params\":{\"receiver\":\"Address to which tokens are being transferred.\"}}],\"ERC20InvalidSender(address)\":[{\"details\":\"Indicates a failure with the token `sender`. Used in transfers.\",\"params\":{\"sender\":\"Address whose tokens are being transferred.\"}}],\"ERC20InvalidSpender(address)\":[{\"details\":\"Indicates a failure with the `spender` to be approved. Used in approvals.\",\"params\":{\"spender\":\"Address that may be allowed to operate on tokens without being their owner.\"}}],\"OwnableInvalidOwner(address)\":[{\"details\":\"The owner is not a valid owner account. (eg. `address(0)`)\"}],\"OwnableUnauthorizedAccount(address)\":[{\"details\":\"The caller account is not authorized to perform an operation.\"}]},\"events\":{\"Approval(address,address,uint256)\":{\"details\":\"Emitted when the allowance of a `spender` for an `owner` is set by a call to {approve}. `value` is the new allowance.\"},\"Transfer(address,address,uint256)\":{\"details\":\"Emitted when `value` tokens are moved from one account (`from`) to another (`to`). Note that `value` may be zero.\"}},\"kind\":\"dev\",\"methods\":{\"allowance(address,address)\":{\"details\":\"Returns the remaining number of tokens that `spender` will be allowed to spend on behalf of `owner` through {transferFrom}. This is zero by default. This value changes when {approve} or {transferFrom} are called.\"},\"approve(address,uint256)\":{\"details\":\"See {IERC20-approve}. NOTE: If `value` is the maximum `uint256`, the allowance is not updated on `transferFrom`. This is semantically equivalent to an infinite approval. Requirements: - `spender` cannot be the zero address.\"},\"balanceOf(address)\":{\"details\":\"Returns the value of tokens owned by `account`.\"},\"decimals()\":{\"details\":\"Returns the number of decimals used to get its user representation. For example, if `decimals` equals `2`, a balance of `505` tokens should be displayed to a user as `5.05` (`505 / 10 ** 2`). Tokens usually opt for a value of 18, imitating the relationship between Ether and Wei. This is the default value returned by this function, unless it's overridden. NOTE: This information is only used for _display_ purposes: it in no way affects any of the arithmetic of the contract, including {IERC20-balanceOf} and {IERC20-transfer}.\"},\"name()\":{\"details\":\"Returns the name of the token.\"},\"owner()\":{\"details\":\"Returns the address of the current owner.\"},\"renounceOwnership()\":{\"details\":\"Leaves the contract without owner. It will not be possible to call `onlyOwner` functions. Can only be called by the current owner. NOTE: Renouncing ownership will leave the contract without an owner, thereby disabling any functionality that is only available to the owner.\"},\"symbol()\":{\"details\":\"Returns the symbol of the token, usually a shorter version of the name.\"},\"totalSupply()\":{\"details\":\"Returns the value of tokens in existence.\"},\"transfer(address,uint256)\":{\"details\":\"See {IERC20-transfer}. Requirements: - `to` cannot be the zero address. - the caller must have a balance of at least `value`.\"},\"transferFrom(address,address,uint256)\":{\"details\":\"See {IERC20-transferFrom}. Skips emitting an {Approval} event indicating an allowance update. This is not required by the ERC. See {xref-ERC20-_approve-address-address-uint256-bool-}[_approve]. NOTE: Does not update the allowance if the current allowance is the maximum `uint256`. Requirements: - `from` and `to` cannot be the zero address. - `from` must have a balance of at least `value`. - the caller must have allowance for ``from``'s tokens of at least `value`.\"},\"transferOwnership(address)\":{\"details\":\"Transfers ownership of the contract to a new account (`newOwner`). Can only be called by the current owner.\"}},\"title\":\"SimpleToken\",\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"contracts/tokens/SimpleToken.sol\":\"SimpleToken\"},\"evmVersion\":\"paris\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[],\"viaIR\":true},\"sources\":{\"@openzeppelin/contracts/access/Ownable.sol\":{\"keccak256\":\"0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8ed324d3920bb545059d66ab97d43e43ee85fd3bd52e03e401f020afb0b120f6\",\"dweb:/ipfs/QmfEckWLmZkDDcoWrkEvMWhms66xwTLff9DDhegYpvHo1a\"]},\"@openzeppelin/contracts/interfaces/draft-IERC6093.sol\":{\"keccak256\":\"0x19fdfb0f3b89a230e7dbd1cf416f1a6b531a3ee5db4da483f946320fc74afc0e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3490d794728f5bfecb46820431adaff71ba374141545ec20b650bb60353fac23\",\"dweb:/ipfs/QmPsfxjVpMcZbpE7BH93DzTpEaktESigEw4SmDzkXuJ4WR\"]},\"@openzeppelin/contracts/token/ERC20/ERC20.sol\":{\"keccak256\":\"0x86b7b71a6aedefdad89b607378eeab1dcc5389b9ea7d17346d08af01d7190994\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1dc2db8d94a21eac8efe03adf574c419b08536409b416057a2b5b95cb772c43c\",\"dweb:/ipfs/QmZfqJCKVU1ScuX2A7s8WZdQEaikwJbDH5JBrBdKTUT4Gu\"]},\"@openzeppelin/contracts/token/ERC20/IERC20.sol\":{\"keccak256\":\"0x74ed01eb66b923d0d0cfe3be84604ac04b76482a55f9dd655e1ef4d367f95bc2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5282825a626cfe924e504274b864a652b0023591fa66f06a067b25b51ba9b303\",\"dweb:/ipfs/QmeCfPykghhMc81VJTrHTC7sF6CRvaA1FXVq2pJhwYp1dV\"]},\"@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol\":{\"keccak256\":\"0xd6fa4088198f04eef10c5bce8a2f4d60554b7ec4b987f684393c01bf79b94d9f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f95ee0bbd4dd3ac730d066ba3e785ded4565e890dbec2fa7d3b9fe3bad9d0d6e\",\"dweb:/ipfs/QmSLr6bHkPFWT7ntj34jmwfyskpwo97T9jZUrk5sz3sdtR\"]},\"@openzeppelin/contracts/utils/Context.sol\":{\"keccak256\":\"0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12\",\"dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF\"]},\"contracts/tokens/SimpleToken.sol\":{\"keccak256\":\"0x71b343b9229fee6d8212f873331d432f7f8904ad0c44d2f899bb61ec764b0181\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ebb00344d7f5f1d48a64dab8c130a4e6ac0951bdc67bb94ad20063c5826e986a\",\"dweb:/ipfs/QmSbtPsFAxdx2ETDujGRoFyoYWkw9hFWuhBoCiNZ6mtgDz\"]}},\"version\":1}"}}}}}