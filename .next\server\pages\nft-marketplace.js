/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/nft-marketplace";
exports.ids = ["pages/nft-marketplace"];
exports.modules = {

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fnft-marketplace&preferredRegion=&absolutePagePath=.%2Fpages%5Cnft-marketplace.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fnft-marketplace&preferredRegion=&absolutePagePath=.%2Fpages%5Cnft-marketplace.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./pages/_app.js\");\n/* harmony import */ var _pages_nft_marketplace_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./pages\\nft-marketplace.js */ \"./pages/nft-marketplace.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__, _pages_nft_marketplace_js__WEBPACK_IMPORTED_MODULE_5__]);\n([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__, _pages_nft_marketplace_js__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_nft_marketplace_js__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_nft_marketplace_js__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_nft_marketplace_js__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_nft_marketplace_js__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_nft_marketplace_js__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_nft_marketplace_js__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_nft_marketplace_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_nft_marketplace_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_nft_marketplace_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_nft_marketplace_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_nft_marketplace_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/nft-marketplace\",\n        pathname: \"/nft-marketplace\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default())\n    },\n    userland: _pages_nft_marketplace_js__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fnft-marketplace&preferredRegion=&absolutePagePath=.%2Fpages%5Cnft-marketplace.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./pages/_app.js":
/*!***********************!*\
  !*** ./pages/_app.js ***!
  \***********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App),\n/* harmony export */   useWeb3: () => (/* binding */ useWeb3)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _frontend_styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../frontend/styles/globals.css */ \"./frontend/styles/globals.css\");\n/* harmony import */ var _frontend_styles_globals_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_frontend_styles_globals_css__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ethers */ \"ethers\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([ethers__WEBPACK_IMPORTED_MODULE_3__]);\nethers__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n// Web3 上下文\n\nconst Web3Context = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_2__.createContext)();\nconst useWeb3 = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_2__.useContext)(Web3Context);\n    if (!context) {\n        throw new Error(\"useWeb3 must be used within a Web3Provider\");\n    }\n    return context;\n};\nfunction Web3Provider({ children }) {\n    const [account, setAccount] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [provider, setProvider] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [signer, setSigner] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [chainId, setChainId] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [isConnecting, setIsConnecting] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // 连接钱包\n    const connectWallet = async ()=>{\n        if (typeof window.ethereum === \"undefined\") {\n            alert(\"请安装 MetaMask!\");\n            return;\n        }\n        setIsConnecting(true);\n        try {\n            // 请求账户访问\n            await window.ethereum.request({\n                method: \"eth_requestAccounts\"\n            });\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_3__.ethers.BrowserProvider(window.ethereum);\n            const signer = await provider.getSigner();\n            const address = await signer.getAddress();\n            const network = await provider.getNetwork();\n            setProvider(provider);\n            setSigner(signer);\n            setAccount(address);\n            setChainId(Number(network.chainId));\n            console.log(\"钱包连接成功:\", address);\n        } catch (error) {\n            console.error(\"连接钱包失败:\", error);\n            alert(\"连接钱包失败: \" + error.message);\n        } finally{\n            setIsConnecting(false);\n        }\n    };\n    // 断开钱包连接\n    const disconnectWallet = ()=>{\n        setAccount(null);\n        setProvider(null);\n        setSigner(null);\n        setChainId(null);\n    };\n    // 监听账户变化\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (typeof window.ethereum !== \"undefined\") {\n            window.ethereum.on(\"accountsChanged\", (accounts)=>{\n                if (accounts.length === 0) {\n                    disconnectWallet();\n                } else {\n                    connectWallet();\n                }\n            });\n            window.ethereum.on(\"chainChanged\", (chainId)=>{\n                setChainId(parseInt(chainId, 16));\n            });\n            // 检查是否已经连接\n            window.ethereum.request({\n                method: \"eth_accounts\"\n            }).then((accounts)=>{\n                if (accounts.length > 0) {\n                    connectWallet();\n                }\n            });\n        }\n        return ()=>{\n            if (typeof window.ethereum !== \"undefined\") {\n                window.ethereum.removeAllListeners(\"accountsChanged\");\n                window.ethereum.removeAllListeners(\"chainChanged\");\n            }\n        };\n    }, []);\n    const value = {\n        account,\n        provider,\n        signer,\n        chainId,\n        isConnecting,\n        connectWallet,\n        disconnectWallet,\n        isConnected: !!account\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Web3Context.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\_app.js\",\n        lineNumber: 108,\n        columnNumber: 5\n    }, this);\n}\nfunction App({ Component, pageProps }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Web3Provider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                ...pageProps\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\_app.js\",\n                lineNumber: 118,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\_app.js\",\n            lineNumber: 117,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\_app.js\",\n        lineNumber: 116,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/_app.js\n");

/***/ }),

/***/ "./pages/nft-marketplace.js":
/*!**********************************!*\
  !*** ./pages/nft-marketplace.js ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NFTMarketplace)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"next/head\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _app__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./_app */ \"./pages/_app.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ethers */ \"ethers\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_app__WEBPACK_IMPORTED_MODULE_3__, ethers__WEBPACK_IMPORTED_MODULE_4__]);\n([_app__WEBPACK_IMPORTED_MODULE_3__, ethers__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\nconst NFT_ABI = [\n    \"function name() view returns (string)\",\n    \"function symbol() view returns (string)\",\n    \"function tokenURI(uint256) view returns (string)\",\n    \"function ownerOf(uint256) view returns (address)\",\n    \"function balanceOf(address) view returns (uint256)\",\n    \"function tokenOfOwnerByIndex(address, uint256) view returns (uint256)\",\n    \"function mint(uint256) payable\",\n    \"function mintPrice() view returns (uint256)\",\n    \"function totalSupply() view returns (uint256)\",\n    \"function approve(address, uint256)\",\n    \"function setApprovalForAll(address, bool)\",\n    \"function isApprovedForAll(address, address) view returns (bool)\"\n];\nconst MARKETPLACE_ABI = [\n    \"function listItem(address, uint256, uint256)\",\n    \"function buyItem(bytes32) payable\",\n    \"function createAuction(address, uint256, uint256, uint256)\",\n    \"function placeBid(bytes32) payable\",\n    \"function endAuction(bytes32)\",\n    \"function withdraw()\",\n    \"function cancelListing(bytes32)\",\n    \"function listings(bytes32) view returns (address, address, uint256, uint256, bool, uint256)\",\n    \"function auctions(bytes32) view returns (address, address, uint256, uint256, uint256, address, uint256, bool, bool)\",\n    \"function getActiveListings() view returns (bytes32[])\",\n    \"function getActiveAuctions() view returns (bytes32[])\",\n    \"function pendingReturns(address) view returns (uint256)\"\n];\nfunction NFTMarketplace() {\n    const { account, signer, isConnected } = (0,_app__WEBPACK_IMPORTED_MODULE_3__.useWeb3)();\n    const [nftContract, setNftContract] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [marketplaceContract, setMarketplaceContract] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [myNFTs, setMyNFTs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [listings, setListings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [auctions, setAuctions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"mint\");\n    // 表单状态\n    const [mintQuantity, setMintQuantity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [listPrice, setListPrice] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedNFT, setSelectedNFT] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [auctionStartPrice, setAuctionStartPrice] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [auctionDuration, setAuctionDuration] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"24\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isConnected && signer) {\n            initializeContracts();\n        }\n    }, [\n        isConnected,\n        signer\n    ]);\n    const initializeContracts = async ()=>{\n        try {\n            // 使用配置文件中的合约地址\n            const contractsConfig = await __webpack_require__.e(/*! import() */ \"frontend_config_contracts_json\").then(__webpack_require__.t.bind(__webpack_require__, /*! ../frontend/config/contracts.json */ \"./frontend/config/contracts.json\", 19));\n            const nftAddress = contractsConfig.contracts.Web3NFT;\n            const marketplaceAddress = contractsConfig.contracts.NFTMarketplace;\n            const nft = new ethers__WEBPACK_IMPORTED_MODULE_4__.ethers.Contract(nftAddress, NFT_ABI, signer);\n            const marketplace = new ethers__WEBPACK_IMPORTED_MODULE_4__.ethers.Contract(marketplaceAddress, MARKETPLACE_ABI, signer);\n            setNftContract(nft);\n            setMarketplaceContract(marketplace);\n            loadData(nft, marketplace);\n        } catch (error) {\n            console.error(\"初始化合约失败:\", error);\n        }\n    };\n    const loadData = async (nft, marketplace)=>{\n        setLoading(true);\n        try {\n            // 加载我的 NFTs\n            await loadMyNFTs(nft);\n            // 加载市场列表\n            await loadMarketListings(marketplace, nft);\n            // 加载拍卖\n            await loadMarketAuctions(marketplace, nft);\n        } catch (error) {\n            console.error(\"加载数据失败:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const loadMyNFTs = async (nft)=>{\n        try {\n            const balance = await nft.balanceOf(account);\n            const nfts = [];\n            for(let i = 0; i < balance; i++){\n                const tokenId = await nft.tokenOfOwnerByIndex(account, i);\n                const tokenURI = await nft.tokenURI(tokenId);\n                nfts.push({\n                    tokenId: tokenId.toString(),\n                    tokenURI,\n                    owner: account\n                });\n            }\n            setMyNFTs(nfts);\n        } catch (error) {\n            console.error(\"加载 NFT 失败:\", error);\n        }\n    };\n    const loadMarketListings = async (marketplace, nft)=>{\n        try {\n            const listingIds = await marketplace.getActiveListings();\n            const listingsData = [];\n            for (const listingId of listingIds){\n                const listing = await marketplace.listings(listingId);\n                if (listing[4]) {\n                    const tokenURI = await nft.tokenURI(listing[2]);\n                    listingsData.push({\n                        id: listingId,\n                        seller: listing[0],\n                        nftContract: listing[1],\n                        tokenId: listing[2].toString(),\n                        price: ethers__WEBPACK_IMPORTED_MODULE_4__.ethers.formatEther(listing[3]),\n                        tokenURI,\n                        listingTime: new Date(Number(listing[5]) * 1000)\n                    });\n                }\n            }\n            setListings(listingsData);\n        } catch (error) {\n            console.error(\"加载市场列表失败:\", error);\n        }\n    };\n    const loadMarketAuctions = async (marketplace, nft)=>{\n        try {\n            const auctionIds = await marketplace.getActiveAuctions();\n            const auctionsData = [];\n            for (const auctionId of auctionIds){\n                const auction = await marketplace.auctions(auctionId);\n                if (auction[7] && !auction[8]) {\n                    const tokenURI = await nft.tokenURI(auction[2]);\n                    auctionsData.push({\n                        id: auctionId,\n                        seller: auction[0],\n                        nftContract: auction[1],\n                        tokenId: auction[2].toString(),\n                        startingPrice: ethers__WEBPACK_IMPORTED_MODULE_4__.ethers.formatEther(auction[3]),\n                        highestBid: ethers__WEBPACK_IMPORTED_MODULE_4__.ethers.formatEther(auction[4]),\n                        highestBidder: auction[5],\n                        endTime: new Date(Number(auction[6]) * 1000),\n                        tokenURI\n                    });\n                }\n            }\n            setAuctions(auctionsData);\n        } catch (error) {\n            console.error(\"加载拍卖失败:\", error);\n        }\n    };\n    const handleMintNFT = async ()=>{\n        if (!nftContract) return;\n        try {\n            setLoading(true);\n            const mintPrice = await nftContract.mintPrice();\n            const totalCost = mintPrice * BigInt(mintQuantity);\n            const tx = await nftContract.mint(mintQuantity, {\n                value: totalCost\n            });\n            await tx.wait();\n            alert(\"NFT 铸造成功!\");\n            loadMyNFTs(nftContract);\n        } catch (error) {\n            console.error(\"铸造失败:\", error);\n            alert(\"铸造失败: \" + error.message);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleListNFT = async ()=>{\n        if (!nftContract || !marketplaceContract || !selectedNFT || !listPrice) return;\n        try {\n            setLoading(true);\n            // 首先授权市场合约\n            const isApproved = await nftContract.isApprovedForAll(account, await marketplaceContract.getAddress());\n            if (!isApproved) {\n                const approveTx = await nftContract.setApprovalForAll(await marketplaceContract.getAddress(), true);\n                await approveTx.wait();\n            }\n            // 列出 NFT\n            const tx = await marketplaceContract.listItem(await nftContract.getAddress(), selectedNFT, ethers__WEBPACK_IMPORTED_MODULE_4__.ethers.parseEther(listPrice));\n            await tx.wait();\n            alert(\"NFT 上架成功!\");\n            loadData(nftContract, marketplaceContract);\n        } catch (error) {\n            console.error(\"上架失败:\", error);\n            alert(\"上架失败: \" + error.message);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleBuyNFT = async (listingId, price)=>{\n        if (!marketplaceContract) return;\n        try {\n            setLoading(true);\n            const tx = await marketplaceContract.buyItem(listingId, {\n                value: ethers__WEBPACK_IMPORTED_MODULE_4__.ethers.parseEther(price)\n            });\n            await tx.wait();\n            alert(\"购买成功!\");\n            loadData(nftContract, marketplaceContract);\n        } catch (error) {\n            console.error(\"购买失败:\", error);\n            alert(\"购买失败: \" + error.message);\n        } finally{\n            setLoading(false);\n        }\n    };\n    if (!isConnected) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-8 text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-3xl font-bold mb-4\",\n                    children: \"NFT 市场\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\nft-marketplace.js\",\n                    lineNumber: 248,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-600\",\n                    children: \"请先连接钱包以使用 NFT 市场功能\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\nft-marketplace.js\",\n                    lineNumber: 249,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\nft-marketplace.js\",\n            lineNumber: 247,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                    children: \"NFT 市场 - Web3 生态系统\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\nft-marketplace.js\",\n                    lineNumber: 257,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\nft-marketplace.js\",\n                lineNumber: 256,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-4xl font-bold text-center mb-8 gradient-text\",\n                        children: \"\\uD83C\\uDFA8 NFT 市场\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\nft-marketplace.js\",\n                        lineNumber: 261,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg p-1 shadow-lg\",\n                            children: [\n                                \"mint\",\n                                \"my-nfts\",\n                                \"marketplace\",\n                                \"auctions\"\n                            ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setActiveTab(tab),\n                                    className: `px-6 py-2 rounded-md font-medium transition-colors ${activeTab === tab ? \"bg-primary-600 text-white\" : \"text-gray-600 hover:text-primary-600\"}`,\n                                    children: [\n                                        tab === \"mint\" && \"铸造 NFT\",\n                                        tab === \"my-nfts\" && \"我的 NFT\",\n                                        tab === \"marketplace\" && \"市场\",\n                                        tab === \"auctions\" && \"拍卖\"\n                                    ]\n                                }, tab, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\nft-marketplace.js\",\n                                    lineNumber: 269,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\nft-marketplace.js\",\n                            lineNumber: 267,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\nft-marketplace.js\",\n                        lineNumber: 266,\n                        columnNumber: 9\n                    }, this),\n                    activeTab === \"mint\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-md mx-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-bold mb-4\",\n                                    children: \"铸造 NFT\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\nft-marketplace.js\",\n                                    lineNumber: 291,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                    children: \"数量\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\nft-marketplace.js\",\n                                                    lineNumber: 294,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    min: \"1\",\n                                                    max: \"10\",\n                                                    value: mintQuantity,\n                                                    onChange: (e)=>setMintQuantity(e.target.value),\n                                                    className: \"input-field\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\nft-marketplace.js\",\n                                                    lineNumber: 297,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\nft-marketplace.js\",\n                                            lineNumber: 293,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleMintNFT,\n                                            disabled: loading,\n                                            className: \"btn-primary w-full\",\n                                            children: loading ? \"铸造中...\" : `铸造 ${mintQuantity} 个 NFT`\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\nft-marketplace.js\",\n                                            lineNumber: 306,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\nft-marketplace.js\",\n                                    lineNumber: 292,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\nft-marketplace.js\",\n                            lineNumber: 290,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\nft-marketplace.js\",\n                        lineNumber: 289,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === \"my-nfts\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid-responsive\",\n                                children: myNFTs.map((nft)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"card\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"aspect-square bg-gray-200 rounded-lg mb-4 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-4xl\",\n                                                    children: \"\\uD83D\\uDDBC️\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\nft-marketplace.js\",\n                                                    lineNumber: 325,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\nft-marketplace.js\",\n                                                lineNumber: 324,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-bold mb-2\",\n                                                children: [\n                                                    \"NFT #\",\n                                                    nft.tokenId\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\nft-marketplace.js\",\n                                                lineNumber: 327,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                children: \"售价 (ETH)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\nft-marketplace.js\",\n                                                                lineNumber: 330,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"number\",\n                                                                step: \"0.01\",\n                                                                placeholder: \"0.1\",\n                                                                className: \"input-field\",\n                                                                onChange: (e)=>setListPrice(e.target.value)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\nft-marketplace.js\",\n                                                                lineNumber: 333,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\nft-marketplace.js\",\n                                                        lineNumber: 329,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>{\n                                                            setSelectedNFT(nft.tokenId);\n                                                            handleListNFT();\n                                                        },\n                                                        disabled: loading || !listPrice,\n                                                        className: \"btn-primary w-full text-sm\",\n                                                        children: \"上架销售\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\nft-marketplace.js\",\n                                                        lineNumber: 341,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\nft-marketplace.js\",\n                                                lineNumber: 328,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, nft.tokenId, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\nft-marketplace.js\",\n                                        lineNumber: 323,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\nft-marketplace.js\",\n                                lineNumber: 321,\n                                columnNumber: 13\n                            }, this),\n                            myNFTs.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: \"您还没有任何 NFT\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\nft-marketplace.js\",\n                                    lineNumber: 357,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\nft-marketplace.js\",\n                                lineNumber: 356,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\nft-marketplace.js\",\n                        lineNumber: 320,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === \"marketplace\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid-responsive\",\n                                children: listings.map((listing)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"card\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"aspect-square bg-gray-200 rounded-lg mb-4 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-4xl\",\n                                                    children: \"\\uD83D\\uDDBC️\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\nft-marketplace.js\",\n                                                    lineNumber: 370,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\nft-marketplace.js\",\n                                                lineNumber: 369,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-bold mb-2\",\n                                                children: [\n                                                    \"NFT #\",\n                                                    listing.tokenId\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\nft-marketplace.js\",\n                                                lineNumber: 372,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600 mb-2\",\n                                                children: [\n                                                    \"卖家: \",\n                                                    listing.seller.slice(0, 6),\n                                                    \"...\",\n                                                    listing.seller.slice(-4)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\nft-marketplace.js\",\n                                                lineNumber: 373,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-lg font-bold text-primary-600 mb-4\",\n                                                children: [\n                                                    listing.price,\n                                                    \" ETH\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\nft-marketplace.js\",\n                                                lineNumber: 376,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleBuyNFT(listing.id, listing.price),\n                                                disabled: loading || listing.seller === account,\n                                                className: \"btn-primary w-full\",\n                                                children: listing.seller === account ? \"我的商品\" : \"购买\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\nft-marketplace.js\",\n                                                lineNumber: 379,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, listing.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\nft-marketplace.js\",\n                                        lineNumber: 368,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\nft-marketplace.js\",\n                                lineNumber: 366,\n                                columnNumber: 13\n                            }, this),\n                            listings.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: \"暂无商品在售\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\nft-marketplace.js\",\n                                    lineNumber: 391,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\nft-marketplace.js\",\n                                lineNumber: 390,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\nft-marketplace.js\",\n                        lineNumber: 365,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === \"auctions\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid-responsive\",\n                                children: auctions.map((auction)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"card\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"aspect-square bg-gray-200 rounded-lg mb-4 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-4xl\",\n                                                    children: \"\\uD83D\\uDDBC️\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\nft-marketplace.js\",\n                                                    lineNumber: 404,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\nft-marketplace.js\",\n                                                lineNumber: 403,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-bold mb-2\",\n                                                children: [\n                                                    \"NFT #\",\n                                                    auction.tokenId\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\nft-marketplace.js\",\n                                                lineNumber: 406,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600 mb-2\",\n                                                children: [\n                                                    \"卖家: \",\n                                                    auction.seller.slice(0, 6),\n                                                    \"...\",\n                                                    auction.seller.slice(-4)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\nft-marketplace.js\",\n                                                lineNumber: 407,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2 mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm\",\n                                                        children: [\n                                                            \"起拍价: \",\n                                                            auction.startingPrice,\n                                                            \" ETH\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\nft-marketplace.js\",\n                                                        lineNumber: 411,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm\",\n                                                        children: [\n                                                            \"当前最高价: \",\n                                                            auction.highestBid,\n                                                            \" ETH\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\nft-marketplace.js\",\n                                                        lineNumber: 414,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm\",\n                                                        children: [\n                                                            \"结束时间: \",\n                                                            auction.endTime.toLocaleString()\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\nft-marketplace.js\",\n                                                        lineNumber: 417,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\nft-marketplace.js\",\n                                                lineNumber: 410,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                disabled: loading || auction.seller === account || new Date() > auction.endTime,\n                                                className: \"btn-primary w-full\",\n                                                children: auction.seller === account ? \"我的拍卖\" : \"出价\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\nft-marketplace.js\",\n                                                lineNumber: 421,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, auction.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\nft-marketplace.js\",\n                                        lineNumber: 402,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\nft-marketplace.js\",\n                                lineNumber: 400,\n                                columnNumber: 13\n                            }, this),\n                            auctions.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: \"暂无拍卖进行中\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\nft-marketplace.js\",\n                                    lineNumber: 432,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\nft-marketplace.js\",\n                                lineNumber: 431,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\nft-marketplace.js\",\n                        lineNumber: 399,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\nft-marketplace.js\",\n                lineNumber: 260,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/nft-marketplace.js\n");

/***/ }),

/***/ "./frontend/styles/globals.css":
/*!*************************************!*\
  !*** ./frontend/styles/globals.css ***!
  \*************************************/
/***/ (() => {



/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "next/head":
/*!****************************!*\
  !*** external "next/head" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/head");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "ethers":
/*!*************************!*\
  !*** external "ethers" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = import("ethers");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fnft-marketplace&preferredRegion=&absolutePagePath=.%2Fpages%5Cnft-marketplace.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();